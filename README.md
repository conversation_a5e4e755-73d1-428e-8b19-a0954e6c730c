# DR Bag Decoder

## Quick Start

### 1. Environment Setup

```bash
# Activate the conda environment
conda activate slam_py310

# Install required dependencies
conda install -c conda-forge ffmpeg
pip install -r requirements.txt

# Build protobuf bindings
python build_protos.py --clean

# Set Python path for proto imports
export PYTHONPATH=$(pwd)/proto_build:$PYTHONPATH
```

### 2. Process ROS Bags

```bash
# Basic processing
python process_topics.py sample_bags

# Process with video generation
python process_topics.py sample_bags --generate_video

# Limit messages for testing
python process_topics.py sample_bags --max_msgs 100 --generate_video --video_fps 20
```

### 3. View Results

```bash
# Check output structure
ls -la output/
# output/
# ├── camera1/           # Raw HEVC data (debugging)
# ├── combined/          # Fused visualization images
# ├── rasmap/           # NN output visualizations
# └── combined_video.mp4 # Generated video (if --generate_video used)
```

## Usage

### Command Line Options

```bash
python process_topics.py <bag_directory> [options]

Required:
  bag_directory         Directory containing .bag files to process

Optional:
  --max_msgs N         Maximum messages per topic (0 = unlimited)
  --out DIR            Output directory (default: output)
  --generate_video     Generate MP4 video from combined images
  --video_fps FLOAT    Video frame rate (default: 10.0)
  --video_name NAME    Output video filename (default: combined_video.mp4)
```

### Examples

```bash
# Process all bags in directory
python process_topics.py /path/to/bags

# Generate high-quality video
python process_topics.py sample_bags --generate_video --video_fps 20

# Quick test with limited messages
python process_topics.py sample_bags --max_msgs 50 --generate_video

# Custom output location
python process_topics.py sample_bags --out my_output --video_name my_video.mp4
```

## Output Structure

```
output/
├── camera1/                    # Raw HEVC data files
│   └── cam1_<timestamp>.hevc
├── combined/                   # Main output: fused visualizations
│   └── combined_<timestamp>.png
├── rasmap/                     # Neural network output visualizations
│   ├── debug_<timestamp>.png   # Debug overlays
│   └── road_<timestamp>.png    # Road mask visualizations
└── combined_video.mp4          # Generated video (if requested)
```

## Performance

### Typical Performance (macOS M4 Pro)
- **Processing Speed**: 200-300 messages/second
- **HEVC Decode Rate**: 10-15% success rate (hardware limitations)
- **Memory Usage**: 500MB-1GB peak
- **Video Generation**: ~10 seconds for 150 frames

### Input Data Streams
| Stream | Topic | Format | Frequency | Resolution |
|--------|-------|--------|-----------|------------|
| Camera | `/sensors/camera/camera_1_raw_data/compressed_proto` | HEVC | ~10 Hz | 2160×3840 |
| RasMap NN | `/perception/deeproute_perception_ras_map` | Protobuf | ~8 Hz | Variable |

## Troubleshooting

### Common Issues

**1. Proto Build Errors**
```bash
# Clean rebuild
python build_protos.py --clean

# Check protobuf version
pip install protobuf==3.20.3
```

**2. HEVC Decoding Failures**
- Expected behavior: ~10-15% success rate due to incomplete NAL units
- Hardware decoder automatically falls back to software/placeholder

**3. Missing Dependencies**
```bash
# Install missing packages
conda install -c conda-forge ffmpeg
pip install -r requirements.txt
```

**4. Python Path Issues**
```bash
# Always set before running
export PYTHONPATH=$(pwd)/proto_build:$PYTHONPATH
```

### Environment Verification

```bash
# Check conda environment
conda activate slam_py310
conda list | grep -E "(av|opencv|protobuf)"

# Test proto imports
python -c "from drivers.sensor_image_pb2 import CompressedImage; print('Proto imports OK')"

# Test HEVC decoder
python -c "from msg_processor import proto_compressed_to_cv_mat; print('HEVC decoder ready')"
```

## Development

### File Structure
```
dr_bag_decoder/
├── process_topics.py          # Main processing script
├── msg_processor.py           # Message processing and HEVC decoding
├── build_protos.py           # Protocol buffer build system
├── requirements.txt          # Python dependencies
├── design_doc.md            # Technical documentation
├── topic_of_interest.md     # Available ROS topics
├── proto/                   # Protocol buffer definitions
├── proto_build/             # Generated Python bindings
└── sample_bags/             # Sample ROS bag files
```

### Adding New Message Types
1. Add proto definitions to `proto/` directory
2. Update `build_protos.py` if needed
3. Add processing logic to `msg_processor.py`
4. Update topic configuration in `topic_of_interest.md`

## License

This project is part of the DeepRoute autonomous driving system.

## Support

For technical issues or questions, refer to the `design_doc.md` for detailed technical specifications and architecture information.
