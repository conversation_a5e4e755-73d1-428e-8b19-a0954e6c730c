#!/usr/bin/env python3
"""Utility to generate Python protobuf bindings from .proto sources.

This replaces the original CMake/Bazel logic, outputs into `proto_build/`.
It uses `grpc_tools.protoc` shipped with the `grpcio-tools` package, so no
system `protoc` installation is required.

Usage:
  python build_protos.py            # default proto dir "proto", output "proto_build"
  python build_protos.py --proto_dir proto --out_dir proto_build

Run this once after you clone/update the repo or whenever `.proto` files change.
After generation remember to set `PYTHONPATH` to include `proto_build` or run
`export PYTHONPATH=$(pwd)/proto_build:$PYTHONPATH`.
"""

from __future__ import annotations

import argparse
import logging
import shutil
import subprocess
import sys
from pathlib import Path

logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")


def ensure_grpc_tools() -> None:
    """Ensure grpc_tools is importable, otherwise guide the user to install it."""
    try:
        import grpc_tools.protoc  # noqa: F401
    except ImportError as exc:
        raise SystemExit(
            "The 'grpcio-tools' package is required. Install it via\n"
            "  pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple grpcio-tools"
        ) from exc


def collect_proto_files(proto_root: Path) -> list[Path]:
    """Return list of .proto files under *proto_root* (recursive)."""
    files = [p for p in proto_root.rglob("*.proto") if not p.match("*/.*")]
    # Optional: skip the church module (mirrors original CMake logic)
    files = [p for p in files if "church/" not in str(p)]
    return sorted(files)


def generate(proto_root: Path, out_root: Path, protos: list[Path]) -> None:
    """Invoke protoc once per file so that import paths remain correct."""
    import grpc_tools.protoc as protoc

    for proto in protos:
        rel_dir = proto.parent.relative_to(proto_root)
        target_dir = out_root / rel_dir
        target_dir.mkdir(parents=True, exist_ok=True)

        cmd = [
            "python",
            "-m",
            "grpc_tools.protoc",
            f"-I{proto_root}",
            f"--python_out={out_root}",
            str(proto),
        ]
        logging.info("protoc %s", proto.relative_to(proto_root))
        if subprocess.call(cmd) != 0:
            raise RuntimeError(f"protoc failed on {proto}")

    # Add __init__.py so Python recognizes packages.
    for d in [out_root] + [p.parent for p in out_root.rglob("*.py")]:
        init_file = d / "__init__.py"
        init_file.touch(exist_ok=True)


def main() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument("--proto_dir", default="proto", type=Path)
    parser.add_argument("--out_dir", default="proto_build", type=Path)
    parser.add_argument("--clean", action="store_true", help="Delete out_dir before generating")
    args = parser.parse_args()

    ensure_grpc_tools()

    if args.clean and args.out_dir.exists():
        shutil.rmtree(args.out_dir)

    args.out_dir.mkdir(parents=True, exist_ok=True)

    protos = collect_proto_files(args.proto_dir)
    logging.info("Found %d proto files", len(protos))
    generate(args.proto_dir, args.out_dir, protos)
    logging.info("Python proto bindings generated in %s", args.out_dir)


if __name__ == "__main__":
    main() 