#!/usr/bin/env python3
"""
List all topics in ROS bag files.

This script scans ROS bag files and prints all available topics with their message counts.
Useful for understanding the structure of bag files before processing.
"""

import argparse
import sys
from pathlib import Path
from collections import defaultdict
from rosbags.rosbag1 import Reader


def analyze_bag_file(bag_path: Path) -> dict:
    """
    Analyze a single bag file and return topic information.
    
    Args:
        bag_path: Path to the bag file
        
    Returns:
        Dictionary with topic information
    """
    topic_info = {}
    
    try:
        with Reader(bag_path) as reader:
            # Get topic information from connections
            for connection in reader.connections:
                topic_info[connection.topic] = {
                    'msgtype': connection.msgtype,
                    'msgcount': connection.msgcount,
                    'md5sum': connection.md5sum
                }
                
    except Exception as e:
        print(f"Error reading {bag_path}: {e}")
        return {}
    
    return topic_info


def print_bag_topics(bag_path: Path, show_details: bool = False):
    """Print topics for a single bag file."""
    print(f"\n{'='*60}")
    print(f"Bag file: {bag_path.name}")
    print(f"Path: {bag_path}")
    print(f"{'='*60}")
    
    topic_info = analyze_bag_file(bag_path)
    
    if not topic_info:
        print("No topics found or error reading bag file.")
        return
    
    # Sort topics by name
    sorted_topics = sorted(topic_info.items())
    
    print(f"Total topics: {len(sorted_topics)}")
    print()
    
    if show_details:
        # Detailed view with message counts and types
        print(f"{'Topic Name':<50} {'Messages':<10} {'Message Type'}")
        print(f"{'-'*50} {'-'*10} {'-'*30}")
        
        for topic, info in sorted_topics:
            print(f"{topic:<50} {info['msgcount']:<10} {info['msgtype']}")
    else:
        # Simple list view
        for i, (topic, info) in enumerate(sorted_topics, 1):
            print(f"{i:3d}. {topic} ({info['msgcount']} messages)")


def print_summary_all_bags(bag_files: list, show_details: bool = False):
    """Print a summary of all topics across all bag files."""
    print(f"\n{'='*80}")
    print("SUMMARY: All topics across all bag files")
    print(f"{'='*80}")
    
    all_topics = defaultdict(lambda: {'bags': set(), 'total_messages': 0, 'msgtype': None})
    
    for bag_path in bag_files:
        topic_info = analyze_bag_file(bag_path)
        for topic, info in topic_info.items():
            all_topics[topic]['bags'].add(bag_path.name)
            all_topics[topic]['total_messages'] += info['msgcount']
            all_topics[topic]['msgtype'] = info['msgtype']
    
    # Sort topics by name
    sorted_all_topics = sorted(all_topics.items())
    
    print(f"Unique topics across all bags: {len(sorted_all_topics)}")
    print(f"Total bag files analyzed: {len(bag_files)}")
    print()
    
    if show_details:
        print(f"{'Topic Name':<50} {'Total Msgs':<12} {'Bag Count':<10} {'Message Type'}")
        print(f"{'-'*50} {'-'*12} {'-'*10} {'-'*30}")
        
        for topic, info in sorted_all_topics:
            bag_count = len(info['bags'])
            print(f"{topic:<50} {info['total_messages']:<12} {bag_count:<10} {info['msgtype']}")
    else:
        for i, (topic, info) in enumerate(sorted_all_topics, 1):
            bag_count = len(info['bags'])
            print(f"{i:3d}. {topic}")
            print(f"     Total messages: {info['total_messages']}, Found in {bag_count} bag(s)")


def main():
    parser = argparse.ArgumentParser(
        description="List all topics in ROS bag files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List topics in all bags in a directory
  python list_bag_topics.py sample_bags

  # Show detailed information with message counts and types
  python list_bag_topics.py sample_bags --details

  # Analyze a specific bag file
  python list_bag_topics.py sample_bags/specific.bag

  # Show summary only (no individual bag details)
  python list_bag_topics.py sample_bags --summary-only

  # Export topic list to file
  python list_bag_topics.py sample_bags > topics.txt
        """
    )
    
    parser.add_argument(
        'path',
        type=Path,
        help='Path to bag file or directory containing bag files'
    )
    
    parser.add_argument(
        '--details', '-d',
        action='store_true',
        help='Show detailed information (message counts, types)'
    )
    
    parser.add_argument(
        '--summary-only', '-s',
        action='store_true',
        help='Show only the summary across all bags'
    )
    
    parser.add_argument(
        '--filter', '-f',
        type=str,
        help='Filter topics containing this string (case-insensitive)'
    )
    
    args = parser.parse_args()
    
    # Find bag files
    if args.path.is_file() and args.path.suffix == '.bag':
        bag_files = [args.path]
    elif args.path.is_dir():
        bag_files = list(args.path.glob('*.bag'))
        if not bag_files:
            print(f"No .bag files found in {args.path}")
            return 1
    else:
        print(f"Invalid path: {args.path}")
        return 1
    
    print(f"Found {len(bag_files)} bag file(s)")
    
    # Sort bag files by name
    bag_files.sort()
    
    # Process each bag file (unless summary-only)
    if not args.summary_only:
        for bag_path in bag_files:
            print_bag_topics(bag_path, args.details)
    
    # Print summary if multiple bags
    if len(bag_files) > 1:
        print_summary_all_bags(bag_files, args.details)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
