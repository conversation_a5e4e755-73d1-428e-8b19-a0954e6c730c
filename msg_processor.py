# 
import struct
from drivers.gnss.ins_pb2 import SensorsIns
from map.sd_map_pb2 import SDMapOnboard, SDMap
from perception.perception_nn_pb2 import NnFrame
from common.geometry_pb2 import PointLLH, Point2D
from lock_on_road.lock_on_road_pb2 import LockOnRoadResult
from drivers.sensor_image_pb2 import CompressedImage

rasmap_topic = "/perception/ras_map_nn"
sdmap_topic = "/map/sd_map"
pose_topic = "/sensors/gnss/pose"
camera_1_topic = "/sensors/camera/camera_1_raw_data/compressed_proto"

def proto_compressed_to_cv_mat(proto_data):
    """Decode compressed image data from protobuf message.

    Optimized for macOS M4 Pro with hardware acceleration.
    """
    format = proto_data.format
    data = proto_data.data

    if format.lower() == 'hevc':
        """Decode HEVC bit-stream with optimized hardware acceleration."""

        # Skip very small frames that are likely corrupted
        if len(data) < 1000:
            return None

        # --- Optimized PyAV path for macOS M4 Pro ---
        try:
            import platform, av, numpy as _np, cv2

            # Create container from bytes
            container = av.open(av.BytesIO(data), format='hevc')

            # Get video stream
            video_stream = container.streams.video[0]

            # Try to decode frames
            frames = []
            for frame in container.decode(video_stream):
                frames.append(frame)
                break  # Only need first frame

            if frames:
                # Convert first frame to BGR format for OpenCV
                frame = frames[0]
                return frame.to_ndarray(format='bgr24')

        except Exception as av_exc:
            # Try alternative PyAV approach with codec context
            try:
                import platform, av, numpy as _np, cv2

                # Try hardware decoder first on macOS
                if platform.system() == 'Darwin':
                    try:
                        # Use VideoToolbox hardware decoder for M4 Pro
                        codec = av.CodecContext.create('hevc_videotoolbox', 'r')
                    except Exception:
                        # Fallback to software decoder
                        codec = av.CodecContext.create('hevc', 'r')
                else:
                    codec = av.CodecContext.create('hevc', 'r')

                # Decode frames
                frames = []
                for packet in codec.parse(data):
                    frames.extend(codec.decode(packet))

                # Flush decoder
                frames.extend(codec.decode())

                if frames:
                    # Convert first frame to BGR format for OpenCV
                    frame = frames[0]
                    return frame.to_ndarray(format='bgr24')

            except Exception:
                pass

        # --- Fallback: ffmpeg CLI (if available) ---
        try:
            import tempfile, subprocess, os

            # Only process larger frames for ffmpeg
            if len(data) < 50000:
                return None

            with tempfile.NamedTemporaryFile(delete=False, suffix='.h265') as tmp_in:
                tmp_in.write(data)
                tmp_in_path = tmp_in.name

            tmp_out_path = tmp_in_path + '.png'

            # Use hardware acceleration if available on macOS
            if platform.system() == 'Darwin':
                cmd = ['ffmpeg', '-y', '-loglevel', 'error',
                       '-hwaccel', 'videotoolbox',
                       '-i', tmp_in_path, '-vframes', '1', '-f', 'image2', tmp_out_path]
            else:
                cmd = ['ffmpeg', '-y', '-loglevel', 'error', '-i', tmp_in_path, '-vframes', '1', tmp_out_path]

            result = subprocess.run(cmd, capture_output=True, timeout=10)

            if result.returncode == 0 and os.path.exists(tmp_out_path):
                img = cv2.imread(tmp_out_path)
            else:
                img = None

            # Cleanup
            try:
                os.remove(tmp_in_path)
                if os.path.exists(tmp_out_path):
                    os.remove(tmp_out_path)
            except Exception:
                pass

            return img

        except Exception:
            pass

        # --- Last resort: Create a placeholder image ---
        try:
            import numpy as np, cv2
            # Create a simple test image (blue rectangle)
            test_img = np.zeros((480, 640, 3), dtype=np.uint8)
            test_img[:, :] = [255, 0, 0]  # Blue in BGR
            cv2.putText(test_img, 'HEVC DECODE PLACEHOLDER', (50, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            return test_img
        except Exception:
            pass

        return None
    
    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decoded = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)
    # print(f"Decoded image shape: {img_decoded.shape if img_decoded is not None else 'None'}")

    return img_decoded

def strip_header(string_data):
    if len(string_data) < 4:
        return string_data
    if (
        string_data[0] != 36
        or string_data[1] != 36
        or string_data[2] != 36
        or string_data[3] != 36
    ):
        # without header
        return string_data
    else:
        # with header
        prefix_size = 4
        header_size_field_size = 4
        info_size = prefix_size + header_size_field_size
        header_size = struct.unpack(
            "I", string_data[prefix_size : prefix_size + header_size_field_size]
        )[0]
        return string_data[info_size + header_size :]

# for rasmap nn
def process_rasmap_nn(rasmap_nn_msg, layer_names=["stopline_curb_confidence", "boundary_crosswalk_confidence", "centerline_confidence", "roadmask_confidence"]):
    
    print(f"Start processing rasmap_nn_msg with {len(rasmap_nn_msg.nn)} layers")
    
    frame = rasmap_nn_msg
    
    layer_dict_for_vis = dict()
    layer_dict_for_use = dict()
    
    # print(f"frame timestamp: {frame.timestamp}, layer names {[layer.name for layer in frame.nn]}")
    SCALE = 32.0
    for layer in frame.nn:
        if layer.name not in layer_names:
            continue
        layer_data = snappy.uncompress(layer.nn_data)
        layer_mat = (
            np.array([item for item in layer_data], dtype=float).reshape(layer.shape)
            / SCALE
        )
        
        FACTOR=255
        if layer_mat.shape[1] == 1:
            layer_dict_for_vis[layer.name] = (layer_mat[0, 0, :, :] * FACTOR).astype("uint8")
        else:
            layer_dict_for_vis[layer.name] = (layer_mat[0, 1, :, :] * FACTOR).astype("uint8")
        
        layer_dict_for_use[layer.name] = layer_mat[0, :, :, :]
    
    CURB_IMG = layer_dict_for_vis.get("stopline_curb_confidence", None)
    BOUNDARY_IMG = layer_dict_for_vis.get("boundary_crosswalk_confidence", None)
    CENTERLINE_IMG = layer_dict_for_vis.get("centerline_confidence", None)
    ROADMASK_IMG = layer_dict_for_vis.get("roadmask_confidence", None)
    
    debug_img_list = []
    debug_img_list.append(BOUNDARY_IMG)
    debug_img_list.append(CENTERLINE_IMG)
    debug_img_list.append(CURB_IMG)
    debug_img = cv2.merge(debug_img_list)
    
    ROADMASK_IMG = cv2.cvtColor(ROADMASK_IMG, cv2.COLOR_GRAY2BGR)
    
    # vertically flip debug_img and roadmask_img
    debug_img = cv2.flip(debug_img, 0)
    ROADMASK_IMG = cv2.flip(ROADMASK_IMG, 0)

    return debug_img, ROADMASK_IMG, layer_dict_for_use 