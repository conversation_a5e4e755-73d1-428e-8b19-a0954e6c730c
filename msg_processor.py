#
import struct
import math
import j<PERSON>
from typing import List, Dict, Any, Tu<PERSON>
from drivers.gnss.ins_pb2 import SensorsIns
from map.sd_map_pb2 import SDMapOnboard, SDMap
from perception.perception_nn_pb2 import Nn<PERSON>rame
from common.geometry_pb2 import PointLLH, Point2D
from lock_on_road.lock_on_road_pb2 import LockOnRoadResult
from drivers.sensor_image_pb2 import CompressedImage

rasmap_topic = "/perception/ras_map_nn"
sdmap_topic = "/map/sd_map"
pose_topic = "/sensors/gnss/pose"
gnss_pose_topic = "/sensors/gnss/pose"
camera_1_topic = "/sensors/camera/camera_1_raw_data/compressed_proto"

def proto_compressed_to_cv_mat(proto_data):
    """Decode compressed image data from protobuf message.

    Optimized for macOS M4 Pro with hardware acceleration.
    """
    format = proto_data.format
    data = proto_data.data

    if format.lower() == 'hevc':
        """Decode HEVC bit-stream with optimized hardware acceleration."""

        # Skip very small frames that are likely corrupted
        if len(data) < 1000:
            return None

        # --- Optimized PyAV path for macOS M4 Pro ---
        try:
            import platform, av, numpy as _np, cv2

            # Create container from bytes
            container = av.open(av.BytesIO(data), format='hevc')

            # Get video stream
            video_stream = container.streams.video[0]

            # Try to decode frames
            frames = []
            for frame in container.decode(video_stream):
                frames.append(frame)
                break  # Only need first frame

            if frames:
                # Convert first frame to BGR format for OpenCV
                frame = frames[0]
                return frame.to_ndarray(format='bgr24')

        except Exception as av_exc:
            # Try alternative PyAV approach with codec context
            try:
                import platform, av, numpy as _np, cv2

                # Try hardware decoder first on macOS
                if platform.system() == 'Darwin':
                    try:
                        # Use VideoToolbox hardware decoder for M4 Pro
                        codec = av.CodecContext.create('hevc_videotoolbox', 'r')
                    except Exception:
                        # Fallback to software decoder
                        codec = av.CodecContext.create('hevc', 'r')
                else:
                    codec = av.CodecContext.create('hevc', 'r')

                # Decode frames
                frames = []
                for packet in codec.parse(data):
                    frames.extend(codec.decode(packet))

                # Flush decoder
                frames.extend(codec.decode())

                if frames:
                    # Convert first frame to BGR format for OpenCV
                    frame = frames[0]
                    return frame.to_ndarray(format='bgr24')

            except Exception:
                pass

        # --- Fallback: ffmpeg CLI (if available) ---
        try:
            import tempfile, subprocess, os

            # Only process larger frames for ffmpeg
            if len(data) < 50000:
                return None

            with tempfile.NamedTemporaryFile(delete=False, suffix='.h265') as tmp_in:
                tmp_in.write(data)
                tmp_in_path = tmp_in.name

            tmp_out_path = tmp_in_path + '.png'

            # Use hardware acceleration if available on macOS
            if platform.system() == 'Darwin':
                cmd = ['ffmpeg', '-y', '-loglevel', 'error',
                       '-hwaccel', 'videotoolbox',
                       '-i', tmp_in_path, '-vframes', '1', '-f', 'image2', tmp_out_path]
            else:
                cmd = ['ffmpeg', '-y', '-loglevel', 'error', '-i', tmp_in_path, '-vframes', '1', tmp_out_path]

            result = subprocess.run(cmd, capture_output=True, timeout=10)

            if result.returncode == 0 and os.path.exists(tmp_out_path):
                img = cv2.imread(tmp_out_path)
            else:
                img = None

            # Cleanup
            try:
                os.remove(tmp_in_path)
                if os.path.exists(tmp_out_path):
                    os.remove(tmp_out_path)
            except Exception:
                pass

            return img

        except Exception:
            pass

        # --- Last resort: Create a placeholder image ---
        try:
            import numpy as np, cv2
            # Create a simple test image (blue rectangle)
            test_img = np.zeros((480, 640, 3), dtype=np.uint8)
            test_img[:, :] = [255, 0, 0]  # Blue in BGR
            cv2.putText(test_img, 'HEVC DECODE PLACEHOLDER', (50, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            return test_img
        except Exception:
            pass

        return None
    
    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decoded = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)
    # print(f"Decoded image shape: {img_decoded.shape if img_decoded is not None else 'None'}")

    return img_decoded

def strip_header(string_data):
    if len(string_data) < 4:
        return string_data
    if (
        string_data[0] != 36
        or string_data[1] != 36
        or string_data[2] != 36
        or string_data[3] != 36
    ):
        # without header
        return string_data
    else:
        # with header
        prefix_size = 4
        header_size_field_size = 4
        info_size = prefix_size + header_size_field_size
        header_size = struct.unpack(
            "I", string_data[prefix_size : prefix_size + header_size_field_size]
        )[0]
        return string_data[info_size + header_size :]

# for rasmap nn
def process_rasmap_nn(rasmap_nn_msg, layer_names=["stopline_curb_confidence", "boundary_crosswalk_confidence", "centerline_confidence", "roadmask_confidence"]):
    
    print(f"Start processing rasmap_nn_msg with {len(rasmap_nn_msg.nn)} layers")
    
    frame = rasmap_nn_msg
    
    layer_dict_for_vis = dict()
    layer_dict_for_use = dict()
    
    # print(f"frame timestamp: {frame.timestamp}, layer names {[layer.name for layer in frame.nn]}")
    SCALE = 32.0
    for layer in frame.nn:
        if layer.name not in layer_names:
            continue
        layer_data = snappy.uncompress(layer.nn_data)
        layer_mat = (
            np.array([item for item in layer_data], dtype=float).reshape(layer.shape)
            / SCALE
        )
        
        FACTOR=255
        if layer_mat.shape[1] == 1:
            layer_dict_for_vis[layer.name] = (layer_mat[0, 0, :, :] * FACTOR).astype("uint8")
        else:
            layer_dict_for_vis[layer.name] = (layer_mat[0, 1, :, :] * FACTOR).astype("uint8")
        
        layer_dict_for_use[layer.name] = layer_mat[0, :, :, :]
    
    CURB_IMG = layer_dict_for_vis.get("stopline_curb_confidence", None)
    BOUNDARY_IMG = layer_dict_for_vis.get("boundary_crosswalk_confidence", None)
    CENTERLINE_IMG = layer_dict_for_vis.get("centerline_confidence", None)
    ROADMASK_IMG = layer_dict_for_vis.get("roadmask_confidence", None)
    
    debug_img_list = []
    debug_img_list.append(BOUNDARY_IMG)
    debug_img_list.append(CENTERLINE_IMG)
    debug_img_list.append(CURB_IMG)
    debug_img = cv2.merge(debug_img_list)
    
    ROADMASK_IMG = cv2.cvtColor(ROADMASK_IMG, cv2.COLOR_GRAY2BGR)
    
    # vertically flip debug_img and roadmask_img
    debug_img = cv2.flip(debug_img, 0)
    ROADMASK_IMG = cv2.flip(ROADMASK_IMG, 0)

    return debug_img, ROADMASK_IMG, layer_dict_for_use


# ---------------------------------------------------------------------------
# Coordinate conversion utilities
# ---------------------------------------------------------------------------

def wgs84_to_gcj02(lon: float, lat: float) -> Tuple[float, float]:
    """Convert WGS84 coordinates to GCJ02 (Chinese coordinate system).

    Args:
        lon: Longitude in WGS84 (degrees)
        lat: Latitude in WGS84 (degrees)

    Returns:
        Tuple of (longitude, latitude) in GCJ02 (degrees)
    """
    # Constants for GCJ02 transformation
    a = 6378245.0  # Semi-major axis
    ee = 0.00669342162296594323  # Eccentricity squared

    def transform_lat(x: float, y: float) -> float:
        ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * math.sqrt(abs(x))
        ret += (20.0 * math.sin(6.0 * x * math.pi) + 20.0 * math.sin(2.0 * x * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(y * math.pi) + 40.0 * math.sin(y / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (160.0 * math.sin(y / 12.0 * math.pi) + 320 * math.sin(y * math.pi / 30.0)) * 2.0 / 3.0
        return ret

    def transform_lon(x: float, y: float) -> float:
        ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * math.sqrt(abs(x))
        ret += (20.0 * math.sin(6.0 * x * math.pi) + 20.0 * math.sin(2.0 * x * math.pi)) * 2.0 / 3.0
        ret += (20.0 * math.sin(x * math.pi) + 40.0 * math.sin(x / 3.0 * math.pi)) * 2.0 / 3.0
        ret += (150.0 * math.sin(x / 12.0 * math.pi) + 300.0 * math.sin(x / 30.0 * math.pi)) * 2.0 / 3.0
        return ret

    # Check if coordinates are in China (rough bounds)
    if not (72.004 <= lon <= 137.8347 and 0.8293 <= lat <= 55.8271):
        return lon, lat

    dlat = transform_lat(lon - 105.0, lat - 35.0)
    dlon = transform_lon(lon - 105.0, lat - 35.0)

    radlat = lat / 180.0 * math.pi
    magic = math.sin(radlat)
    magic = 1 - ee * magic * magic
    sqrtmagic = math.sqrt(magic)
    dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * math.pi)
    dlon = (dlon * 180.0) / (a / sqrtmagic * math.cos(radlat) * math.pi)

    mglat = lat + dlat
    mglon = lon + dlon

    return mglon, mglat


def save_gnss_to_geojson(gnss_data: List[Dict[str, Any]], output_path: str) -> None:
    """Save GNSS data to GeoJSON format.

    Args:
        gnss_data: List of GNSS data dictionaries containing coordinates and properties
        output_path: Path to save the GeoJSON file
    """
    features = []

    for data in gnss_data:
        feature = {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [data["longitude_gcj02"], data["latitude_gcj02"], data.get("height", 0.0)]
            },
            "properties": {
                "timestamp": data["timestamp"],
                "longitude_wgs84": data["longitude_wgs84"],
                "latitude_wgs84": data["latitude_wgs84"],
                "longitude_gcj02": data["longitude_gcj02"],
                "latitude_gcj02": data["latitude_gcj02"],
                "height": data.get("height", 0.0),
                "euler_angles": data.get("euler_angles", {}),
                "measurement_time": data.get("measurement_time")
            }
        }
        features.append(feature)

    geojson = {
        "type": "FeatureCollection",
        "features": features
    }

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(geojson, f, indent=2, ensure_ascii=False)

    print(f"Saved {len(features)} GNSS points to {output_path}")


def process_gnss_message(sensors_ins_msg, timestamp: str) -> Dict[str, Any]:
    """Process a GNSS SensorsIns message and extract relevant data.

    Args:
        sensors_ins_msg: Parsed SensorsIns protobuf message
        timestamp: ROS timestamp string

    Returns:
        Dictionary containing processed GNSS data
    """
    # Extract position data - check both possible fields
    position_llh = None
    if sensors_ins_msg.HasField("imu_frame_position_llh"):
        position_llh = sensors_ins_msg.imu_frame_position_llh
    elif sensors_ins_msg.HasField("vehicle_frame_position_llh"):
        position_llh = sensors_ins_msg.vehicle_frame_position_llh

    if position_llh is None:
        return None

    # Extract WGS84 coordinates
    lon_wgs84 = position_llh.lon
    lat_wgs84 = position_llh.lat
    height = position_llh.height

    # Convert to GCJ02
    lon_gcj02, lat_gcj02 = wgs84_to_gcj02(lon_wgs84, lat_wgs84)

    # Extract euler angles (roll/pitch/azimuth in degrees)
    euler_angles = {}
    if hasattr(sensors_ins_msg, 'roll_pitch_azimuth') and sensors_ins_msg.HasField('roll_pitch_azimuth'):
        rpa = sensors_ins_msg.roll_pitch_azimuth
        euler_angles = {
            "roll": rpa.x,    # degrees
            "pitch": rpa.y,   # degrees
            "azimuth": rpa.z  # degrees (azimuth)
        }

    # Extract measurement time
    measurement_time = None
    if hasattr(sensors_ins_msg, 'measurement_time'):
        measurement_time = sensors_ins_msg.measurement_time

    return {
        "timestamp": timestamp,
        "longitude_wgs84": lon_wgs84,
        "latitude_wgs84": lat_wgs84,
        "longitude_gcj02": lon_gcj02,
        "latitude_gcj02": lat_gcj02,
        "height": height,
        "euler_angles": euler_angles,
        "measurement_time": measurement_time
    }