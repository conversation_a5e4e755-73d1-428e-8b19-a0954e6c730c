package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "dem_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["dem_event.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dem_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dem_event_proto",
    ],
)

python_proto_library(
    name = "dem_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dem_event_proto",
    ],
)

proto_library(
    name = "dem_heartbeat_proto",
    visibility = ["//visibility:public"],
    srcs = ["dem_heartbeat.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dem_heartbeat_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dem_heartbeat_proto",
    ],
)

python_proto_library(
    name = "dem_heartbeat_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dem_heartbeat_proto",
    ],
)

proto_library(
    name = "store_trip_data_proto",
    visibility = ["//visibility:public"],
    srcs = ["store_trip_data.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "store_trip_data_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":store_trip_data_proto",
    ],
)

python_proto_library(
    name = "store_trip_data_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":store_trip_data_proto",
    ],
)

proto_library(
    name = "function_group_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["function_group_state.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "function_group_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":function_group_state_proto",
    ],
)

python_proto_library(
    name = "function_group_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":function_group_state_proto",
    ],
)

proto_library(
    name = "dem_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["dem_command.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dem_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dem_command_proto",
    ],
)

python_proto_library(
    name = "dem_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dem_command_proto",
    ],
)

proto_library(
    name = "execution_process_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["execution_process_state.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "execution_process_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":execution_process_state_proto",
    ],
)

python_proto_library(
    name = "execution_process_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":execution_process_state_proto",
    ],
)

proto_library(
    name = "execution_management_proto",
    visibility = ["//visibility:public"],
    srcs = ["execution_management.proto"],
    deps = [
        "//proto/dem:function_group_state_proto",
        "//proto/dem:execution_process_state_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "execution_management_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":execution_management_proto",
    ],
)

python_proto_library(
    name = "execution_management_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":execution_management_proto",
    ],
)

