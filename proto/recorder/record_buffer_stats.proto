syntax = "proto2";

package deeproute.recorder;

message RecordBufferStats {

  /**
   @~english the earliest of all messages' timestamp, in nanoseconds
   @~chinese 所有缓存消息中最早的时间戳，单位是ns
   **/
  optional uint64 min_msg_time = 1;

  /**
   @~english the latest of all messages' timestamp, in nanoseconds
   @~chinese 所有缓存消息中最晚的时间戳，单位是ns
   **/
  optional uint64 max_msg_time = 2;

  /**
   @~english count of all cached message
   @~chinese 缓存消息数量
   **/
  optional uint64 msg_count = 3;
}

