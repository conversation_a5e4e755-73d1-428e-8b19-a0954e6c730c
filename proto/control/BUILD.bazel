package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "pad_msg_proto",
    visibility = ["//visibility:public"],
    srcs = ["pad_msg.proto"],
    deps = [
        "//proto/common:header_proto",
        "//proto/canbus:chassis_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "pad_msg_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":pad_msg_proto",
    ],
)

python_proto_library(
    name = "pad_msg_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":pad_msg_proto",
    ],
)

proto_library(
    name = "control_cmd_proto",
    visibility = ["//visibility:public"],
    srcs = ["control_cmd.proto"],
    deps = [
        "//proto/canbus:chassis_proto",
        "//proto/common:header_proto",
        "//proto/common:vehicle_signal_proto",
        "//proto/common:drive_state_proto",
        "//proto/control:pad_msg_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "control_cmd_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":control_cmd_proto",
    ],
)

python_proto_library(
    name = "control_cmd_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":control_cmd_proto",
    ],
)

