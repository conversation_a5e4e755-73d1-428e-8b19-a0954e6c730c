syntax = "proto2";
package deeproute.control;

import "common/header.proto";
import "canbus/chassis.proto";

enum DrivingAction {
  STOP = 0;
  START = 1;
  RESET = 2;
};

message PadMessage {
  // control mode, set mode according to low level definition
  optional deeproute.common.Header header = 1;

  // send driving mode to drive
  optional deeproute.canbus.Chassis.DrivingMode driving_mode = 2;

  // action in the driving_mode
  optional DrivingAction action = 3;
}
