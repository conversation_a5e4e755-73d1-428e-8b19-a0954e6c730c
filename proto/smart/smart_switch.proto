syntax = "proto3";
package smart;

import "smart/smart_common.proto";

message SmartSettingSwitch{
  //LCC/NSP切换软开关 flexray 回显proto
  // SwitchStatus lcc_nsp_switch = 1[deprecated = true];
  //ALC 软开关 记忆和设置一样
  SwitchStatus alc_switch = 2;
  //NSP 软开关 记忆和设置一样
  SwitchStatus nsp_switch = 3;
  //NSP 变道确认功能开启/关闭 记忆和设置一样
  SwitchStatus change_lane_switch = 4;
  //用户选择NSP道路限速偏移模式 记忆和设置一样
  SpeedLimitOffsetOption offset_option = 5;
  //用户设置NSP道路限速偏移量-20到20% 记忆和设置一样
  int32 speed_limit_offset = 6;
  //变道风格 记忆和设置一样
  LaneChangeStyle change_style = 7;
  //手动调节巡航车速
  // SetCruiseSpdLevel set_sed_level = 8[deprecated = true];
  //手动调节跟车时距 收flexray 回显proto 记忆和设置不同 这里只有记忆
  SetFollowTimeGapLevel set_follow_time_gap_level = 9;
  //记忆泊车开关  记忆和设置一样
  SwitchStatus lp_switch = 10;
  //记忆泊车推送开关  记忆和设置一样
  SwitchStatus lp_route_push_switch = 11;
  //按resume按钮时需要判断目前是acc还是polit模式
  MemoryAccNspState mem_acc_nsp_state = 12;
  //记忆车速循环 增加记忆信号（本次上电循环内记忆）往SOC2发的是仪表车速
  float mem_target_speed = 13;
}
//用于回显的设置按钮状态
message SmartSettingSwitch2DHU{
  //LCC/NSP切换软开关 flexray
  // 回显监听状态机变化
  // SwitchStatus lcc_nsp_switch = 1[deprecated = true];
  //ALC 软开关
  SwitchStatus alc_switch = 2;
  //NSP 软开关
  SwitchStatus nsp_switch = 3;
  //NSP 变道确认功能开启/关闭
  SwitchStatus change_lane_switch = 4;
  //用户选择NSP道路限速偏移模式
  SpeedLimitOffsetOption offset_option = 5;
  //用户设置NSP道路限速偏移量-20到20%
  int32 speed_limit_offset = 6;
  //变道风格
  LaneChangeStyle change_style = 7;
  //手动调节跟车时距 收flexray 回显proto
  SetFollowTimeGapLevel set_follow_time_gap_level = 8;
  //记忆泊车开关
  SwitchStatus lp_switch = 9;
  //记忆泊车推送开关
  SwitchStatus lp_route_push_switch = 10;
}

message DrivingSmartSwitch {
  //变道确认框
  SwitchStatus change_lane_confirm = 11;
}

enum SpeedLimitOffsetOption {
  SpeedLimitOffsetOptionInvalid = 0;
  //相对值
  SpeedLimitOffsetOptionRelative = 1;
  //绝对值
  SpeedLimitOffsetOptionAbsolute = 2;
  SpeedLimitOffsetOptionDisable = 3;
}

enum LaneChangeStyle {
  LaneChangeStyleInvalid = 0;
  //关闭
  LaneChangeStyleOff = 1;
  //平稳
  LaneChangeStyleSmooth = 2;
  //激进
  LaneChangeStyleRadical = 3;
  LaneChangeStyleDisable = 4;
  LaneChangeStyleReserve = 5;
}

enum SetCruiseSpdLevel {
  SetCruiseSpdLevelNeutral = 0;
  //+1
  SetCruiseSpdLevelPosMod1 = 1;
  //+5
  SetCruiseSpdLevelPosMod2 = 2;
  //-1
  SetCruiseSpdLevelNegMod1 = 3;
  //-5
  SetCruiseSpdLevelNegMod2 = 4;
}

enum SetFollowTimeGapLevel {
  SetFollowTimeGapLevelNoRequest = 0;
  SetFollowTimeGapLevel1 = 1;
  SetFollowTimeGapLevel2 = 2;
  SetFollowTimeGapLevel3 = 3;
}
enum MemoryAccNspState {
  MemoryAccNspStateNoRequest = 0;
  MemoryAccNspStatePolit = 1;
  MemoryAccNspStateAcc = 2;
}

message ParkingSmartSwitch {
  //记忆泊车按钮状态
  LpFeatureBtnState lp_feature_btn = 1;             
  //记忆泊车学习按钮状态
  //LpFeatureStateInf. LpStartPathSeN
  ButtonState lp_learning_btn = 2;
  //学习自动泊车按键
  // LpFeatureStateInf. LpAutoParkSeN
  ButtonState lp_learning_park_in_btn = 3;
  //学习完成按钮
  //LpFeatureStateInf. LpCompleteLearnSeN          
  //巡航开始泊车按钮
  ButtonState lp_learning_complete_btn = 4;
  // LpFeatureStateInf.LpStartSeN Standby页面开始记忆泊车按键
  ButtonState lp_start_btn = 5;
  //FeatureStateInf. StartParkButtonStateSeN
  // 巡航过程中车位被占用用户选择其他车位可泊的开始泊车按键
  ButtonState lp_parking_park_in_btn = 6;
  //FeatureStateInf. ParkContinueButtonSeN
  //巡航过程中的suspend发生后的继续按键  车门开启等外界因素, APA同信号
  ButtonState lp_parking_resume_btn = 7;
  //FeatureStateInf. SuspendTimeSeN
  //记忆泊车暂停后倒计时, APA同信号
  float lp_parking_suspend_time = 8;
}

enum LpFeatureBtnState {
  LpFeatureBtnStateDefault = 0;

  //记忆泊车按键高亮状态（功能可激活）
  //LpFeatureStateInf. LpRecoverSeN = 0x01 LpHighlight
  LpFeatureBtnStateHighLight = 1;
  //记忆泊车按键正常状态（应用可拉起）
  //LpFeatureStateInf. LpRecoverSeN = 0x02 LpNormal
  LpFeatureBtnStateNormal = 2;
  //高亮状态的恢复记忆泊车按键
  //LpFeatureStateInf. LpRecoverSeN = 0x03 LpRecoveHighlight
  LpFeatureBtnStateRecoverHighLight = 3;
   //记忆泊车按键置灰状态（功能disable）
  //LpFeatureStateInf. LpRecoverSeN = 0x04 LpGrey
  LpFeatureBtnStateGray = 4;
  
}


