package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "smart_navigation_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_navigation.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_navigation_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_navigation_proto",
    ],
)

python_proto_library(
    name = "smart_navigation_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_navigation_proto",
    ],
)

proto_library(
    name = "smart_odd_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_odd.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_odd_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_odd_proto",
    ],
)

python_proto_library(
    name = "smart_odd_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_odd_proto",
    ],
)

