package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "smart_parking_frame_data_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_parking_frame_data.proto"],
    deps = [
        "//proto/smart/parking:smart_parking_common_proto",
        "//proto/smart:smart_business_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_parking_frame_data_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_parking_frame_data_proto",
    ],
)

python_proto_library(
    name = "smart_parking_frame_data_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_parking_frame_data_proto",
    ],
)

proto_library(
    name = "smart_parking_common_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_parking_common.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_parking_common_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_parking_common_proto",
    ],
)

python_proto_library(
    name = "smart_parking_common_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_parking_common_proto",
    ],
)

proto_library(
    name = "smart_lp_parking_map_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_lp_parking_map.proto"],
    deps = [
        "//proto/smart/parking:smart_parking_common_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_lp_parking_map_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_lp_parking_map_proto",
    ],
)

python_proto_library(
    name = "smart_lp_parking_map_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_lp_parking_map_proto",
    ],
)

proto_library(
    name = "smart_parking_dwm_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_parking_dwm.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_parking_dwm_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_parking_dwm_proto",
    ],
)

python_proto_library(
    name = "smart_parking_dwm_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_parking_dwm_proto",
    ],
)

