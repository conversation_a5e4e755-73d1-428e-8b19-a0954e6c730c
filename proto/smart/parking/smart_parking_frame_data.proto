syntax = "proto3";
package smart.parking;
import "smart/parking/smart_parking_common.proto";
import "smart/smart_business.proto";

message ParkingFrameData {
  double timestamp = 1; // 时间戳
  LPMapping lp_mapping = 2; // LP学习 过程中的还原世界
  LPCruise lp_cruise = 3; //  LP巡航 过程中的还原世界
  APA apa = 4; // LP standby 过程中的还原世界
  //LpStateType
  LpStateType lp_state_type = 5;
}

message APA{
  float left_path_distance = 1; // 剩余泊车距离 (单位m,精确到小数点后1位), 支持 APA
  PAS pas = 2; // 雷达障碍物位置
  repeated ParkingSpace parking_space = 3 ; // 停车位（局部坐标）
  repeated ObjectDetectionObstacle obstacles = 4 ; // 障碍物识别
  repeated Point planning_trajectory = 5; // 规划轨迹 (自车坐标 轨迹点集)
  Location location = 6; // 车辆所在的位置（在全局路径上的位置）
}

message LPMapping{
  Location location = 1; // 车辆所在的位置（在全局路径上的位置）
  PAS pas = 2; // 雷达障碍物位置
  //LpPlanningTrajectoryInf. LpParkInTrajAlDistanceSeN
  int32 total_learn_distance = 3; // 建图阶段，已学习的距离 (单位m，精确到整数位)
  int32 road_bump_times = 4; // 通过减速带次数
  repeated ParkingSpace parking_space = 5; // 车位的占用状态
  repeated ObjectDetectionObstacle obstacles = 6 ; // 障碍物识别
  // LpPlanningTrajectoryInf. LpPathPointsSeN
  repeated Point mapping_trajectory = 7; // 建图过程中，车走过的轨迹
  float left_path_distance = 8; // 剩余泊车距离 (单位m,精确到小数点后1位), APA建图
  repeated Point planning_trajectory = 9; // APA泊车过程中，规划的泊车轨迹 (自车坐标 轨迹点集 自车坐标系)
  //保存地图进度
  float mapping_save_progress = 10;
  float parking_total_path_distance = 11; // 每次档位变化内规划总距离 (单位m,精确到小数点后1位), APA建图
  float max_learn_distance = 12;// 最大学习距离
  TrajectoryType trajectory_type = 13; // 轨迹类型(上坡，下坡，平楼层)
}

message LPCruise{
  Location location = 1; // 车辆所在的位置（在全局路径上的位置）
  PAS pas = 2; // 雷达障碍物位置
  //LpPathInfoInf. PathSurplusDistanceSeN
  int32 distance_left = 3; // 巡航阶段剩余距离 0-3.40E+38(单位m，精确到整数位)
  //LpPathInfoInf.LpDistanceSeN
  int32 odm = 4; // 巡航阶段，本次使用过程中已经走过的距离
  int32 around_person_times = 5; // 巡航阶段，本次使用过程中避让行人次数
  int32 around_obstacle_times = 6; // 巡航阶段，本次使用过程中绕障次数, 根据 PRD 这里专指绕开车辆
  //PercepFusionObjsInf. Object
  repeated ObjectDetectionObstacle  obstacles = 7; // 障碍物信息
  //PlanningTractoryInf. PathPointsSeN
  repeated Point planning_trajectory = 8; // 规划轨迹 (自车坐标 轨迹点集)
  int32 is_display_the_upstairs_and_downstairs_page = 9; // 0代表平地 1代表上坡 2代表下坡
  //PerceptionAPASlotsInf. APASlots
  repeated ParkingSpace parking_space = 10;  // 分为两个阶段：巡航阶段只使用“车位ID、车位的占用状态、number”三个信息。泊车阶段使用全部的信息。
  int32 road_bump_times = 11; // 通过减速带次数（预留）
  float left_path_distance = 12; // 剩余泊车距离 (单位m,精确到小数点后1位), APA建图
  //总耗时LpPathInfoInf.LpTimeSeN
  float total_time = 13;
  float parking_total_path_distance = 14; // 每次档位变化规划总距离 (单位m,精确到小数点后1位), APA建图
  TrajectoryType trajectory_type = 15; // 轨迹类型(上坡，下坡，平楼层)
}