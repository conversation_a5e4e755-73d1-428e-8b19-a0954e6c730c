syntax = "proto3";
package smart;

enum DrivingDwmUID {
  DRIVING_DWM_DEFAULT_ID = 0;
  DRIVING_DWM_20001 = 20001;
  DRIVING_DWM_20002 = 20002;
  DRIVING_DWM_20004 = 20004;
  DRIVING_DWM_20007 = 20007;
  DRIVING_DWM_20008 = 20008;
  DRIVING_DWM_20009 = 20009;
  DRIVING_DWM_20013 = 20013;
  DRIVING_DWM_20016 = 20016;
  DRIVING_DWM_20018 = 20018;
  DRIVING_DWM_20021 = 20021;
  DRIVING_DWM_20024 = 20024;
  DRIVING_DWM_20028 = 20028;
  DRIVING_DWM_20031 = 20031;
  DRIVING_DWM_20032 = 20032;
  DRIVING_DWM_20034 = 20034;
  DRIVING_DWM_20038 = 20038;
  DRIVING_DWM_20040 = 20040;
  DRIVING_DWM_20041 = 20041;
  DRIVING_DWM_20042 = 20042;
  DRIVING_DWM_20048 = 20048;
  DRIVING_DWM_20050 = 20050;
  DRIVING_DWM_20051 = 20051;
  DRIVING_DWM_20052 = 20052;
  DRIVING_DWM_20053 = 20053;
  DRIVING_DWM_20054 = 20054;
  DRIVING_DWM_20055 = 20055;
  DRIVING_DWM_20058 = 20058;
  DRIVING_DWM_20061 = 20061;
  DRIVING_DWM_20065 = 20065;
  DRIVING_DWM_20069 = 20069;
  DRIVING_DWM_20070 = 20070;
  DRIVING_DWM_20080 = 20080;
  DRIVING_DWM_20082 = 20082;
  DRIVING_DWM_20086 = 20086;
  DRIVING_DWM_20087 = 20087;
  DRIVING_DWM_20088 = 20088;
  DRIVING_DWM_20089 = 20089;
  DRIVING_DWM_20091 = 20091;
  DRIVING_DWM_20094 = 20094;
  DRIVING_DWM_20102 = 20102;
  DRIVING_DWM_20104 = 20104;
  DRIVING_DWM_20105 = 20105;
  DRIVING_DWM_20109 = 20109;
  DRIVING_DWM_20110 = 20110;
  DRIVING_DWM_20111 = 20111;
  DRIVING_DWM_20119 = 20119;
  DRIVING_DWM_20120 = 20120;
  DRIVING_DWM_20135 = 20135;
  DRIVING_DWM_20137 = 20137;
  DRIVING_DWM_20138 = 20138;
  DRIVING_DWM_20140 = 20140;
  DRIVING_DWM_20143 = 20143;
  DRIVING_DWM_20175 = 20175;
  DRIVING_DWM_20193 = 20193;
  DRIVING_DWM_20197 = 20197;
  DRIVING_DWM_20198 = 20198;
  DRIVING_DWM_20201 = 20201;
  DRIVING_DWM_20202 = 20202;
  DRIVING_DWM_20203 = 20203;
  DRIVING_DWM_20208 = 20208;
  DRIVING_DWM_20212 = 20212;
  DRIVING_DWM_20213 = 20213;
  DRIVING_DWM_20214 = 20214;
  DRIVING_DWM_20217 = 20217;
  DRIVING_DWM_20226 = 20226;
  DRIVING_DWM_20227 = 20227;
  DRIVING_DWM_20228 = 20228;
  DRIVING_DWM_20232 = 20232;
  DRIVING_DWM_20241 = 20241;
  DRIVING_DWM_20242 = 20242;
  DRIVING_DWM_20243 = 20243;
  DRIVING_DWM_20245 = 20245;
  DRIVING_DWM_20250 = 20250;
  DRIVING_DWM_20253 = 20253;
  DRIVING_DWM_20254 = 20254;
  DRIVING_DWM_20255 = 20255;
  DRIVING_DWM_20258 = 20258;
  DRIVING_DWM_20268 = 20268;
  DRIVING_DWM_20269 = 20269;
  DRIVING_DWM_20270 = 20270;
  DRIVING_DWM_20271 = 20271;
  DRIVING_DWM_20272 = 20272;
  DRIVING_DWM_20275 = 20275;
  DRIVING_DWM_20276 = 20276;
  DRIVING_DWM_20277 = 20277;
  DRIVING_DWM_20278 = 20278;
  DRIVING_DWM_20279 = 20279;
  DRIVING_DWM_20288 = 20288;
  DRIVING_DWM_20289 = 20289;
  DRIVING_DWM_22004 = 22004;
  DRIVING_DWM_22006 = 22006;
  DRIVING_DWM_22011 = 22011;
  DRIVING_DWM_22012 = 22012;
  DRIVING_DWM_22013 = 22013;
  DRIVING_DWM_22015 = 22015;
  DRIVING_DWM_22016 = 22016;
  DRIVING_DWM_22023 = 22023;
  DRIVING_DWM_22036 = 22036;
  DRIVING_DWM_22037 = 22037;
  DRIVING_DWM_22038 = 22038;
  DRIVING_DWM_22045 = 22045;
  DRIVING_DWM_22046 = 22046;
  DRIVING_DWM_22047 = 22047;
  DRIVING_DWM_22061 = 22061;
  DRIVING_DWM_22073 = 22073;
  DRIVING_DWM_22075 = 22075;
  DRIVING_DWM_22076 = 22076;
  DRIVING_DWM_22080 = 22080;
  DRIVING_DWM_22081 = 22081;
  DRIVING_DWM_22082 = 22082;
  DRIVING_DWM_22083 = 22083;
  DRIVING_DWM_22089 = 22089;
  DRIVING_DWM_22090 = 22090;
  DRIVING_DWM_22145 = 22145;
  DRIVING_DWM_22148 = 22148;
  DRIVING_DWM_22149 = 22149;
  DRIVING_DWM_22155 = 22155;
  DRIVING_DWM_22156 = 22156;
  DRIVING_DWM_22157 = 22157;
  DRIVING_DWM_22159 = 22159;
  DRIVING_DWM_22160 = 22160;
  DRIVING_DWM_22161 = 22161;
  DRIVING_DWM_22167 = 22167;
  DRIVING_DWM_22170 = 22170;
  DRIVING_DWM_22172 = 22172;
  DRIVING_DWM_22173 = 22173;
  DRIVING_DWM_22178 = 22178;
  DRIVING_DWM_22179 = 22179;
  DRIVING_DWM_22180 = 22180;
  DRIVING_DWM_23002 = 23002;
  DRIVING_DWM_23006 = 23006;
  DRIVING_DWM_23007 = 23007;
  DRIVING_DWM_23012 = 23012;
  DRIVING_DWM_23014 = 23014;
  DRIVING_DWM_23016 = 23016;
  DRIVING_DWM_23018 = 23018;
  DRIVING_DWM_23019 = 23019;
  DRIVING_DWM_23021 = 23021;
  DRIVING_DWM_23026 = 23026;
  DRIVING_DWM_23028 = 23028;
  DRIVING_DWM_23030 = 23030;
  DRIVING_DWM_23031 = 23031;
  DRIVING_DWM_23033 = 23033;
  DRIVING_DWM_23041 = 23041;
  DRIVING_DWM_23044 = 23044;
  DRIVING_DWM_23045 = 23045;
  DRIVING_DWM_23048 = 23048;
  DRIVING_DWM_23051 = 23051;
  DRIVING_DWM_23053 = 23053;
  DRIVING_DWM_23054 = 23054;
  DRIVING_DWM_23060 = 23060;
  DRIVING_DWM_23063 = 23063;
  DRIVING_DWM_23068 = 23068;
  DRIVING_DWM_23069 = 23069;
  DRIVING_DWM_23070 = 23070;
  DRIVING_DWM_23118 = 23118;
  DRIVING_DWM_23123 = 23123;
  DRIVING_DWM_23124 = 23124;
  DRIVING_DWM_23126 = 23126;
  DRIVING_DWM_23127 = 23127;
  DRIVING_DWM_23128 = 23128;
  DRIVING_DWM_23129 = 23129;
  DRIVING_DWM_23130 = 23130;
  DRIVING_DWM_23131 = 23131;
  DRIVING_DWM_23133 = 23133;
  DRIVING_DWM_23134 = 23134;
  DRIVING_DWM_23135 = 23135;
  DRIVING_DWM_23136 = 23136;
  DRIVING_DWM_23137 = 23137;
  DRIVING_DWM_23138 = 23138;
  DRIVING_DWM_23140 = 23140;
  DRIVING_DWM_23141 = 23141;
  DRIVING_DWM_23142 = 23142;
  DRIVING_DWM_23143 = 23143;
  DRIVING_DWM_23145 = 23145;
  DRIVING_DWM_23147 = 23147;
  DRIVING_DWM_23152 = 23152;
  DRIVING_DWM_23153 = 23153;
  DRIVING_DWM_23154 = 23154;
  DRIVING_DWM_23155 = 23155;
  DRIVING_DWM_23156 = 23156;
  DRIVING_DWM_23157 = 23157;
  DRIVING_DWM_23158 = 23158;
  DRIVING_DWM_23159 = 23159;
  DRIVING_DWM_23160 = 23160;
  DRIVING_DWM_23161 = 23161;
  DRIVING_DWM_23162 = 23162;
  DRIVING_DWM_23163 = 23163;
  DRIVING_DWM_23164 = 23164;
  DRIVING_DWM_23165 = 23165;
  DRIVING_DWM_23166 = 23166;
  DRIVING_DWM_23167 = 23167;
  DRIVING_DWM_23168 = 23168;
  DRIVING_DWM_23169 = 23169;
  DRIVING_DWM_23170 = 23170;
  DRIVING_DWM_23171 = 23171;
  DRIVING_DWM_23172 = 23172;
  DRIVING_DWM_23173 = 23173;
  DRIVING_DWM_23174 = 23174;
  DRIVING_DWM_23175 = 23175;
  DRIVING_DWM_23176 = 23176;
  DRIVING_DWM_23177 = 23177;
  DRIVING_DWM_23178 = 23178;
  DRIVING_DWM_23179 = 23179;
  DRIVING_DWM_23180 = 23180;
  DRIVING_DWM_23181 = 23181;
  DRIVING_DWM_23182 = 23182;
  DRIVING_DWM_23183 = 23183;
  DRIVING_DWM_23184 = 23184;
  DRIVING_DWM_23185 = 23185;
  DRIVING_DWM_23186 = 23186;
  DRIVING_DWM_23187 = 23187;
  DRIVING_DWM_23188 = 23188;
  DRIVING_DWM_23189 = 23189;
  DRIVING_DWM_23190 = 23190;
  DRIVING_DWM_23191 = 23191;
  DRIVING_DWM_23192 = 23192;
  DRIVING_DWM_23193 = 23193;
  DRIVING_DWM_23194 = 23194;
  DRIVING_DWM_23195 = 23195;
  DRIVING_DWM_23196 = 23196;
  DRIVING_DWM_23197 = 23197;
  DRIVING_DWM_23198 = 23198;
  DRIVING_DWM_23199 = 23199;
  DRIVING_DWM_23200 = 23200;
  DRIVING_DWM_23201 = 23201;
  DRIVING_DWM_23202 = 23202;
  DRIVING_DWM_23203 = 23203;
  DRIVING_DWM_23207 = 23207;
  DRIVING_DWM_23208 = 23208;
  DRIVING_DWM_23212 = 23212;
  DRIVING_DWM_23213 = 23213;
  DRIVING_DWM_23217 = 23217;
  DRIVING_DWM_23218 = 23218;
  DRIVING_DWM_23219 = 23219;
  DRIVING_DWM_23220 = 23220;
  DRIVING_DWM_23222 = 23222;

  DRIVING_DWM_FAULT_59001 = 59001;
  DRIVING_DWM_FAULT_59002 = 59002;
  DRIVING_DWM_FAULT_59004 = 59004;
  DRIVING_DWM_FAULT_59005 = 59005;
  DRIVING_DWM_FAULT_59006 = 59006;
  DRIVING_DWM_FAULT_59007 = 59007;
  DRIVING_DWM_FAULT_59008 = 59008;
  DRIVING_DWM_FAULT_59009 = 59009;
  DRIVING_DWM_FAULT_59012 = 59012;
  DRIVING_DWM_FAULT_59013 = 59013;
  DRIVING_DWM_FAULT_59014 = 59014;
  DRIVING_DWM_FAULT_59015 = 59015;
  DRIVING_DWM_FAULT_59065 = 59065;
  DRIVING_DWM_FAULT_59066 = 59066;
  DRIVING_DWM_FAULT_59067 = 59067;
  DRIVING_DWM_FAULT_59068 = 59068;
  DRIVING_DWM_FAULT_59069 = 59069;
  DRIVING_DWM_FAULT_59070 = 59070;
  DRIVING_DWM_FAULT_59072 = 59072;
  DRIVING_DWM_FAULT_59102 = 59102;
}


