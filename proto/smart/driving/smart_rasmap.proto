syntax = "proto3";
package smart;
import "smart/smart_common.proto";

message CrossData {
  //GuardRail Boundary. LineTypeSeN(LineType)
  repeated BoundaryLine boundary_line = 1;
  //可行驶区域
  repeated DrivingArea driving_area = 2;
  //停止线
  repeated Stopline stop_line = 3;
  //1.斑马线；
  repeated Area area = 4;
  //地面箭头
  repeated ArrowInfo arrow_info = 5;
  //车道线
  repeated Spline lane_spline = 6;
  //时间戳
  uint64 time_stamp = 7;
  //红绿灯
  repeated Trafficlight traffic_light = 8;
  //车道信息
  repeated Lane lanes = 9;
  //变道信息
  LaneChangeRelated lane_change_related = 10;
  //变道状态
  Lanechange_Sts lanechange_sts = 11;
  //repeated ParkingSpace parking_space = 12;
  //预警状态
  OperationStatus operation_status = 13;
  float turn_distance = 14;
  //======
  //避让大车方向
  AvoidanceDir avoidance_dir = 15;
  //2D红绿灯
  TrafficLightInfo2D traffic_light_info_2D = 16;
}

message TrafficLightInfo2D {
  //红绿灯类型
  TFLType traffic_light_type = 1;
  //红绿灯颜色
  TFLColor traffic_light_color = 2;
  //车道方向
  LaneTurn lane_turn = 3;
  //红绿灯状态
  TrafficLightState traffic_light_state = 4;
  int32 count_downs = 6;
}

enum TrafficLightState {
  UNKNOWN = 0;
  NONE = 1;
  NORMAL = 2;
  BLOCKED = 3;
  UNABLE_TO_LINK_LANE = 4;
}

enum LaneTurn {
  INVALID = 0;
  STRAIGHT = 1;
  LEFT = 2;
  RIGHT = 3;
  U_TURN_LEFT = 4;
  U_TURN_RIGHT = 5;
}

message AvoidanceDir {
  enum Dir {
    INVALID = 0;
    RIGHT = 1;
    LEFT = 2;
    RESERVE = 3;
  };
  Dir dir = 1;
}

message BoundaryLine {
  //道路边界线ID
  int32 id = 1;
  //道路边界坐标点
  Polyline polyline = 2;
}

message DrivingArea {
  //可行驶区域ID
  //AreaInfo. AreaIDSeN(ID)
  int32 id = 1;
  //可行驶区域
  //GeometryPointsSeN(VehiclePointMap). PositionYSeN(VehiclePosition)
  repeated Polygon polygon = 2;
  //======
  //0    invalid
  //1    lane（不发）
  //2    road（路面）
  //3    NotRoadArea（非路面）
  //4    reserve（不发）
}

message Stopline {
  //停止线ID
  //Stopline. StoplineIDSeN(ID)
  int32 id = 1;
  //停止线起点
  //Stopline. GeometryPointsSeN(VehiclePointMap). VehiclePoint. PositionYSeN(VehiclePosition)
  Pnt3D startPnt = 2;
  //停止线终点
  Pnt3D endPnt = 3;
  enum Color {
    InitialDefault = 0;
    red = 1;
  }
  Color color = 4;
}

message Area {
  //区域ID
  //Crosswalk. CrossWalkIDSeN(ID)为人行道id
  int32 id = 1;
  //顺时针多边形
  //Crosswalk. GeometryPointsSeN(VehiclePointMap) .
  Polygon polygon = 2;
  //"区域类型：

  enum Type {
    //0.未知；
    UNKNOWN = 0;
    //1.斑马线；
    CROSSWALK = 1;
    //2.待行区；（预留，待开发）
    WAITINGAREA = 2;
    //3.导流带；（预留，待开发）
    DIVERSIONZONE = 3;
  };
  Type type = 3;
}

message ArrowInfo {
  //地面箭头ID
  //RoadMark. RoadmarkIDSeN(ID)
  int32 id = 1;
  //=====
  //RoadMark. RoadmarkTypeSeN(RoadMarkType)
  //smart地面箭头类型：
  //1.直行
  //3.直行或左转
  //4.直行或右转
  //5.直行或掉头
  //6.左转
  //7.左转或掉头
  //8.左弯或向左合流
  //9.右转
  //10.右弯或向右合流
  //11.左右转弯
  //12.掉头
  //13.禁止左弯标记
  //14.禁止右弯标记
  //15.禁止掉头标记
  //16.直行或左转或右转
  //17.直行或掉头或左转
  //18.右转或掉头
  //22.车距确认线
  //23.停车让行线
  //24.减速让行线
  //25.停止线
  //26.禁停区
  //27.靠右侧道路
  //28.靠左侧道路
  //29.立交直行和左转弯
  //30.立交直行和右转弯
  //31环岛
  //其他
  enum ArrowType {
    //0.地面箭头无效
    ARROW_TYPE_INVALID = 0;
    //1.直行箭头
    ARROW_TYPE_S = 1;
    //2.右转箭头
    ARROW_TYPE_RT = 2;
    //3.左转箭头
    ARROW_TYPE_LT = 3;
    //4.直行右转箭头
    ARROW_TYPE_SRT = 4;
    //5.直行左转箭头
    ARROW_TYPE_SLT = 5;
    //6.直行左右转箭头
    ARROW_TYPE_SLR = 6;
    //7.左右转箭头
    ARROW_TYPE_LR = 7;
    //8.左调头箭头
    ARROW_TYPE_UL = 8;
    //9.右调头箭头（感知+地图）
    ARROW_TYPE_UR = 9;
    //10.直行调头箭头（感知+地图）
    ARROW_TYPE_ST = 10;
    //11.人行横道预告线（菱形图标）（内部支持）
    ARROW_TYPE_CROSSWARN = 11;
    //12.公交专用文字标识（预留）
    ARROW_TYPE_BUSONLY = 12;
  };
  ArrowType arrowtype = 2;
  //地面箭头包裹框中心坐标
  //RoadMark. GeometryPointsSeN(VehiclePointMap) . PositionXSeN(VehiclePosition)
  Pnt3D pos = 3;
  //RoadMark. RoadMarkHeadingSeN(Heading)
  Pnt2D dir = 4;
}

message Spline {
  // Boundary. BoundaryIDSeN (ID)(车道线唯一数据标识)
  int32 id = 1;
  //Boundary. LineTypeSeN(LineType) (车道边界类型)
  //Boundary. LineMarkingSeN(LineMarking)（车道线形状）
  enum LineType {
    //0. 无效车道线
    LINE_TYPE_INVALID = 0;
    // 1.单实线
    LINE_TYPE_SOLID = 1;
    //2.单虚线
    LINE_TYPE_DASH = 2;
    //3.内实外虚
    LINE_TYPE_SOLID_DASH = 3;
    //4.内虚外实
    LINE_TYPE_DASH_SOLID = 4;
    //5.双实线
    LINE_TYPE_SOLID_SOLID = 5;
    //6.双虚线
    LINE_TYPE_DASH_DASH = 6;
    //7.路钉
    LINE_TYPE_BOTTS = 7;
    // 8.减速带（鱼骨线）
    LINE_TYPE_DECELERATION = 8;
    //9.菱形线
    LINE_TYPE_HOV = 9;
    //10.道路边缘
    LINE_TYPE_ROAD_EDGE = 10;
    //11.护栏
    LINE_TYPE_ELEVATED_STRUCTURE = 11;
    //12.路沿
    LINE_TYPE_CURB = 12;
    //13.锥桶墙
    LINE_TYPE_CONES_POLES = 13;
    //14.水马墙
    LINE_TYPE_BARRIER = 14;
  };
  LineType linetype = 2;
  //"拟合车道线颜色
  //Boundary.BoundaryColorSeN(Color)
  enum LineColor {
    //0.车道线颜色无效；
    LINE_COLOR_INVALID = 0;
    //1.灰色（预留）;
    LINE_COLOR_GRAY = 1;
    //2.白色；
    LINE_COLOR_WHITE = 2;
    //3.黄色；
    LINE_COLOR_YELLOW = 3;
  };
  LineColor linecolor = 9;
  //车道几何坐标点串
  //Boundary. GeometryPointsSeN(VehiclePointMap). VehiclePoint. PositionYSeN(VehiclePosition)
  Polyline lineboundary = 10;
}

enum TFLType {
  //0.红绿灯识别无效；
  TFL_TYPE_INVALID = 0;
  //1.直行箭头式红绿灯；
  TFL_TYPE_S = 1;
  //2.左转箭头式红绿灯；
  TFL_TYPE_L = 2;
  //3.右转箭头式红绿灯；
  TFL_TYPE_R = 3;
  //4.调头箭头式红绿灯；
  TFL_TYPE_U = 4;
  //5.圆形红绿灯；
  TFL_TYPE_Round = 5;
};

//"红绿灯颜色
enum TFLColor {
  //0.红绿灯颜色未识别；
  TFL_COLOR_INVALID = 0;
  //1.红绿熄灭，显示灰色；
  TFL_COLOR_GREY = 1;
  //2.红灯常亮；
  TFL_COLOR_RED = 2;
  //3.绿灯常亮；
  TFL_COLOR_GREEN = 3;
  //4.黄灯常亮；
  TFL_COLOR_YELLOW = 4;
  //5.红灯闪烁；（预留）
  TFL_COLOR_RF = 5;
  //6.绿灯闪烁；（预留）
  TFL_COLOR_GF = 6;
  //7.黄灯闪烁；（预留）"
  TFL_COLOR_YF = 7;
  TFL_COLOR_BLACK = 8;
};

message Trafficlight_Info {
  //smart
  //中间灯位：
  //TLATrafficLightLevelSeN — 只给中间灯位显示（箭头或圆形）
  //TLAStrightSignalLampSeN — — 直行灯位（红黄绿）
  //左灯位：
  //TLALeftSignalLampSeN — 左灯位向左箭头（箭头 红黄绿）
  //TLATurnSignalLamp — — 左灯位掉头箭头（箭头 红黄绿）
  //右灯位：
  //TLARightSignalLampSeN — — 右灯位向右箭头（箭头 红黄绿）
  TFLType trafficlight_type = 1;
  TFLColor trafficlight_color = 2;
}

message Trafficlight {
  //红绿灯ID
  int32 id = 1;
  //红绿灯属性 这里只发一个
  repeated Trafficlight_Info trafficlight_info = 2;
  //表示对应车道的倒计时，默认值为0，不显示
  uint32 trafficlight_time = 3;
  //红绿灯坐标信息
  Pnt3D pos = 4;
  //长
  float length = 5;
  //宽
  float width = 6;
  //高
  float height = 7;
}

message Lane {
  //车道中心线id
  int32 id = 1;
  //左车道边线id
  int32 left_boundary_id = 2;
  //右车道边线id
  int32 right_boundary_id = 3;
  //车道中心线
  Polyline centerline = 4;
  //前置id
  repeated int32 predecessor_id = 5;
  //后置id
  repeated int32 successor_id = 6;
  //左车道id
  int32 left_neighbor_id = 7;
  //右车道id
  int32 right_neighbor_id = 8;
  //是否是自车所在车道
  bool is_ego_lane = 9;
}

message LaneChangeRelated {
  //目标车道
  TargetLane target_lane = 1;
  enum TargetLane {
    MIDDLE = 0;
    LEFT = 1;
    RIGHT = 2;
  }
  //目标车道id
  repeated int32 target_lane_id = 2;
  //======
  //目标车位位置
  LaneChangeTargetPos target_pos = 3;
}

message LaneChangeTargetPos {
  //RelativeLocalizationInf. TargetPoistionXSeN(VehiclePosition)
  Pnt3D pos = 1;
  //RelativeLocalizationInf. TargetPoistionAngleSeN(Angle)
  Pnt2D dir = 2;
}

message Lanechange_Sts {
  //=======
  //当变道需确认时，变道触发条件满足，功能请求用户确认时，需发送LaneColorSeN ==4
  enum LanechangeSts {
    //"0; //无变道，变道完成/变道失败
    LANECHANGESTS_INVALID = 0;
    //1; //左变道条件满足
    LANECHANGESTS_LEFT_SATISFIED = 1;
    //2; //右变道条件满足
    LANECHANGESTS_RIGHT_SATISFIED = 2;
    //3; //左变道条件不满足
    LANECHANGESTS_LEFT_UNSATISFIED = 3;
    //4; //右变道条件不满足
    LANECHANGESTS_RIGHT_UNSATISFIED = 4;
    //5; //左侧实线变道条件不满足
    LANECHANGESTS_LEFT_SOLID = 5;
    //6; //右侧实线变道条件不满足"
    LANECHANGESTS_RIGHT_SOLID = 6;
    //变道成功能
    LANECHANGESTS_SUCCESS = 7;
    //变道失败
    LANECHANGESTS_FAIL = 8;
    //变道失败，正在返回原位
    LANECHANGESTS_FAIL_RECOVERING= 9;
    //变道确认框请求中
    LANECHANGESTS_ASKING= 10;
  }
  LanechangeSts lanechange_sts = 1;
}

message ParkingSpace {
  string id = 1;
  repeated ParkSpaceBoundingBox bounding_box = 2;
  ParkingSpaceType type = 3;
  ParkingSpaceStatus status = 4;
}

enum ParkingSpaceType {
  PARK_SPACE_TYPE_NONE = 0;
  PARK_SPACE_TYPE_VERTICAL = 1;
  PARK_SPACE_TYPE_LATERAL = 2;
  PARK_SPACE_TYPE_OBLIQUE = 3;
  PARK_SPACE_TYPE_INVALID = 4;
}
message ParkSpaceBoundingBox {
  Pnt3D point = 1;
  ParkSpacePosition position = 2;
  int32 quality = 3;
  enum ParkSpacePosition {
    POSITION_NONE = 0;
    POSITION_LEFT_FRONT = 1;
    POSITION_LEFT_REAR = 2;
    POSITION_RIGHT_REAR = 3;
    POSITION_RIGHT_FRONT = 4;
  }
}

enum ParkingSpaceStatus {
  PARK_SPACE_STATUS_UNKNOWN = 0;
  PARK_SPACE_STATUS_FREE = 1;
  PARK_SPACE_STATUS_OCCUPIED = 2;
  PARK_SPACE_STATUS_DESTINATION = 3;
  PARK_SPACE_STATUS_SELECTABLE = 4;
  PARK_SPACE_STATUS_FAVORITE = 5;
  PARK_SPACE_STATUS_INVALID = 6;
}

message OperationStatus {
  uint64 timestamp = 1;
  ICCStatusInfo ICC_status = 2;
  LDWStatusInfo ldw_status = 3;
  ELKStatusInfo elk_status = 4;
  LKAStatusInfo lka_status = 5;
}

enum ICCStatus {
  ICC_OFF = 0;
  ICC_FAILURE = 1;
  ICC_PASSIVE = 2;
  ICC_STANDBY = 3;
  ICC_NORMAL = 4;
  ICC_LATERAL_CONTROL_EXIT = 5;
  ICC_FORBIDDEN = 6;
  ICC_DRIVER_TAKEOFF = 7;
  ICC_LOW_RISK_FAILURE = 8;
  ICC_MRM = 9;
}

message ICCPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
  VehicleStatusCheckInfo vehicle_status_check_info = 3;
}

message ICCStatusInfo {
  ICCStatus status = 1;
  oneof reason {ICCPassiveReason passive_reason = 2;}
}

enum LDWStatus {
  LDW_OFF = 0;
  LDW_PASSIVE = 1;
  LDW_STANDBY = 2;
  LDW_LEFT_ACTIVE = 3;
  LDW_RIGHT_ACTIVE = 4;
  LDW_FAILURE = 5;
}
message LDWStatusInfo {
  LDWStatus status = 1;
  oneof reason {LDWPassiveReason passive_reason = 2;}
}

message LDWPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
}

enum ELKStatus {
  ELK_OFF = 0;
  ELK_FAILURE = 1;
  ELK_PASSIVE = 2;
  ELK_STANDBY = 3;
  ELK_ACTIVE = 4;
}

message ELKStatusInfo {
  ELKStatus status = 1;
  oneof reason {ELKActiveReason active_reason = 2;}
}

message ELKActiveReason {
  bool left_warning = 1;
  bool right_warning = 2;
  repeated int32 obj_id = 3;
}

enum LKAStatus {
  LKA_OFF = 0;
  LKA_PASSIVE = 1;
  LKA_STANDBY = 2;
  LKA_LEFT_ACTIVE = 3;
  LKA_RIGHT_ACTIVE = 4;
  LKA_FAILURE = 5;
}
message LKAStatusInfo {
  LKAStatus status = 1;
  oneof reason {LKAPassiveReason passive_reason = 2;}
}

message LKAPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
}

// 以下枚举值必须和common保持一致
message DeviceCheckInfo {
  enum DeviceInfo {
    DEVICE_UNKNOW = 0;
    DRIVER_SEATBELT_NOT_BUCKLED = 1; //主驾安全带未系
    EPB_PARKED = 2;                  //电子手刹拉起
    GEAR_NOT_IN_D = 3;               //档位不在D挡
    DOOR_OPENED = 4;                 //四门两盖开启
    BRAKE_APPLIED = 5;               //非静止状态踩刹车
    WIPER_HIGH = 6;                  //雨刮高速
    CRUISE_CANCEL_PRESSED = 7;       //前拨拨杆
    THROTTLE_OVERRIDE = 8;           // 油门接管，使用: 泊车
    ACTUAL_ANGLE_OVER_THRESHOLD = 9; //方向盘转角超过门限
    LEFT_TURN_SIGNAL_ON = 10;        //左转向灯亮起
    RIGHT_TURN_SIGNAL_ON = 11;       //右转向灯亮起
    NO_TURN_SIGNAL = 12;             //没有转向灯信号
    GEER_IN_R = 13;                  //档位在R档
    GEER_IN_D = 14;                  //档位在D档位
    GEER_IN_N = 15;                  //档位在N档位
    GEER_IN_P = 16;                  //档位在P档位
    GEAR_OVERRIDE = 17;              // 档位接管, 使用：泊车
    STEERING_WHEEL_OVERRIDE = 18;    // 方向盘接管, 使用：泊车
    TRUNK_OPENED = 19;               // 后备箱打开, 使用：泊车
    REAR_VIEW_MIRROR_CLOSED = 20;    // 后视镜折叠,使用：泊车
    STEER_OVERRIDE = 21;             //方向盘接管

    STEER_WHEEL_SPEED_TO_HIGH = 22;   //方向盘转速过高
    DOUBLE_FLASH_ON = 23;             //双闪打开
    FOG_LIGHTS_ON = 24;               //雾灯打开
    HOOD_OPENED = 25;                 // 引擎盖开启
    EPS_TORSIONTORQUE_INVALID = 26;   // 扭矩invalid
    HOD_HANDOFF_MONITOR_INVALID = 27; // hod invalid
    DRIVER_DOOR_OPENED = 28;          // 主驾门打开
  }
  repeated DeviceInfo device_info = 1;
}
message BusinessCheckInfo {
  enum Status {
    STATUS_UNKNOW = 0;
    LCC_ACTIVATED = 1;     // LCC激活
    NOH_ACTIVATED = 2;     // NOH激活
    AEB_ACTIVATED = 3;     // AEB激活
    APA_ON = 4;            // APA开启
    ESC_OFF = 5;           // ESC OFF
    ABS_ACTIVED = 6;       // ABS激活
    HDC_ACTIVED = 7;       // HDC激活
    TCS_ACTIVED = 8;       // TCS激活
    VDC_ACTIVED = 9;       // VDC激活
    VCU_NOT_READY = 10;    // VCU未就绪
    CDP_ACTIVED = 11;      // CDP激活
    AVH_ACTIVED = 12;      // AVH激活
    ESC_ACTIVED = 13;      // ESC激活
    VCU_LIMPHOME_TCM = 14; // VCU在跛行模式
    EPS_NOT_READEY = 15;
    ESP_ACTIVED = 16;
    DTC_ACTIVED = 17;
    EBP_ACTIVED = 18; // EBP 激活
    EBD_ACTIVED = 19; // EBD 激活
    ACC_ACTIVED = 20;
    ODD_UNAVLIABLE = 21;
    ESC_SYSTEM_FAILED = 22; // ESC 系统故障
    EPS_SYSTEM_FAILED = 23; // EPS 系统故障
    VCU_SYSTEM_FAILED = 24; // VCU 系统故障
    EPB_SYSTEM_FAILED = 25; // EPB 系统故障
    LDW_ACTIVATED = 26;
    RDP_ACTIVATED = 27;

    TOO_SLOW_SPEED = 4000;          //速度太低
    TOO_FAST_SPEED = 4001;          //速度过高
    HIGH_YAW_RATE = 4002;           //横摆角速度高
    MISSING_LEFT_LANE_LINE = 4003;  //左边车道线丢失
    MISSING_RIGHT_LANE_LINE = 4004; //右边车道线丢失
    OVER_WIDE_LANE = 4005;
    OVER_NARROW_LANE = 4006;
    EXCESSIVE_CURVATURE_LANE = 4007;

    HEAVY_BRAKE_PRESSURE = 4008;
    EXCESSIVE_STEERING_LEFT_TORQUE = 4009;
    EXCESSIVE_STEERING_RIGHT_TORQUE = 4010;
    EXCESSIVE_STEERING_ANGLE = 4011;
    EXCESSIVE_STEERING_ANGLE_RATE = 4012;
    HIGH_ACC_PEDAL_RATE = 4013; // > 70%/s
    EXCESSIVE_WARNING_DURATION = 4014;
    TOO_SHORT_WARNING_INTERVAL = 4015;
    FRONT_WHEEL_EXCEED_WARNING_LINE = 4016;

    NO_NAVIGATION_INFO = 4500;  //没有导航信息
    NAVIGATION_ABNORMAL = 4501; //导航异常
    OUT_ODD_ROAD = 4502;
    NAVIGATION_ID_NOT_MATCH = 4503; //新的导航未开启成功，导致导航id不匹配
  }
  repeated Status status = 1;
}
message VehicleStatusCheckInfo {
  enum Status {
    STATUS_UNKNOW = 0;
    VEHICLE_SLIP = 1;                     //车辆溜车
    VEHICLE_SPEED_OVER_THRESHOLD = 2;     //车速超过门限>150km/h
    CURVE_RADIUS_LESS_THAN_THRESHOLD = 3; //<125m
    VEHICLE_SPEED_TO_LOW = 5;             //车速过低
    NOT_STANDSTILL = 6;                   //车辆非静止
  }
  repeated Status status = 1;
}