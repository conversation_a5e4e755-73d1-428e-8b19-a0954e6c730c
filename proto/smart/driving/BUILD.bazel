package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "smart_driving_dwm_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_driving_dwm.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_driving_dwm_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_driving_dwm_proto",
    ],
)

python_proto_library(
    name = "smart_driving_dwm_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_driving_dwm_proto",
    ],
)

proto_library(
    name = "smart_driving_object_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_driving_object.proto"],
    deps = [
        "//proto/smart:smart_common_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_driving_object_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_driving_object_proto",
    ],
)

python_proto_library(
    name = "smart_driving_object_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_driving_object_proto",
    ],
)

proto_library(
    name = "smart_rasmap_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_rasmap.proto"],
    deps = [
        "//proto/smart:smart_common_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_rasmap_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_rasmap_proto",
    ],
)

python_proto_library(
    name = "smart_rasmap_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_rasmap_proto",
    ],
)

