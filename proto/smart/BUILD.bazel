package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "smart_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_command.proto"],
    deps = [
        "//proto/smart:smart_common_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_command_proto",
    ],
)

python_proto_library(
    name = "smart_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_command_proto",
    ],
)

proto_library(
    name = "smart_switch_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_switch.proto"],
    deps = [
        "//proto/smart:smart_common_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_switch_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_switch_proto",
    ],
)

python_proto_library(
    name = "smart_switch_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_switch_proto",
    ],
)

proto_library(
    name = "smart_business_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_business.proto"],
    deps = [
        "//proto/smart:smart_switch_proto",
        "//proto/smart/driving:smart_driving_dwm_proto",
        "//proto/smart/parking:smart_parking_dwm_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_business_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_business_proto",
    ],
)

python_proto_library(
    name = "smart_business_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_business_proto",
    ],
)

proto_library(
    name = "smart_common_proto",
    visibility = ["//visibility:public"],
    srcs = ["smart_common.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "smart_common_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":smart_common_proto",
    ],
)

python_proto_library(
    name = "smart_common_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":smart_common_proto",
    ],
)

