package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "visualizer_esa_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["visualizer_esa_command.proto"],
    deps = [
        "//proto/common:header_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "visualizer_esa_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":visualizer_esa_command_proto",
    ],
)

python_proto_library(
    name = "visualizer_esa_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":visualizer_esa_command_proto",
    ],
)

proto_library(
    name = "esa_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["esa_state.proto"],
    deps = [
        "//proto/common:header_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "esa_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":esa_state_proto",
    ],
)

python_proto_library(
    name = "esa_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":esa_state_proto",
    ],
)

proto_library(
    name = "esa_trajectory_proto",
    visibility = ["//visibility:public"],
    srcs = ["esa_trajectory.proto"],
    deps = [
        "//proto/canbus:chassis_proto",
        "//proto/common:header_proto",
        "//proto/common:pnc_point_proto",
        "//proto/common:vehicle_signal_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "esa_trajectory_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":esa_trajectory_proto",
    ],
)

python_proto_library(
    name = "esa_trajectory_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":esa_trajectory_proto",
    ],
)

