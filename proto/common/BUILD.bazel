package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "module_status_proto",
    visibility = ["//visibility:public"],
    srcs = ["module_status.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "module_status_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":module_status_proto",
    ],
)

python_proto_library(
    name = "module_status_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":module_status_proto",
    ],
)

proto_library(
    name = "vehicle_signal_proto",
    visibility = ["//visibility:public"],
    srcs = ["vehicle_signal.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "vehicle_signal_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":vehicle_signal_proto",
    ],
)

python_proto_library(
    name = "vehicle_signal_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":vehicle_signal_proto",
    ],
)

proto_library(
    name = "last_words_proto",
    visibility = ["//visibility:public"],
    srcs = ["last_words.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "last_words_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":last_words_proto",
    ],
)

python_proto_library(
    name = "last_words_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":last_words_proto",
    ],
)

proto_library(
    name = "drive_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["drive_state.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "drive_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":drive_state_proto",
    ],
)

python_proto_library(
    name = "drive_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":drive_state_proto",
    ],
)

proto_library(
    name = "pnc_point_proto",
    visibility = ["//visibility:public"],
    srcs = ["pnc_point.proto"],
    deps = [
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "pnc_point_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":pnc_point_proto",
    ],
)

python_proto_library(
    name = "pnc_point_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":pnc_point_proto",
    ],
)

proto_library(
    name = "error_code_proto",
    visibility = ["//visibility:public"],
    srcs = ["error_code.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "error_code_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":error_code_proto",
    ],
)

python_proto_library(
    name = "error_code_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":error_code_proto",
    ],
)

proto_library(
    name = "security_strategy_proto",
    visibility = ["//visibility:public"],
    srcs = ["security_strategy.proto"],
    deps = [
        "//proto/common:module_status_proto",
        "//proto/common:error_code_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "security_strategy_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":security_strategy_proto",
    ],
)

python_proto_library(
    name = "security_strategy_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":security_strategy_proto",
    ],
)

proto_library(
    name = "onboard_status_proto",
    visibility = ["//visibility:public"],
    srcs = ["onboard_status.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "onboard_status_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":onboard_status_proto",
    ],
)

python_proto_library(
    name = "onboard_status_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":onboard_status_proto",
    ],
)

proto_library(
    name = "report_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["report_event.proto"],
    deps = [
        "@com_google_protobuf//:any_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "report_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":report_event_proto",
    ],
)

python_proto_library(
    name = "report_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":report_event_proto",
    ],
)

proto_library(
    name = "pair_proto",
    visibility = ["//visibility:public"],
    srcs = ["pair.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "pair_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":pair_proto",
    ],
)

python_proto_library(
    name = "pair_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":pair_proto",
    ],
)

proto_library(
    name = "module_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["module_event.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "module_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":module_event_proto",
    ],
)

python_proto_library(
    name = "module_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":module_event_proto",
    ],
)

proto_library(
    name = "header_proto",
    visibility = ["//visibility:public"],
    srcs = ["header.proto"],
    deps = [
        "//proto/common:error_code_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "header_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":header_proto",
    ],
)

python_proto_library(
    name = "header_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":header_proto",
    ],
)

proto_library(
    name = "geometry_proto",
    visibility = ["//visibility:public"],
    srcs = ["geometry.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "geometry_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":geometry_proto",
    ],
)

python_proto_library(
    name = "geometry_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":geometry_proto",
    ],
)

