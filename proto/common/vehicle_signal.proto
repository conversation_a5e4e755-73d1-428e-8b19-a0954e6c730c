syntax = "proto2";

package deeproute.common;

message VehicleSignal {
  enum TurnSignal {
    TURN_NONE = 0;
    TURN_LEFT = 1;
    TURN_RIGHT = 2;
  };

  enum AutoBeam {
    AUTO_BEAM_NO_REQUEST = 0;
    AUTO_BEAM_CLOSED = 1;
    AUTO_BEAM_OPEN = 2;
  };

  enum Reason {
    UNKNOWN = 0;
    TURN = 1;
    LANE_CHANGE = 2;
    NUDGE = 3;
    MERGE = 4;
    SPLIT = 5;
    PARKING = 6;
  };

  optional TurnSignal turn_signal = 1;
  // lights enable command
  optional bool high_beam = 2;
  optional bool low_beam = 3;
  optional bool horn = 4;
  optional bool emergency_light = 5;
  optional bool position = 6;
  optional AutoBeam auto_beam = 7;
  repeated Reason reason = 8;
}
