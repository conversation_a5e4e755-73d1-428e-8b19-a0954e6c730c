syntax = "proto2";

package deeproute.common;

// This is the engage advice that published by critical runtime modules.
message EngageAdvice {
  enum Advice {
    UNKNOWN = 0;
    DISALLOW_ENGAGE = 1;
    READY_TO_ENGAGE = 2;
    KEEP_ENGAGED = 3;
    PREPARE_DISENGAGE = 4;
  }

  optional Advice advice = 1 [default = DISALLOW_ENGAGE];
  optional string reason = 2;
}

message DrivingMode {
  enum Mode {
    DRIVING = 0;
    PARKING = 1;
  }
  optional Mode mode = 1;
}