syntax = "proto2";

package dr.common;

enum Module {
  UNKNOWN = 0;
  SENSOR_LIDAR = 1;
  SENSOR_USS = 2;
  SENSOR_INS = 3;
  SENSOR_RADAR = 4;
  MAP_ENGINE = 5;
  LOCK_ON_ROAD = 6;
  INS_ONLINE = 7;
  LOCALIZATION = 8 [deprecated = true];
  PERCEPTION = 9;
  PLANNING = 10;
  CANBUS = 11;
  BLC = 12;
  SAFETY = 13;
  STARTER = 14;
  PERF_COLLECTOR = 15;
  CHURCH = 16;
  SENTRY = 17;
  SENSOR_CAMERA = 18;
  LOCAL_ROUTING = 19;
  CONTROL = 20;
  ROUTING = 21;
  PARKING_ENTRY_DETECTION = 22 [deprecated = true];
  RTK_POSE_FORWARD = 23;
  AEB = 24;
  PERCEP_AEB = 25;
  LIDAR_MATCHING = 26;
  ONBOARD_MAPS = 27;
  DSM = 28;
  DEM = 29;
  PERCEP_AVM = 30 [deprecated = true];
  PERCEP_ADAS = 31;
  GPS_LOCALIZATION = 32;
  DIAGNOSTIC_APP = 33;
  AROUND_VIEW_MONITOR = 34;  // also called 'PERCEP_AVM'
  ADAS_ONLINE_CALIBRATOR = 35;
  OSBRIDGE = 36;
  VIZ_ANDROID = 37;
  VIZ_MAP = 38;
  DTS = 39;
  BSW = 40;
  KEY_MANAGER = 41;
  ROUDI = 42;
  ADAS_CALIB_MONITOR = 43;
  ADPT_CANBUS = 44;
  ADPT_DOWNSTREAM = 45;
  ADPT_UPSTREAM = 46;
  ADPT_SENSOR_INS = 47;
  ADPT_DOMAINV2 = 48;
  ADPT_DOMAINV3 = 49;
  SENSOR_INS_ONLINE = 50;
  INS_CALIBRATION = 51;
  DATA_AGENT = 52;
  UPLOADER = 53;
  SAFE_APP = 54;
  COMPLIANCER = 55;
  SD_ROUTING = 56;
  MP_SENTRY = 57;
  MPS_SERVER = 58;
  TOB_SAFETY_APP = 59;
  ADAS_INS_CALIBRATOR = 60;
  LOCALIZATION_MATCHING = 61;
  LIDAR_MANAGER = 62;
  ONLINE_RECORDER = 63;
  PBOX_MANAGER = 64;
  SENSOR_CAMERA_PANORAMIC = 65;
  DTA = 66;
  ONLINE_RECORDER_LIGHT = 67;
  ONLINE_RECORDER_MEDIUM = 68;
  ONLINE_RECORDER_HEAVY = 69;
  DRPKI = 70;
  PCAP_CAPTURE = 71;
  GRADING_ONBOARD = 72;
  BLACKBOX = 73;
  DEVICE_MANAGER = 74;
  ROUTINGD_V88 = 75;
  ROUTINGD_V168 = 76;
  BRIDGE_BLC = 77;
  BRIDGE_SAFETY = 78;
  MCU_LOG_ADAPTER = 79;
}

enum Event {
  NORMAL = 0;

  /* 1000~4999 CANBUS */
  /* 1000~1999 CABUS RUNNING CHECK */
  /* 2000~2499 CANBUS CONTROL CHECK */
  /* 2500~2999 CANBUS BLC CHECK */
  /* 3000~4999 CANBUS CHASSIS CHECK */
  CANBUS_ADS_CAN0_BUS_ERROR = 1000;
  CANBUS_ADS_CAN1_BUS_ERROR = 1001;
  CANBUS_ADS_RPC_VERSION_NOT_MATCHED = 1002;
  CANBUS_STARTUP_FAILED = 1003;
  CANBUS_INPUT_NO_CAN_FRAME = 1004;
  CANBUS_NO_CAR_STATE = 1005;
  CANBUS_CONTROL_CHECKER_ERROR = 1006;
  CANBUS_DM_CHECKER_ERROR = 1007;
  CANBUS_ADS_FRAME_CHECKER_STARTUP_ERROR = 1008;
  CANBUS_ADS_FRAME_CHECKER_RUNNING_ERROR = 1009;

  CANBUS_RUNNING_GEAR_REQ_RESP_NOT_MATCH = 1010;
  CANBUS_MCU_SOC2MCU_COMMUNICATION_LOST = 1011;
  CANBUS_RUNNING_AEB_LOST_HEARTBEAT = 1012;
  CANBUS_RUNNING_BLC_LOST_HEARTBEAT = 1013;
  CANBUS_RUNNING_CONTROL_LOST_HEARTBEAT = 1014;
  CANBUS_RUNNING_MCU_LOST_HEARTBEAT = 1015;

  CANBUS_CONTROL_STEERING_TARGET_OVERFLOW = 2000;
  CANBUS_CONTROL_STEERING_TARGET_RATE_OVERFLOW = 2001;
  CANBUS_CONTROL_TORQUE_OVERFLOW = 2002;
  CANBUS_CONTROL_TORQUE_RATE_OVERFLOW = 2003;
  CANBUS_CONTROL_LONGITUDINAL_CMD_OVERFLOW = 2004;
  CANBUS_CONTROL_LONGITUDINAL_CMD_DISTANCE_OVERFLOW = 2005;
  CANBUS_CONTROL_ACCELERATION_OVERFLOW = 2006;
  CANBUS_CONTROL_ACCELERATION_RATE_OVERFLOW = 2007;
  CANBUS_CONTROL_DECELERATION_OVERFLOW = 2008;
  CANBUS_CONTROL_DECELERATION_RATE_OVERFLOW = 2009;
  CANBUS_CONTROL_GEAR_LOCATION_INVALID = 2010;
  CANBUS_CONTROL_DRIVING_MODE_INVALID = 2011;
  CANBUS_CONTROL_UNEXPECTED_GEAR_REQ = 2012;
  CANBUS_CONTROL_APA_ACCELERATION_OVERFLOW = 2013;

  CANBUS_CHASSIS_GEAR_INVALID = 3000;
  CANBUS_CHASSIS_FRONT_WHEEL_ANGLE_OVERFLOW = 3001;
  CANBUS_CHASSIS_FRONT_WHEEL_ANGLE_RATE_OVERFLOW = 3002;
  CANBUS_CHASSIS_TORQUE_OVERFLOW = 3003;
  CANBUS_CHASSIS_VEHICLE_SPEED_INVALID = 3004;
  CANBUS_CHASSIS_FL_WHEEL_SPEED_INVALID = 3005;
  CANBUS_CHASSIS_FR_WHEEL_SPEED_INVALID = 3006;
  CANBUS_CHASSIS_RL_WHEEL_SPEED_INVALID = 3007;
  CANBUS_CHASSIS_RR_WHEEL_SPEED_INVALID = 3008;
  CANBUS_CHASSIS_FL_WHEEL_DRIVE_DIRECTION_INVALID = 3009;
  CANBUS_CHASSIS_FR_WHEEL_DRIVE_DIRECTION_INVALID = 3010;
  CANBUS_CHASSIS_RL_WHEEL_DRIVE_DIRECTION_INVALID = 3011;
  CANBUS_CHASSIS_RR_WHEEL_DRIVE_DIRECTION_INVALID = 3012;
  CANBUS_CHASSIS_ESP_FAULT = 3013;
  CANBUS_CHASSIS_ESP_ERROR = 3014;
  CANBUS_CHASSIS_ESP_TEMPORARY_FAILURE = 3015;
  CANBUS_CHASSIS_ESP_PERMANENTLY_FAILURE = 3016;
  CANBUS_CHASSIS_EPS_FAULT = 3017;
  CANBUS_CHASSIS_EPS_ERROR = 3018;
  CANBUS_CHASSIS_EPS_TEMPORARY_FAILURE = 3019;
  CANBUS_CHASSIS_EPS_PERMANENTLY_FAILURE = 3020;
  CANBUS_CHASSIS_SAS_FAULT = 3021;
  CANBUS_CHASSIS_SAS_ERROR = 3022;
  CANBUS_CHASSIS_SAS_TEMPORARY_FAILURE = 3023;
  CANBUS_CHASSIS_SAS_PERMANENTLY_FAILURE = 3024;
  CANBUS_CHASSIS_IBC_FAULT = 3025;
  CANBUS_CHASSIS_IBC_ERROR = 3026;
  CANBUS_CHASSIS_IBC_TEMPORARY_FAILURE = 3027;
  CANBUS_CHASSIS_IBC_PERMANENTLY_FAILURE = 3028;
  CANBUS_CHASSIS_THROTTLE_PEDAL_INVALID = 3029;
  CANBUS_CHASSIS_BRAKE_PEDAL_INVALID = 3030;
  CANBUS_CHASSIS_VEH_YAW_RATE_INVALID = 3031;
  CANBUS_CHASSIS_VEH_LAT_ACCEL_INVALID = 3032;
  CANBUS_CHASSIS_VEH_LGT_ACCEL_INVALID = 3033;
  CANBUS_CHASSIS_EPB_ERROR = 3034;
  CANBUS_CHASSIS_ACC_SWITCH_ERROR = 3035;
  CANBUS_CHASSIS_VEH_YAW_RATE_OVERFLOW = 3036;
  CANBUS_CHASSIS_VEH_LAT_ACCEL_OVERFLOW = 3037;
  CANBUS_CHASSIS_VEH_LGT_ACCEL_OVERFLOW = 3038;
  CANBUS_CHASSIS_VEHICLE_STANDSTILL_INVALID = 3039;
  CANBUS_CHASSIS_CDD_NOT_AVAILABLE = 3040;
  CANBUS_CHASSIS_ESP_DIAGNOSIS_ACTIVE = 3041;
  CANBUS_CHASSIS_ABS_PERMANENTLY_FAILURE = 3042;
  CANBUS_CHASSIS_EBD_PERMANENTLY_FAILURE = 3043;
  CANBUS_CHASSIS_TIRE_PRESSURE_PERMANENTLY_FAILURE = 3044;
  CANBUS_CHASSIS_CRASH_OUTPUT_DETECTED = 3045;
  CANBUS_CHASSIS_FRONT_WIPER_SWITCH_ERROR = 3046;
  CANBUS_CHASSIS_CTA_BRAKE_NOT_AVAILABLE = 3047;
  CANBUS_CHASSIS_ABP_NOT_AVAILABLE = 3048;
  CANBUS_CHASSIS_ABA_NOT_AVAILABLE = 3049;
  CANBUS_CHASSIS_AWB_NOT_AVAILABLE = 3050;
  CANBUS_CHASSIS_AEB_NOT_AVAILABLE = 3051;
  CANBUS_CHASSIS_APA_NOT_AVAILABLE = 3052;
  CANBUS_CHASSIS_MEB_NOT_AVAILABLE = 3053;
  CANBUS_CHASSIS_RPA_NOT_AVAILABLE = 3054;
  CANBUS_CHASSIS_EPS_AVAIL_TEMPORARY_FAILURE = 3055;
  CANBUS_CHASSIS_EPS_AVAIL_PERMANENTLY_FAILURE = 3056;
  CANBUS_CHASSIS_THROTTLE_PEDAL_OUT_OF_RANGE = 3057;

  CANBUS_CHASSIS_GWM_ESP_FUNC_STS_OFF = 3500;
  CANBUS_CHASSIS_GWM_EPS_INTERFER_DET_INVALID = 3501;
  CANBUS_CHASSIS_GWM_PRKG_CTRL_MOD_REQ_INVALID = 3502;
  CANBUS_CHASSIS_GWM_VCU_AUTO_PARKING_UNAVAILABLE = 3503;
  CANBUS_CHASSIS_GWM_BLE_NOT_CONNECTED = 3504;
  CANBUS_CHASSIS_GWM_BLE_NOT_IN_SECURITY_CERTIFICATION = 3505;
  CANBUS_CHASSIS_GWM_VIDEO_LOST = 3506;
  CANBUS_CHASSIS_GWM_IP_ACC_ERROR = 3507;

  /* 5000~9999 SENSORS */
  /* 5000~5499 LiDAR   */
  /* 5500~5999 GNSS    */
  /* 6000~6499 RADAR   */
  /* 6500~6999 USS     */
  /* 7000~7499 CAM     */
  /* 7500~9999 RESERVE   */
  LIDAR_INIT_SPEC_LOAD_FAIL = 5000;
  LIDAR_INIT_EXTRINSIC_LIDAR_NUM_WRONG = 5001;
  LIDAR_INIT_CONFIG_LOAD_FAIL = 5002;
  LIDAR_INIT_CONFIG_NO_LIDAR = 5003;
  LIDAR_INIT_STREAM_TYPE_WRONG = 5004;
  LIDAR_INIT_STREAM_CONNET_WRONG = 5005;
  LIDAR_INIT_DECODER_TYPE_WRONG = 5006;
  LIDAR_INIT_AT128_INTRINSIC_LOAD = 5007;
  LIDAR_INIT_ZVS_INTRINSIC_LOAD = 5008;
  LIDAR_INIT_INTRINSIC_NOT_ENOUGH = 5009;
  LIDAR_INIT_INTRINSIC_INVALID = 5010;
  LIDAR_INIT_START_CALIBRATION_MODE = 5011;
  LIDAR_INIT_START_DEBUG_MODE = 5012;
  LIDAR_INIT_START = 5013;
  LIDAR_INPUT_INVALID = 5100;
  LIDAR_INPUT_TIMEOUT = 5101;
  LIDAR_INPUT_READ_FAIL = 5102;
  LIDAR_INPUT_PKT_SYS_TIME_DIFF_TOO_LARGE = 5103;
  LIDAR_INPUT_PREVIOUS_FRAME = 5104;
  LIDAR_INPUT_PKT_TIME_DIFF_TOO_LARGE = 5105;
  LIDAR_INPUT_SYS_TIME_DIFF_TOO_LARGE = 5106;
  LIDAR_RUNTIME_CLOUD_OVERFLOW = 5200;
  LIDAR_RUNTIME_PROCESS_TIME_TOO_LONG = 5201;
  LIDAR_OUTPUT_CLOUD_NUM_OVERFLOW = 5300;
  LIDAR_OUTPUT_CLOUD_NUM_TOO_FEW = 5301;
  LIDAR_LOST_COMMUNICATION = 5400;
  LIDAR_ABNORMAL_FRAME_RATE = 5401;
  LIDAR_TIME_SYNCHRONISATION_FAULT = 5402;
  LIDAR_BLIND_FAULT = 5403;
  LIDAR_INTERNAL_FAILURE = 5404;
  LIDAR_E2E_CHECKSUM_ERROR = 5405;
  LIDAR_E2E_ROLLING_COUNTER_ERROR = 5406;
  LIDAR_ABNORMAL_TEMPERATURE = 5407;
  LIDAR_ABNORMAL_VOLTAGE = 5408;
  LIDAR_ABNORMAL_FRAME_JITTER = 5409;
  LIDAR_SN_MISMATCH = 5410;
  LIDAR_INTERNAL_READ_SN_ERROR = 5411;
  LIDAR_TIME_SYNCHRONISATION_FAULT_RESTART = 5412;

  GNSS_INIT_LOAD_CONFIG_FAIL = 5500;
  GNSS_INIT_LOAD_REGION_LIST_FAIL = 5501;
  GNSS_INIT_STREAM_TYPE_WRONG = 5502;
  GNSS_INIT_STREAM_CONNET_WRONG = 5503;
  GNSS_INIT_DECODER_TYPE_WRONG = 5504;
  GNSS_INIT_START_DEBUG_MODE = 5505;
  GNSS_INIT_START = 5506;
  GNSS_INIT_CONVERGE = 5507;
  GNSS_INPUT_INVALID_ERROR = 5600;
  GNSS_INPUT_TIMEOUT_ERROR = 5601;
  GNSS_INPUT_READ_FAIL_ERROR = 5602;
  GNSS_INPUT_RAW_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE_ERROR = 5603;
  GNSS_INPUT_RAW_IMU_PKT_SYS_TIME_DIFF_TOO_LARGE_ERROR = 5604;
  GNSS_INPUT_RAW_VEL_PKT_SYS_TIME_DIFF_TOO_LARGE_ERROR = 5605;
  GNSS_INPUT_RAW_POSE_PKT_TIME_DIFF_TOO_LARGE_WARN = 5606;
  GNSS_INPUT_RAW_POSE_PKT_TIME_DIFF_TOO_SMALL_WARN = 5607;
  GNSS_INPUT_RAW_POSE_SYS_TIME_DIFF_TOO_LARGE_WARN = 5608;
  GNSS_INPUT_RAW_POSE_SYS_TIME_DIFF_TOO_SMALL_WARN = 5609;
  GNSS_INPUT_RAW_IMU_PKT_TIME_DIFF_TOO_LARGE_WARN = 5610;
  GNSS_INPUT_RAW_IMU_PKT_TIME_DIFF_TOO_SMALL_WARN = 5611;
  GNSS_INPUT_RAW_IMU_SYS_TIME_DIFF_TOO_LARGE_WARN = 5612;
  GNSS_INPUT_RAW_IMU_SYS_TIME_DIFF_TOO_SMALL_WARN = 5613;
  GNSS_INPUT_RAW_VEL_PKT_TIME_DIFF_TOO_LARGE_WARN = 5614;
  GNSS_INPUT_RAW_VEL_PKT_TIME_DIFF_TOO_SMALL_WARN = 5615;
  GNSS_INPUT_RAW_VEL_SYS_TIME_DIFF_TOO_LARGE_WARN = 5616;
  GNSS_INPUT_RAW_VEL_SYS_TIME_DIFF_TOO_SMALL_WARN = 5617;
  GNSS_RUNTIME_PROCESS_TIME_TOO_LONG_WARN = 5700;
  GNSS_OUTPUT_RAW_POSE_SOLUTION_STATUS_ABNORMAL = 5800;
  GNSS_OUTPUT_RAW_POSE_POSITION_TYPE_ABNORMAL = 5801;
  GNSS_OUTPUT_RAW_VEL_SOLUTION_STATUS_ABNORMAL = 5802;
  GNSS_OUTPUT_RAW_VEL_VELOCITY_TYPE_ABNORMAL = 5803;

  DSV_FR_LOST_COMMUNICATION = 6000;
  FR_ABNORMAL_FRAME_RATE = 6001;
  FR_BLIND_FAULT = 6002;
  FR_INTERNAL_FAILURE = 6003;
  DSV_FR_TIME_SYNCHRONIZATION_FAULT = 6004;  // FR: front radar
  DSV_CR_RL_LOST_COMMUNICATION = 6100;
  CR_RL_ABNORMAL_FRAME_RATE = 6101;
  CR_RL_BLIND_FAULT = 6102;
  CR_RL_INTERNAL_FAILURE = 6103;
  DSV_CR_RL_TIME_SYNCHRONIZATION_FAULT = 6104;  // CR: corner radar RL: rear
                                                // left
  DSV_CR_RR_LOST_COMMUNICATION = 6200;
  CR_RR_ABNORMAL_FRAME_RATE = 6201;
  CR_RR_BLIND_FAULT = 6202;
  CR_RR_INTERNAL_FAILURE = 6203;
  DSV_CR_RR_TIME_SYNCHRONIZATION_FAULT = 6204;

  DSV_CR_FL_LOST_COMMUNICATION = 6300;  // CR: corner radar FL: front left
  CR_FL_ABNORMAL_FRAME_RATE = 6301;

  DSV_CR_FR_LOST_COMMUNICATION = 6400;
  CR_FR_ABNORMAL_FRAME_RATE = 6401;  // CR: corner radar FR: front right

  DSV_PDC_LOST_COMMUNICATION = 6500;
  PDC_ABNORMAL_FRAME_RATE = 6501;
  PDC_INTERNAL_FAILURE_APASYS = 6502;
  PDC_INTERNAL_FAILURE_RPASYS = 6503;
  PDC_INTERNAL_FAILURE_FPASYS = 6504;
  DSV_PDC_TIME_SYNCHRONIZATION_FAULT = 6505;
  PDC_INTERNAL_FAILURE_APASYS_DISABLE = 6506;

  CAMERA_DEBUG_INFO = 7000;
  CAMERA_LATENCY_ERROR = 7001;
  CAMERA_TIMEOUT = 7002;
  CAMERA_FRONT_STARTUP_ERR = 7100;
  CAMERA_FRONT_STARTUP_WARN = 7101;
  CAMERA_FRONT_ONLINE_ERR = 7102;
  CAMERA_FRONT_ONLINE_WARN = 7103;
  CAMERA_SIDE_FRONT_STARTUP_ERR = 7104;
  CAMERA_SIDE_FRONT_STARTUP_WARN = 7105;
  CAMERA_SIDE_FRONT_ONLINE_ERR = 7106;
  CAMERA_SIDE_FRONT_ONLINE_WARN = 7107;
  CAMERA_REAR_STARTUP_ERR = 7108;
  CAMERA_REAR_STARTUP_WARN = 7109;
  CAMERA_REAR_ONLINE_ERR = 7110;
  CAMERA_REAR_ONLINE_WARN = 7111;
  CAMERA_SIDE_REAR_STARTUP_ERR = 7112;
  CAMERA_SIDE_REAR_STARTUP_WARN = 7113;
  CAMERA_SIDE_REAR_ONLINE_ERR = 7114;
  CAMERA_SIDE_REAR_ONLINE_WARN = 7115;
  CAMERA_PANORAMIC_STARTUP_ERR = 7116;
  CAMERA_PANORAMIC_STARTUP_WARN = 7117;
  CAMERA_PANORAMIC_ONLINE_ERR = 7118;
  CAMERA_PANORAMIC_ONLINE_WARN = 7119;
  CAMERA_PANORAMIC_FRONT_STARTUP_ERR = 7120;
  CAMERA_PANORAMIC_FRONT_STARTUP_WARN = 7121;
  CAMERA_PANORAMIC_FRONT_ONLINE_ERR = 7122;
  CAMERA_PANORAMIC_FRONT_ONLINE_WARN = 7123;
  CAMERA_PANORAMIC_LEFT_STARTUP_ERR = 7124;
  CAMERA_PANORAMIC_LEFT_STARTUP_WARN = 7125;
  CAMERA_PANORAMIC_LEFT_ONLINE_ERR = 7126;
  CAMERA_PANORAMIC_LEFT_ONLINE_WARN = 7127;
  CAMERA_PANORAMIC_RIGHT_STARTUP_ERR = 7128;
  CAMERA_PANORAMIC_RIGHT_STARTUP_WARN = 7129;
  CAMERA_PANORAMIC_RIGHT_ONLINE_ERR = 7130;
  CAMERA_PANORAMIC_RIGHT_ONLINE_WARN = 7131;
  CAMERA_PANORAMIC_REAR_STARTUP_ERR = 7132;
  CAMERA_PANORAMIC_REAR_STARTUP_WARN = 7133;
  CAMERA_PANORAMIC_REAR_ONLINE_ERR = 7134;
  CAMERA_PANORAMIC_REAR_ONLINE_WARN = 7135;
  CAMERA_FRONT_NARROW_STARTUP_ERR = 7136;
  CAMERA_FRONT_NARROW_STARTUP_WARN = 7137;
  CAMERA_FRONT_NARROW_ONLINE_ERR = 7138;
  CAMERA_FRONT_NARROW_ONLINE_WARN = 7139;
  CAMERA_FRONT_WIDE_STARTUP_ERR = 7140;
  CAMERA_FRONT_WIDE_STARTUP_WARN = 7141;
  CAMERA_FRONT_WIDE_ONLINE_ERR = 7142;
  CAMERA_FRONT_WIDE_ONLINE_WARN = 7143;

  CAMERA_SOC1_SENSOR_FATAL = 7200;
  CAMERA_SOC1_SENSOR_RECOVERED_AFTER_FATAL = 7201;
  CAMERA_SOC1_SENSOR_HEARTBEAT_LOSS = 7202;

  /* 10000~19999 LOCALIZATION&MAPPING */
  /* 10000~10499 INS-ONLINE   */
  /* 10500~10999 GPS-LOCALIZATION   */
  /* 11000~11999 MAP-ENGINE   */
  /* 12000~12499 LOCK-ON-ROAD   */
  /* 12500~12999 LOCALIZATION-MATCHING   */
  /* 13000~13499 ONBOARD-MAPS  */
  /* 13500~19999 RESERVE   */

  INS_ONLINE_INIT_LOAD_MSF_CONFIG_FAILED = 10000;
  INS_ONLINE_INIT_LOAD_CAR_CONFIG_FAILED = 10001;
  INS_ONLINE_INIT_LOAD_SENSOR_CONFIG_FAIL = 10002;
  INS_ONLINE_INIT_STREAM_CONNET_WRONG = 10003;
  INS_ONLINE_INIT_CONVERGE = 10004;
  INS_ONLINE_INIT_START_DEBUG_MODE = 10005;
  INS_ONLINE_INIT_START = 10006;
  INS_ONLINE_INIT_GNSS_SIGNAL_LOCK = 10007;
  INS_ONLINE_INIT_ALIGNMENT_NOT_ENOUGH_IMU_INPUT = 10008;
  INS_ONLINE_INIT_ALIGNMENT_NOT_ENOUGH_VEL_INPUT = 10009;
  INS_ONLINE_INIT_ALIGNMENT_EVALUATE_GNSSVEL_FAIL = 10010;
  INS_ONLINE_INIT_ALIGNMENT_EVALUATE_WHEELSPEED_FAIL = 10011;
  INS_ONLINE_INIT_ALIGNMENT_CLEAR_HISTORICAL_WHEELSPEED = 10012;
  INS_ONLINE_INIT_ALIGNMENT_SUCCESS = 10013;
  INS_ONLINE_INPUT_IMU_PKT_SYS_TIME_DIFF_TOO_LARGE = 10100;
  INS_ONLINE_INPUT_IMU_PKT_TIME_DIFF_TOO_LARGE = 10101;
  INS_ONLINE_INPUT_IMU_PKT_TIME_DIFF_TOO_SMALL = 10102;
  INS_ONLINE_INPUT_IMU_SYS_TIME_DIFF_TOO_LARGE = 10103;
  INS_ONLINE_INPUT_IMU_SYS_TIME_DIFF_TOO_SMALL = 10104;
  INS_ONLINE_INPUT_WHEEL_SPEED_PKT_SYS_TIME_DIFF_TOO_LARGE = 10105;
  INS_ONLINE_INPUT_WHEEL_SPEED_PKT_TIME_DIFF_TOO_LARGE = 10106;
  INS_ONLINE_INPUT_WHEEL_SPEED_PKT_TIME_DIFF_TOO_SMALL = 10107;
  INS_ONLINE_INPUT_WHEEL_SPEED_SYS_TIME_DIFF_TOO_LARGE = 10108;
  INS_ONLINE_INPUT_WHEEL_SPEED_SYS_TIME_DIFF_TOO_SMALL = 10109;
  INS_ONLINE_INPUT_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 10110;
  INS_ONLINE_INPUT_POSE_PKT_TIME_DIFF_TOO_LARGE = 10111;
  INS_ONLINE_INPUT_POSE_PKT_TIME_DIFF_TOO_SMALL = 10112;
  INS_ONLINE_INPUT_POSE_SYS_TIME_DIFF_TOO_LARGE = 10113;
  INS_ONLINE_INPUT_POSE_SYS_TIME_DIFF_TOO_SMALL = 10114;
  INS_ONLINE_INPUT_VEL_PKT_SYS_TIME_DIFF_TOO_LARGE = 10115;
  INS_ONLINE_INPUT_VEL_PKT_TIME_DIFF_TOO_LARGE = 10116;
  INS_ONLINE_INPUT_VEL_PKT_TIME_DIFF_TOO_SMALL = 10117;
  INS_ONLINE_INPUT_VEL_SYS_TIME_DIFF_TOO_LARGE = 10118;
  INS_ONLINE_INPUT_VEL_SYS_TIME_DIFF_TOO_SMALL = 10119;
  INS_ONLINE_INPUT_IMU_TIME_DIFF_EXCEED_ODD = 10120;
  INS_ONLINE_INPUT_IMU_TIME_REVERSE = 10121;
  INS_ONLINE_INPUT_IMU_DATA_INVALID = 10150;
  INS_ONLINE_INPUT_WHEEL_SPEED_DATA_INVALID = 10151;
  INS_ONLINE_INPUT_POSE_DATA_INVALID = 10152;
  INS_ONLINE_INPUT_VEL_DATA_INVALID = 10153;
  INS_ONLINE_INPUT_IMU_SIX_AXIS_DATA_ALL_ZERO = 10154;
  INS_ONLINE_INPUT_IMU_TIMESTAMP_ROLLBACK = 10155;
  INS_ONLINE_INPUT_POSE_TIMESTAMP_ROLLBACK = 10156;
  INS_ONLINE_INPUT_VEL_TIMESTAMP_ROLLBACK = 10157;
  INS_ONLINE_RUNTIME_DECODER_PROCESS_TIME_TOO_LONG = 10200;
  INS_ONLINE_RUNTIME_DECODER_PKT_OVERFLOW = 10201;
  INS_ONLINE_RUNTIME_MSF_PROCESS_TIME_TOO_LONG = 10202;
  INS_ONLINE_INTERNAL_ACCE_BIAS_TOO_LARGE = 10250;
  INS_ONLINE_INTERNAL_GYRO_BIAS_TOO_LARGE = 10251;
  INS_ONLINE_INTERNAL_WHEEL_SPEED_SCALE_TOO_LARGE = 10252;
  INS_ONLINE_INTERNAL_WHEEL_PULSE_SCALE_TOO_LARGE = 10253;
  INS_ONLINE_INTERNAL_WHEEL_SPEED_LOST_TOO_LONG = 10254;

  INS_ONLINE_OUTPUT_POSE_ABNORMAL = 10300;

  INS_ONLINE_INMU_LOST_COMMUNICATION = 10400;
  INS_ONLINE_INMU_TIME_SYNCHRONISATION_FAULT = 10401;
  INS_ONLINE_INMU_ABNORMAL_FRAME_RATE = 10402;
  INS_ONLINE_INMU_INTERNAL_FAILURE = 10403;
  INS_ONLINE_INMU_INTERNAL_FAILURE_GNSS = 10404;

  GPS_LOCALIZATION_INIT_LOAD_MSF_CONFIG_FAILED = 10500;
  GPS_LOCALIZATION_INIT_LOAD_NAVI_CONFIG_FAILED = 10501;
  GPS_LOCALIZATION_INIT_NOT_START = 10502;
  GPS_LOCALIZATION_INIT_CONVERGING = 10503;
  GPS_LOCALIZATION_INIT_START = 10504;
  GPS_LOCALIZATION_INIT_PERFORMANCE_INFO = 10505;
  GPS_LOCALIZATION_INPUT_GPS_TIME_REVERSE = 10600;
  GPS_LOCALIZATION_INPUT_GPS_TIME_SYS_TIME_DIFF_TOO_LARGE = 10601;
  GPS_LOCALIZATION_INPUT_GPS_VALUE_ILLEGAL = 10602;
  GPS_LOCALIZATION_INPUT_GPS_VALUE_ABNORMAL = 10603;
  GPS_LOCALIZATION_RUNTIME_PROCESS_IMU_TIME_TOO_LONG = 10700;
  GPS_LOCALIZATION_RUNTIME_PROCESS_IMU_WHEEL_SPEED_TIME_TOO_LONG = 10701;
  GPS_LOCALIZATION_RUNTIME_PROCESS_GNSS_TIME_TOO_LONG = 10702;
  GPS_LOCALIZATION_INTERNAL_GYRO_BIAS_TOO_LARGE = 10800;
  GPS_LOCALIZATION_INTERNAL_ACCE_BIAS_TOO_LARGE = 10801;
  GPS_LOCALIZATION_OUTPUT_MEASUREMENT_TIME_TOO_SMALL = 10900;
  GPS_LOCALIZATION_OUTPUT_MEASUREMENT_TIME_DIFF_LARGE = 10901;
  GPS_LOCALIZATION_OUTPUT_ATTITUDE_ABNORMAL = 10902;
  GPS_LOCALIZATION_OUTPUT_VELOCITY_ABNORMAL = 10903;

  MAP_ENGINE_INIT_LOAD_PROJECTION_FAILED = 11000;
  MAP_ENGINE_INIT_LOAD_CAR_CONFIG_FAILED = 11001;
  MAP_ENGINE_INIT_START = 11002;
  MAP_ENGINE_LOAD_SDK_CONFIG_FAILED = 11003;
  MAP_ENGINE_INPUT_RASMAP_PKT_SYS_TIME_DIFF_TOO_LARGE = 11100;
  MAP_ENGINE_INPUT_RASMAP_PKT_TIME_DIFF_TOO_LARGE = 11101;
  MAP_ENGINE_INPUT_RASMAP_PKT_TIME_DIFF_TOO_SMALL = 11102;
  MAP_ENGINE_INPUT_RASMAP_SYS_TIME_DIFF_TOO_LARGE = 11103;
  MAP_ENGINE_INPUT_RASMAP_SYS_TIME_DIFF_TOO_SMALL = 11104;
  MAP_ENGINE_INPUT_LANE_INDEX_PKT_SYS_TIME_DIFF_TOO_LARGE = 11105;
  MAP_ENGINE_INPUT_LANE_INDEX_PKT_TIME_DIFF_TOO_LARGE = 11106;
  MAP_ENGINE_INPUT_LANE_INDEX_PKT_TIME_DIFF_TOO_SMALL = 11107;
  MAP_ENGINE_INPUT_LANE_INDEX_SYS_TIME_DIFF_TOO_LARGE = 11108;
  MAP_ENGINE_INPUT_LANE_INDEX_SYS_TIME_DIFF_TOO_SMALL = 11109;
  MAP_ENGINE_INPUT_LOCK_ON_ROAD_INVALID = 11150;
  MAP_ENGINE_INPUT_RAS_MAP_INVALID = 11151;
  MAP_ENGINE_INPUT_LANE_INDEX_INVALID = 11152;
  MAP_ENGINE_INPUT_ROUTING_INVALID = 11153;
  MAP_ENGINE_INPUT_LINK_INDEX_INVALID = 11154;
  MAP_ENGINE_INPUT_GNSS_POSE_INVALID = 11155;
  MAP_ENGINE_RUNTIME_PROCESS_LOCAL_ROUTING_TIME_TOO_LONG = 11200;
  MAP_ENGINE_RUNTIME_PROCESS_HORIZON_MAP_TIME_TOO_LONG = 11201;
  MAP_ENGINE_RUNTIME_BEGIN_MERGE = 11202;
  MAP_ENGINE_RUNTIME_BEGIN_EXIT = 11203;
  MAP_ENGINE_RUNTIME_ENTER_CROSSING = 11204;
  MAP_ENGINE_RUNTIME_OUT_CROSSING = 11205;
  MAP_ENGINE_RUNTIME_ROUTE_WAY_POINT_INVALID = 11206;
  MAP_ENGINE_RUNTIME_ROUTE_CALC_DIST_ERROR = 11207;
  MAP_ENGINE_RUNTIME_FETCH_SEMANTIC_MAP_INVALID = 11208;
  MAP_ENGINE_RUNTIME_ROAD_MODEL_INVALID = 11209;
  MAP_ENGINE_RUNTIME_EGO_LANE_INVALID = 11210;
  MAP_ENGINE_RUNTIME_GRAPH_MATCH_INFO = 11211;  // Graph match info event
  // GraphMatchInfoEvent from graph_match/graph_match_event.proto
  MAP_ENGINE_RUNTIME_QUERY_SD_MAP_ERROR = 11212;
  MAP_ENGINE_RUNTIME_QUERY_SD_MAP_NOT_RETURNED = 11213;


  MAP_ENGINE_RUNTIME_PASSABLE_MASK_FN = 11214; // deprecated
  ROUTING_PASSABLE_MASK_FN = 11215; // Passable model shadow mode event
  MAP_ENGINE_RUNTIME_EGO_LINK_NOT_FOUND = 11216; // Error!!!Lor is valid but ego link is not in sdmap
  
  MAP_ENGINE_OUTPUT_PRIORITY_JUMP = 11300;
  MAP_ENGINE_OUTPUT_HORIZON_MAP_INVALID = 11301;

  // Data for sdmap crowd source.
  MAP_ENGINE_OUTPUT_LANE_NUM_INFO = 11400;
  MAP_ENGINE_OUTPUT_LINK_EXPERIENCE_SPEED_INFO = 11401;
  MAP_ENGINE_OUTPUT_OBSTACLE_DATA = 11402;
  MAP_ENGINE_OUTPUT_MP_INFO = 11403;
  MAP_ENGINE_OUTPUT_LANE_TOPO_INFO = 11404;
  MAP_ENGINE_OUTPUT_AMAP_INFO = 11405;
  MAP_ENGINE_OUTPUT_LANE_MARKER_INFO = 11406;
  MAP_ENGINE_OUTPUT_TRAFFIC_LIGHT_INFO = 11407;
  MAP_ENGINE_OUTPUT_LANE_ATTRIBUTE_INFO = 11408;
  MAP_ENGINE_OUTPUT_AMAP_TRAFFIC_LIGHT_INFO = 11409;
  MAP_ENGINE_OUTPUT_AMAP_ROAD_CLASS_INFO = 11410;
  MAP_ENGINE_OUTPUT_SPEED_LIMIT_SIGN_INFO = 11411;
  MAP_ENGINE_OUTPUT_RASMAP_INFO = 11412;
  MAP_ENGINE_OUTPUT_2D_PERCEPTION_INFO = 11413;
  MAP_ENGINE_OUTPUT_MANUAL_SPEED_REGULATION_INFO = 11414;
  MAP_ENGINE_OUTPUT_AMAP_CAMERA_INFO = 11415;
  MAP_ENGINE_OUTPUT_LANE_BOUNDARY_INFO = 11416;
  MAP_ENGINE_OUTPUT_DATA_COLLECTION_TIME_POINT = 11417;
  MAP_ENGINE_OUTPUT_TUNNEL_TSR_INFO = 11418;

  SD_ROUTING_INIT_START = 11500;
  SD_ROUTING_INIT_FAILED = 11501;
  SD_ROUTING_RUNTIME_SD_VERSION_NOT_READY = 11502;
  SD_ROUTING_RUNTIME_QUERY_SD_VERSION_ERROR = 11503;

  LOCK_ON_ROAD_INIT_PROJECTION_FAILED = 12000;
  LOCK_ON_ROAD_INIT_LOAD_CAR_CONFIG_FAILED = 12001;
  LOCK_ON_ROAD_INIT_START = 12002;
  LOCK_ON_ROAD_INIT_DDMM_MODEL_NOT_FOUND = 12003;
  LOCK_ON_ROAD_INIT_DDMM_MODEL_LOAD_FAILED = 12004;
  LOCK_ON_ROAD_INIT_DDMM_NO_TUNE_FILE_FOUND = 12005;
  LOCK_ON_ROAD_INIT_DDMM_LOAD_MODEL_CONFIG_FAILED = 12006;
  LOCK_ON_ROAD_INIT_DDMM_NO_CALIBRATION_FILE_FOUND = 12007;
  LOCK_ON_ROAD_INIT_DDMM_ENGINE_INIT_FAILED = 12008;
  LOCK_ON_ROAD_INPUT_RASMAP_PKT_SYS_TIME_DIFF_TOO_LARGE = 12100;
  LOCK_ON_ROAD_INPUT_RASMAP_PKT_TIME_DIFF_TOO_LARGE = 12101;
  LOCK_ON_ROAD_INPUT_RASMAP_PKT_TIME_DIFF_TOO_SMALL = 12102;
  LOCK_ON_ROAD_INPUT_RASMAP_SYS_TIME_DIFF_TOO_LARGE = 12103;
  LOCK_ON_ROAD_INPUT_RASMAP_SYS_TIME_DIFF_TOO_SMALL = 12104;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 12105;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_PKT_TIME_DIFF_TOO_LARGE = 12106;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_PKT_TIME_DIFF_TOO_SMALL = 12107;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_SYS_TIME_DIFF_TOO_LARGE = 12108;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_SYS_TIME_DIFF_TOO_SMALL = 12109;
  LOCK_ON_ROAD_INPUT_RAS_MAP_INVALID = 12150;
  LOCK_ON_ROAD_INPUT_GLOBAL_POSE_INVALID = 12151;
  LOCK_ON_ROAD_INPUT_ODOMETRY_INVALID = 12152;
  LOCK_ON_ROAD_INPUT_ROUTING_INVALID = 12153;
  LOCK_ON_ROAD_INPUT_EGO_LANE_INVALID = 12154;
  LOCK_ON_ROAD_RUNTIME_PROCESS_RAS_MAP_TIME_TOO_LONG = 12200;
  LOCK_ON_ROAD_RUNTIME_PROCESS_GLOBAL_POSE_TIME_TOO_LONG = 12201;
  LOCK_ON_ROAD_RUNTIME_PROCESS_ROUTING_TIME_TOO_LONG = 12202;
  LOCK_ON_ROAD_RUNTIME_DDMM_PERFORMANCE_POOR = 12203;
  LOCK_ON_ROAD_RUNTIME_DDMM_ACQUIRE_SDMAP_FAILED = 12204;
  LOCK_ON_ROAD_RUNTIME_DDMM_PROCESS_SDMAP_FAILED = 12205;
  LOCK_ON_ROAD_RUNTIME_DDMM_INPUT_DATA_INVALID = 12206;
  LOCK_ON_ROAD_RUNTIME_LINK_PROPERTIES = 12207;
  LOCK_ON_ROAD_RUNTIME_NAVIGATION_SOURCE_READY_TIME = 12208;
  LOCK_ON_ROAD_OUTPUT_LANE_INDEX_JUMP = 12300;
  LOCK_ON_ROAD_OUTPUT_DDMM_RAW_RESULT_INVALID = 12301;
  LOCK_ON_ROAD_OUTPUT_DDMM_POST_PROCESS_RESULT_INVALID = 12302;
  LOCK_ON_ROAD_OUTPUT_DDMM_POST_PROCESS_RESULT_JUMPS = 12303;
  LOCK_ON_ROAD_OUTPUT_GNSS_STATUS_ABNORMAL = 12304;

  // LOCALIZATION_MATCHING 12500 ~ 12599
  // clang-format off
  LOCALIZATION_MATCHING_INIT_LOAD_CONFIG_FAILED = 12500;
  LOCALIZATION_MATCHING_INIT_LOAD_VEHICLE_CONFIG_FAILED = 12501;
  LOCALIZATION_MATCHING_ACTIVATE = 12502;
  LOCALIZATION_MATCHING_DEACTIVATE = 12503;

  LOCALIZATION_MATCHING_INPUT_LOCAL_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 12601;
  LOCALIZATION_MATCHING_INPUT_LOCAL_POSE_PKT_TIME_DIFF_TOO_LARGE = 12602;
  LOCALIZATION_MATCHING_INPUT_LOCAL_POSE_PKT_TIME_DIFF_TOO_SMALL = 12603;
  LOCALIZATION_MATCHING_INPUT_LOCAL_POSE_DATA_INVALID = 12604;
  LOCALIZATION_MATCHING_INPUT_POINTCLOUD_PKT_SYS_TIME_DIFF_TOO_LARGE = 12605;
  LOCALIZATION_MATCHING_INPUT_POINTCLOUD_PKT_TIME_DIFF_TOO_LARGE = 12606;
  LOCALIZATION_MATCHING_INPUT_POINTCLOUD_PKT_TIME_DIFF_TOO_SMALL = 12607;
  LOCALIZATION_MATCHING_INPUT_POINTCLOUD_DATA_INVALID = 12608;
  LOCALIZATION_MATCHING_INPUT_OBJECTS_PKT_SYS_TIME_DIFF_TOO_LARGE = 12609;
  LOCALIZATION_MATCHING_INPUT_OBJECTS_PKT_TIME_DIFF_TOO_LARGE = 12610;
  LOCALIZATION_MATCHING_INPUT_OBJECTS_PKT_TIME_DIFF_TOO_SMALL = 12611;
  LOCALIZATION_MATCHING_INPUT_OBJECTS_DATA_INVALID = 12612;
  LOCALIZATION_MATCHING_INPUT_COMMAND_PKT_SYS_TIME_DIFF_TOO_LARGE = 12613;
  LOCALIZATION_MATCHING_INPUT_COMMAND_INVALID = 12614;
  LOCLIAZATION_MATCHING_INPUT_KEYFRAME_UPDATE_MSG_PKT_SYS_TIME_DIFF_TOO_LARGE = 12615;
  LOCLIAZATION_MATCHING_INPUT_KEYFRAME_UPDATE_MSG_INVALID = 12616;

  LOCLIAZATION_MATCHING_PROCESS_POINTCLOUD_TOO_LONG = 12700;
  LOCALIZATION_MATCHING_PROCESS_POINTCLOUD_FAIL_TO_GET_POSE = 12701;
  LOCALIZATION_MATCHING_PROCESS_POINTCLOUD_FAIL_TO_GET_OBJECT = 12702;

  LOCALIZATION_MATCHING_OUTPUT_MATCHING_DEGENERATION = 12801;
  LOCALIZATION_MATCHING_OUTPUT_MATCHING_PKT_SYS_TIME_DIFF_TOO_LARGE = 12802;
  LOCALIZATION_MATCHING_OUTPUT_MATCHING_DATA_INVALID = 12803;
  // clang-format on

  // ONBOARD_MAPS 13000 ~ 13499
  // INIT 13000 ~ 13049
  ONBOARD_MAPS_INIT_LOAD_MODULE_CONFIG_FAILED = 13000;
  ONBOARD_MAPS_INIT_LOAD_VEHICLE_CONFIG_FAILED = 13001;
  ONBOARD_MAPS_INIT_LOAD_MAPS_FAILED = 13002;
  ONBOARD_MAPS_INIT_CREATE_MAP_FAILED = 13003;
  ONBOARD_MAPS_INIT_START = 13004;
  // INPUT_CHECK 13050 ~ 13149
  ONBOARD_MAPS_INPUT_GNSS_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 13050;
  ONBOARD_MAPS_INPUT_GNSS_POSE_PKT_TIME_DIFF_TOO_LARGE = 13051;
  ONBOARD_MAPS_INPUT_GNSS_POSE_PKT_TIME_DIFF_TOO_SMALL = 13052;
  ONBOARD_MAPS_INPUT_GNSS_POSE_DATA_INVALID = 13053;
  ONBOARD_MAPS_INPUT_LOCAL_POSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 13054;
  ONBOARD_MAPS_INPUT_LOCAL_POSE_PKT_TIME_DIFF_TOO_LARGE = 13055;
  ONBOARD_MAPS_INPUT_LOCAL_POSE_PKT_TIME_DIFF_TOO_SMALL = 13056;
  ONBOARD_MAPS_INPUT_LOCAL_POSE_DATA_INVALID = 13057;
  ONBOARD_MAPS_INPUT_POINTCLOUD_PKT_SYS_TIME_DIFF_TOO_LARGE = 13058;
  ONBOARD_MAPS_INPUT_POINTCLOUD_PKT_TIME_DIFF_TOO_LARGE = 13059;
  ONBOARD_MAPS_INPUT_POINTCLOUD_PKT_TIME_DIFF_TOO_SMALL = 13060;
  ONBOARD_MAPS_INPUT_POINTCLOUD_DATA_INVALID = 13061;
  ONBOARD_MAPS_INPUT_RASMAP_PKT_SYS_TIME_DIFF_TOO_LARGE = 13062;
  ONBOARD_MAPS_INPUT_RASMAP_PKT_TIME_DIFF_TOO_LARGE = 13063;
  ONBOARD_MAPS_INPUT_RASMAP_PKT_TIME_DIFF_TOO_SMALL = 13064;
  ONBOARD_MAPS_INPUT_RASMAP_DATA_INVALID = 13065;
  ONBOARD_MAPS_INPUT_OBJECTS_PKT_SYS_TIME_DIFF_TOO_LARGE = 13066;
  ONBOARD_MAPS_INPUT_OBJECTS_PKT_TIME_DIFF_TOO_LARGE = 13067;
  ONBOARD_MAPS_INPUT_OBJECTS_PKT_TIME_DIFF_TOO_SMALL = 13068;
  ONBOARD_MAPS_INPUT_OBJECTS_DATA_INVALID = 13069;
  ONBOARD_MAPS_INPUT_COMMAND_PKT_SYS_TIME_DIFF_TOO_LARGE = 13070;
  ONBOARD_MAPS_INPUT_COMMAND_INVALID = 13071;
  // MAPPING  13150 ~ 13249
  ONBOARD_MAPS_MAPPING_CREATE_UNKOWN_FAILURE = 13150;
  ONBOARD_MAPS_MAPPING_PROCESS_TOO_LONG = 13151;
  ONBOARD_MAPS_MAPPING_UPDATE_FAILURE = 13152;
  // MATCHING 13250 ~ 13349
  ONBOARD_MAPS_MATCHING_LOAD_MAP_FAILURE = 13250;
  ONBOARD_MAPS_MATCHING_TOO_LONG = 13251;
  ONBOARD_MAPS_MATCHING_LOCALIZATION_LOST = 13252;
  ONBOARD_MAPS_MATCHING_STATUS_JITTER = 13253;
  // OUTPUT 13350 ~ 13449
  ONBOARD_MAPS_EVENT_PKT_SYS_TIME_DIFF_TOO_LARGE = 13350;
  ONBOARD_MAPS_EVENT_DATA_INVALID = 13351;
  ONBOARD_MAPS_CMD_RESPONSE_PKT_SYS_TIME_DIFF_TOO_LARGE = 13352;
  ONBOARD_MAPS_CMD_RESPONSE_INVALID = 13353;
  ONBOARD_MAPS_MATCHING_STATUS_PKT_SYS_TIME_DIFF_TOO_LARGE = 13354;
  ONBOARD_MAPS_MATCHING_STATUS_POSE_INVALID = 13355;
  ONBOARD_MAPS_MATCHING_STATUS_EGO_LANE_ID_INVALID = 13356;
  // RESERVE 13450 ~ 13499

  /* 20000~29999 PERCEPTION */
  // ****** 以下为感知模块错误码定义 ******
  // *** 20000 - 20999 ***
  // 感知启动相关（包含成功/失败）。错误码字符均以“PERCEPTION_INIT_FAIL_” 开头

  // [INFO] 感知成功启动运行，无任何报错/异常
  PERCEPTION_INIT_SUCCESS = 20000;

  // 预留 20001 - 20100

  // ** 20101 - 20199 **
  // 配置项缺失/加载失败，导致的感知启动失败

  // [FATAL] 感知加载自身配置参数检查出错，导致感知无法启动
  // Metadata：
  // 以 kEventKeyConfigName为KEY，Value为无法加载的config的绝对路径。
  // 从而标明该错误是由自身配置项导致，还是子模块的后处理参数导致。
  PERCEPTION_INIT_FAIL_CONFIG_INVALID = 20101;

  // [FATAL] 感知加载必需的vehicle config出错
  // Metadata：
  // 以 kEventKeyCarID 为KEY，Value为车辆ID。
  PERCEPTION_INIT_FAIL_CAR_CONFIG_INVALID = 20102;

  // [FATAL] 感知加载必需的模型参数时出错，包括所有感知子模块加载的模型
  // Metadata：
  // 以 kEventKeyNnModelName 为KEY，Value为加载失败的模型的绝对路径。
  PERCEPTION_INIT_FAIL_MODEL_CONFIG_INVALID = 20103;

  // [FATAL]
  // 感知加载必需的相机参数时出错，包括相机的数量，型号，标定过后的内外参信息等
  // Metadata：
  // 如果是整体文件读取失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为kEventValueSensorAllCameras；
  //  - 以 kEventKeyDescription 为 KEY，Value为失败原因或留空。
  // 如果是某个相机参数加载失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX
  //  中的预定义值；
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因或留空（例如型号读取失败，内参读取失败，等。）
  PERCEPTION_INIT_FAIL_CAMERA_CONFIG_INVALID = 20104;

  // [FATAL]
  // 感知加载必需的激光雷达参数时出错，包括激光雷达的数量，型号，标定过后的内外参信息等
  // 如果是整体文件读取失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorLidar；
  //  - 以 kEventKeyDescription 为 KEY，Value为失败原因或留空。
  // 如果是某个激光雷达参数加载失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 激光雷达ID；
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因或留空（例如型号读取失败，内参读取失败，等。）
  PERCEPTION_INIT_FAIL_LIDAR_CONFIG_INVALID = 20105;

  // [FATAL]
  // 感知加载必需的毫米波参数时出错，包括毫米波雷达的数量，型号，标定过后的内外参信息等
  // 如果是整体文件读取失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorRadar；
  //  - 以 kEventKeyDescription 为 KEY，Value为失败原因或留空。
  // 如果是某个激光雷达参数加载失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 毫米波雷达ID；
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因或留空（例如型号读取失败，内参读取失败，等。）
  PERCEPTION_INIT_FAIL_RADAR_CONFIG_INVALID = 20106;

  // [FATAL]
  // 感知加载必需的超声波雷达参数时出错，包括超声波的数量，型号，标定过后的内外参信息等
  // 如果是整体文件读取失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorUltrasonic；
  //  - 以 kEventKeyDescription 为 KEY，Value为失败原因或留空。
  // 如果是某个激光雷达参数加载失败：
  //  - 以 kEventKeySensorName 为 KEY，Value为 超声波雷达ID；
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因或留空（例如型号读取失败，内参读取失败，等。）
  PERCEPTION_INIT_FAIL_ULTRASONIC_CONFIG_INVALID = 20107;

  // ** 20201 - 20299 **
  // 依赖项缺失/依赖模块启动失败/依赖模块异常，导致的感知启动失败

  // [FATAL] 感知缺少必要的软件组件，导致启动失败
  // Metadata：
  //  - 以 kEventKeyComponentName 为 KEY，Value为
  //  缺少的软件组件名字（例如libxxx.so）
  PERCEPTION_INIT_FAIL_MISSING_DEP_SW = 20201;

  // [FATAL] 感知缺少必要的硬件组件，或与必要的硬件组件通信异常，导致启动失败
  // Metadata：
  //  - 以 kEventKeyComponentName 为
  //  KEY，Value为缺少的硬件组件名字（kEventValueHardwareGPU，或kEventValueHardwareDLA，等）
  PERCEPTION_INIT_FAIL_MISSING_DEP_HW = 20202;

  // [FATAL] 感知所依赖的算法模块或硬件模块未成功启动，导致启动失败
  // Metadata：
  //  - 以 kEventKeyComponentName 为 KEY，Value为缺少的算法模块名字/内部编号
  PERCEPTION_INIT_FAIL_MISSING_DEP_MODULE = 20203;

  // ** 20301 - 20399 **
  // 感知自身问题，导致的感知启动失败

  // [FATAL] 感知的多个模型之间存在接口不吻合的现象，导致启动失败
  // Metadata:
  //  - 以 kEventKeyNnModelName 为KEY，Value为存在错误现象的模型名称。
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因，标明不吻合的具体node，可存在多个。
  PERCEPTION_INIT_FAIL_MODELS_MISALIGN = 20301;

  // [FATAL] 感知的模型与前处理或后处理参数存在接口不吻合的现象，导致启动失败
  // Metadata:
  //  - 以 kEventKeyNnModelName
  //  为KEY，Value为存在错误现象的模型名称，当存在多个模型名称时，拼接成一个字符串，并以
  //  "," 隔开。
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为失败原因，标明不吻合的具体node，可存在多个。
  PERCEPTION_INIT_FAIL_MODEL_ALGO_MISALIGN = 20302;

  // *** 21000 - 21999 ***
  // 感知运行期间，由于上游数据缺失/数据结构问题，导致对下游的输出可能存在缺失/不稳定现象，
  // 感知需要提前告警。错误码字符均以 “PERCEPTION_INPUT_DATA_”开头。
  // 与22xxx的区别为，本段错误码只关注数据是否完整，数据是否能够解析成功，以及是否存在关键
  // 字段缺失的问题，而不关注数据质量

  // [INFO] 感知上游数据无缺失，数据结构与完整性校验通过，无任何报错/异常
  PERCEPTION_INPUT_DATA_VALIDATION_SUCCESS = 21000;

  // 预留 21001 - 21100

  // ** 21101 - 21199 ** - Camera相关的错误码

  // [ERROR] Church喂给感知的某一帧数据中存在相机缺失的现象
  // Metadata:
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorAllCameras
  //  （Camear全部丢失） 或 kEventValueSensorXXX 中的预定义值
  //  （Camera部分丢失）； 当存在多个 Camera 时，拼接成一个字符串，并以 ","
  //  隔开。
  PERCEPTION_INPUT_DATA_MISSING_CAMERA = 21101;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在所有必需相机的数据，但某一个相机数据为空，
  // 或存在数据结构的缺失或校验失败（例如对压缩图而言，缺失数据总长度等）
  // Metadata：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX
  //  中的预定义值。当存在多个camera时，需要为每一个camera分别发送错误码。
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为缺失的字段，或校验失败的具体原因。
  PERCEPTION_INPUT_DATA_STRUCT_ISSUE_CAMERA = 21102;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在所有必需相机的数据，但某一个相机的数据在解析过程中报错失败。
  // Metadata：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX
  //  中的预定义值。当存在多个camera时，需要为每一个camera分别发送错误码。
  //  - 以 kEventKeyDescription 为 KEY，Value为解析失败的具体原因。
  PERCEPTION_INPUT_DATA_PARSE_FAIL_CAMERA = 21103;

  // [WARN]
  // Church喂给感知的某一帧数据中存在相机数据到达时间与采样时间相差过大的现象
  // 阈值： 当到达时间与采样时间相差达50ms时触发（TBD）
  // Metadata：
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX 中的预定义值
  //  - 以 kEventKeySamplePublishGapMillisec 为KEY，
  //    Value为采样时间与发布时间的差值，单位为毫秒
  //  - 以 kEventKeySampleReceiveGapMillisec 为KEY，
  //    Value为采样时间与接收时间的差值，单位为毫秒
  //  - 以 kEventKeySampleTimestampMillisec 为KEY，
  //    Value为采样时间的时间戳，单位为毫秒
  //  - 以 kEventKeyPublishTimestampMillisec 为KEY，
  //    Value为发布时间的时间戳，单位为毫秒
  //  - 以 kEventKeyReceiveTimestampMillisec 为KEY，
  //    Value为接收时间的时间戳，单位为毫秒
  PERCEPTION_INPUT_DATA_DELAYED_CAMERA = 21104;

  // ** 21201 - 21299 ** - Lidar 相关的错误码

  // [ERROR] Church喂给感知的某一帧数据中存在激光雷达缺失的现象
  // Metadata： N/A
  PERCEPTION_INPUT_DATA_MISSING_LIDAR = 21201;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在必需激光雷达的数据，但数据为空，或存在数据结构的缺失或校验失败
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为缺失的字段，或校验失败的具体原因。
  PERCEPTION_INPUT_DATA_STRUCT_ISSUE_LIDAR = 21202;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在所有必需激光雷达的数据，但在解析过程中报错失败。
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value为解析失败的具体原因。
  PERCEPTION_INPUT_DATA_PARSE_FAIL_LIDAR = 21203;

  // [WARN]
  // Church喂给感知的某一帧数据中存在激光雷达数据到达时间与采样时间相差过大的现象
  // 阈值： 当到达时间与采样时间相差达120ms时触发
  // Metadata：
  //  - 以 kEventKeySamplePublishGapMillisec 为KEY，
  //    Value为采样时间与发布时间的差值，单位为毫秒
  //  - 以 kEventKeySampleReceiveGapMillisec 为KEY，
  //    Value为采样时间与接收时间的差值，单位为毫秒
  //  - 以 kEventKeySampleTimestampMillisec 为KEY，
  //    Value为采样时间的时间戳，单位为毫秒
  //  - 以 kEventKeyPublishTimestampMillisec 为KEY，
  //    Value为发布时间的时间戳，单位为毫秒
  //  - 以 kEventKeyReceiveTimestampMillisec 为KEY，
  //    Value为接收时间的时间戳，单位为毫秒
  PERCEPTION_INPUT_DATA_DELAYED_LIDAR = 21204;

  // ** 21301 - 21399 ** - Radar 相关的错误码

  // [ERROR] Church喂给感知的某一帧数据中存在毫米波雷达缺失的现象
  // Metadata：N/A
  PERCEPTION_INPUT_DATA_MISSING_RADAR = 21301;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在必需毫米波雷达的数据，但数据为空，或存在数据结构的缺失或校验失败
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为缺失的字段，或校验失败的具体原因。
  PERCEPTION_INPUT_DATA_STRUCT_ISSUE_RADAR = 21302;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在所有必需毫米波雷达的数据，但在解析过程中报错失败。
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value为解析失败的具体原因。
  PERCEPTION_INPUT_DATA_PARSE_FAIL_RADAR = 21303;

  // [WARN]
  // Church喂给感知的某一帧数据中存在激光雷达数据到达时间与采样时间相差过大的现象
  // 阈值： 未知（TBD）
  PERCEPTION_INPUT_DATA_DELAYED_RADAR = 21304;

  // ** 21401 - 21499 ** - Ultrasonic 相关的错误码

  // [ERROR] Church喂给感知的某一帧数据中存在超声波雷达缺失的现象
  // Metadata：N/A
  PERCEPTION_INPUT_DATA_MISSING_ULTRASONIC = 21401;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在必需超声波雷达的数据，但数据为空，或存在数据结构的缺失或校验失败
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为缺失的字段，或校验失败的具体原因。
  PERCEPTION_INPUT_DATA_STRUCT_ISSUE_ULTRASONIC = 21402;

  // [ERROR]
  // Church喂给感知的某一帧数据中存在所有必需超声波雷达的数据，但在解析过程中报错失败。
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value为解析失败的具体原因。
  PERCEPTION_INPUT_DATA_PARSE_FAIL_ULTRASONIC = 21403;

  // [WARN]
  // Church喂给感知的某一帧数据中存在超声波雷达数据到达时间与采样时间相差过大的现象
  // 阈值： 未知（TBD）
  PERCEPTION_INPUT_DATA_DELAYED_ULTRASONIC = 21404;

  // ** 21501 - 21599 ** - 定位数据相关的错误码
  // [FATAL] Church喂给感知的某一帧数据中存在定位数据缺失的现象
  // Metadata：N/A
  PERCEPTION_INPUT_DATA_MISSING_CAR_STATE = 21501;

  // [FATAL]
  // Church喂给感知的某一帧数据中存在必需定位数据，但数据为空，或存在数据结构的缺失或校验失败。
  // 例如时间戳长度必须为16位，等。
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value为缺失的字段，或校验失败的具体原因。
  PERCEPTION_INPUT_DATA_STRUCT_ISSUE_CAR_STATE = 21502;

  // [FATAL]
  // Church喂给感知的某一帧数据中存在必需定位数据，但在解析过程中报错失败。
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value为解析失败的具体原因。
  PERCEPTION_INPUT_DATA_PARSE_FAIL_CAR_STATE = 21503;

  // [WARN] Church喂给感知的某一帧数据中存在SD Map数据缺失的现象
  // Metadata：N/A
  PERCEPTION_INPUT_DATA_MISSING_SD_MAP = 21601;

  // *** 22000 - 22999 ***
  // 感知运行期间，在上游数据如期到达的前提下，检测到已到达数据不符合感知的需求，或存在隐患，
  // 或对于感知不可用，导致对下游的输出可能存在缺失/不稳定现象，感知需要提前告警。错误码字符均以
  // “PERCEPTION_INPUT_LOW_QUALITY_” 开头
  // 与21xxx的区别为，本段错误码只关注数据质量本身，如有数据缺失，或存在结构上不完整的现象，
  // 应该归类在21xxx之下

  // [INFO] 感知上游数据无缺失，数据质量校验通过，无任何报错/异常
  PERCEPTION_INPUT_QUALITY_CHECK_SUCCESS = 22000;

  // 预留 22001 - 22100

  // ** 22101 - 22199 ** - Camera相关的错误码
  // [WARN] 感知接收到的某一帧的相机图片尺寸不满足标准
  // Metadata:
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX
  //  中的预定义值； 当多个Camera存在该问题时，应分别报错。
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_INCORRECT_RES = 22101;

  // [WARN]
  // 感知接收到的某一帧的相机图片存在普适性质量问题（PSNR/SSIM/RMSE/NRMSE/ENTROPY等metrics）。
  // 存在质量问题的原因有可能是遮挡，模糊，严重噪音等。
  // 参考文档：https://blog.csdn.net/qq_43555943/article/details/110957599
  // Metadata:
  //  - 以 kEventKeySensorName 为 KEY，Value为 kEventValueSensorXXX
  //  中的预定义值； 当多个Camera存在该问题时，应分别报错。
  //  - 以 kEventKeyDescription 为 KEY，Value为具体问题。
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_GENERAL = 22102;

  // ** 22201 - 22299 ** - Lidar相关的错误码

  // [WARN] 感知接收到的某一帧的激光雷达存在遮挡现象
  // Metadata：N/A
  PERCEPTION_INPUT_LOW_QUALITY_LIDAR_OCCLUDED = 22201;

  // [WARN] 感知接收到的lidar输入有某些角度缺失，metadata需要上报对应的角度信息
  // 常见于orin等平台处理不及时造成的lidar部分角度数据丢掉
  // 此时对应角度附近的感知结果不可信或质量低（只影响fusion下的性能）
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value中需要包含对应的角度信息，start-end的弧度/角度区间；如果存在多个，用","隔开。
  PERCEPTION_INPUT_LOW_QUALITY_LIDAR_MISSING_ANGLE = 22202;

  // [WARN] 感知接收到的lidar输入范围近，远距离缺失
  // 常见与雨天，lidar脏污等场景，此时检测距离近，远距离检测可能缺失（40m以上），需要减速
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value中需要一个以米为单位的具体数值，说明地面可取信的最远范围。
  PERCEPTION_INPUT_LOW_QUALITY_LIDAR_LOW_INTENSITY = 22203;

  // ** 22301 - 22399 ** - Radar相关的错误码
  // [WARN] 感知接收到的某一帧的毫米波雷达存在遮挡现象
  // Metadata：N/A
  PERCEPTION_INPUT_LOW_QUALITY_RADAR_OCCLUDED = 22301;

  // [WARN] 感知接收到的某一帧的毫米波雷达数据存在跳变
  // 常见暴雨/隧道下多径效应导致的毫米波雷达点杂乱
  // Metadata：
  //  - 以 kEventKeyDescription为KEY，Value中需要一个0-1
  //  之间的浮点数，表明和上一帧的相似度。
  PERCEPTION_INPUT_LOW_QUALITY_RADAR_DRAMATIC_CHANGE = 22302;

  // ** 22401 - 22499 ** - Ultrasonic相关的错误码
  // [WARN]
  // 感知接收到的某一帧的超声波雷达存在数值异常的现象（例如返回的距离为0）
  // Metadata：
  //  - 以 kEventKeyDescription 为
  //  KEY，Value中需要包含被遮挡的超声波雷达的对应ID；当多个超声波存在该问题时，应分别报错。
  PERCEPTION_INPUT_LOW_QUALITY_ULTRASONIC_DISTANCE_INVALID = 22401;

  // ** 22501 - 22599 ** - 定位数据相关的错误码
  // [ERROR] 感知接收到的某一帧的定位数据存在NaN
  // Metadata：
  //  - 以 kEventKeyFieldName 为KEY，Value为存在NaN的对应字段；
  // 如可存在多个字段，则用 “,” 隔开。
  PERCEPTION_INPUT_LOW_QUALITY_CAR_STATE_NAN = 22501;

  // [ERROR] 感知接收到的某一帧的定位数据存在不合理的较大跳变，不符合运动学模型
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value中需要包含不符合运动学模型的原因。
  PERCEPTION_INPUT_LOW_QUALITY_CAR_STATE_KINEMATICS = 22502;

  // *** 23000 - 23999 ***
  // 感知运行期间，由于自身计算错误/限制，导致对下游的输出可能存在缺失/不稳定现象，感知需要提前告警。
  // 错误码字符均以“PERCEPTION_RUNTIME_”开头

  // 预留 23001 - 23100

  // [INFO] 感知当前帧运算通过，无任何报错/异常
  PERCEPTION_RUNTIME_SUCCESS = 23000;

  // ** 23101 - 23199 ** - 软件调度相关的错误码
  // [WARN] 感知计算结果的过程太慢，超出正常预期时间
  // Metadata：
  //  - 以 kEventKeyTimeConsumedMillisec 为
  //  KEY，Value为当前帧计算所用的时间，单位为毫秒。
  //  - 以 kEventKeyTimeExpectedMillisec 为
  //  KEY，Value为当前帧计算所预期的时间（即阈值），单位为毫秒。
  PERCEPTION_RUNTIME_COMPUTE_TOO_SLOW = 23101;

  // [ERROR]
  // 感知在计算过程中出现死循环，或其他无法正常退出的问题。（预留给调度框架）
  // Metadata：
  //  - 以 kEventKeyTimeConsumedMillisec 为
  //  KEY，Value为当前帧计算所用的时间，单位为毫秒。
  //  - 以 kEventKeyTimeExpectedMillisec 为
  //  KEY，Value为当前帧计算所预期的时间（即阈值），单位为毫秒。
  PERCEPTION_RUNTIME_DEAD_LOCK = 23102;

  // ** 23201 - 23299 ** - 3D检测相关的错误码
  // [INFO] 感知检测到路牌鬼影
  // Metadata：
  //  - 以 kEventKeyDescription 为 KEY，Value为具体问题。
  PERCEPTION_RUNTIME_TRAFFIC_SIGN_NOISE_DETECTED = 23201;

  // ** 23301 - 23399 ** - 红绿灯检测相关
  // [WARN] 感知算出的红绿灯检测结果，存在置信度较低的现象
  // Metadata：红绿灯2D检测出现同一形状不同颜色灯/出现控路状态跳变/出现与sdmap校验有DIFF等异常情况
  PERCEPTION_RUNTIME_TRAFFIC_LIGHT_UNCERTAIN_OUTLIER = 23301;

  // ** 23401 - 23499 ** - 车道线检测相关
  // [WARN] 感知算出的车道线检测结果，存在大曲率弯道置信度较低的现象
  // Metadata：置信度分数以及阈值
  PERCEPTION_RUNTIME_LANE_LARGE_CURVE_LOW_SCORE = 23401;

  // [WARN] 感知算出的车道线检测结果，存在检测结果线段较短的现象
  // Metadata：置信度分数以及阈值
  PERCEPTION_RUNTIME_LANE_TOO_SHORT = 23402;

  // [WARN] 感知算出的车道线检测结果，存在检测结果线段较差的情况
  PERCEPTION_RUNTIME_LANE_CROSS_LINE = 23403;

  // [WARN] 感知算出的车道线检测结果, 存在检测结果线段未被关联到车道中
  PERCEPTION_RUNTIME_LANE_DISSOCIATIVE_LINE = 23404;

  // [WARN] 感知算出的 Lane Close 结果, 存在一致性问题
  PERCEPTION_RUNTIME_LANE_CLOSE_INCONSISTENCY = 23405;
  
  // [INFO] PERCEP_CAMERA检测到待转区，数据回流
  SANMAP_DATA_BACKFLOW_LEFTTURN = 23406;
  // [INFO] PERCEP_CAMERA检测到待转区FP，数据回流
  SANMAP_DATA_BACKFLOW_LEFTTURN_FP = 23407;

  // [WARN] 感知算出的 BusLane 结果, 存在不稳定的情况
  PERCEPTION_RUNTIME_LANE_BUS_UNSTABLE = 23408;

  // [INFO] PERCEP_CAMERA检测到疑似非机动车道FP，数据回流
  SANMAP_DATA_BACKFLOW_BIKING_FP = 23409;

  // [INFO] 路线SD Link中检测到三岔路口，数据回流
  SANMAP_DATA_BACKFLOW_MULTI_SPLIT = 23410;

  // ** 23501 - 23599 ** - 路牌/路标检测相关
  // [WARN] 感知算出的路牌/路标检测结果，存在置信度较低的现象
  // Metadata：置信度分数以及阈值
  PERCEPTION_RUNTIME_TRAFFIC_SIGN_UNCERTAIN_OUTLIER = 23501;

  // ** 23601 - 23699 ** - 泊车车位检测相关
  // 暂缺

  // ** 29101 - 29199 ** - AVM相关的错误码
  // [ERROR] PERCEP_AVM加载自身配置参数检查出错
  PERCEP_AVM_INIT_FAIL_CONFIG_INVALID = 29101;

  // [ERROR] PERCEP_AVM加载必需的vehicle config出错
  PERCEP_AVM_INIT_FAIL_CAR_CONFIG_INVALID = 29102;

  // [ERROR] PERCEP_AVM加载必需的ground config出错
  PERCEP_AVM_INIT_FAIL_GROUND_CONFIG_INVALID = 29103;

  // [ERROR] PERCEP_AVM加载必需的camera config出错
  PERCEP_AVM_INIT_FAIL_CAMERA_CONFIG_INVALID = 29104;

  // [ERROR] PERCEP_AVM加载orincam config出错
  // 可能是orincam中没有鱼眼相机相关的信息
  PERCEP_AVM_INIT_FAIL_ORINCAM_CONFIG_INVALID = 29105;

  // [ERROR] PERCEP_AVM加载必需的lidar config出错
  PERCEP_AVM_INIT_FAIL_LIDAR_CONFIG_INVALID = 29106;

  // [WARN] PERCEP_AVM加载超声波config出错，可能不会启动PDC
  PERCEP_AVM_INIT_FAIL_ULTRASONIC_CONFIG_INVALID = 29107;

  // [ERROR] PERCEP_AVM加载必需的图片资源出错
  PERCEP_AVM_INIT_FAIL_RESOURCE_LOADING_FAIL = 29108;

  // [WARN] PERCEP_AVM获取鱼眼相机图像时没有同步到全部的相机图像
  PERCEP_AVM_INPUT_DATA_MISSING_CAMERA = 29131;

  // [WARN] PERCEP_AVM收到的某一帧定位数据缺失或解析错误。
  PERCEP_AVM_INPUT_DATA_PARSE_FAIL_CAR_STATE = 29132;

  // [WARN] PERCEP_AVM检测到前视鱼眼摄像图丢帧[对应panoramic_1]
  PERCEP_AVM_INPUT_PANORAMIC_1_MISSING = 29133;

  // [WARN] PERCEP_AVM检测到右侧鱼眼摄像图丢帧[对应panoramic_2]
  PERCEP_AVM_INPUT_PANORAMIC_2_MISSING = 29134;

  // [WARN] PERCEP_AVM检测到后视鱼眼摄像图丢帧[对应panoramic_3]
  PERCEP_AVM_INPUT_PANORAMIC_3_MISSING = 29135;

  // [WARN] PERCEP_AVM检测到左侧鱼眼摄像图丢帧[对应panoramic_4]
  PERCEP_AVM_INPUT_PANORAMIC_4_MISSING = 29136;

  // [WARN] PERCEP_AVM检测到方向盘转角信号无效或消失
  PERCEP_AVM_INPUT_WHEEL_ANGLE_INVALID = 29137;

  // [WARN] PERCEP_AVM检测到超声波雷达信号超时或无效
  PERCEP_AVM_INPUT_ULTRASONIC_INVALID = 29138;

  // [ERROR] PERCEP_AVM接收到来自CUDA驱动层的报错
  PERCEP_AVM_RECEIVE_CUDA_ERROR_FROM_KERMEL = 29141;

  // [WARN] PERCEP_AVM检测到车辆未进行下线标定
  PERCEP_AVM_CAMERA_NOT_CALIBRATED = 29151;

  // [INFO] PERCEP_AVM在本次行程中发出第一帧图像
  PERCEP_AVM_FIRST_FRAME_DISPLAYED = 29161;

  // *** 29200 - 29999 ***
  // 感知因业务需求，额外定义的与外部对接的错误码

  // ****** 以上为感知模块错误码定义 ******
  /* 30000~39999 PPNC */
  PREDICTION_CUTIN_FP = 30000;
  PREDICTION_CUTIN_FN = 30001; 

  PREDICTION_VRU_CROSS_FP = 30010;
  PREDICTION_VRU_CROSS_FN = 30011;

  PREDICTION_BINSPEED_URBAN = 30020;
  PREDICTION_BINSPEED_HIGHWAY = 30021;

  PREDICTION_ABN_VEHICLE_FP = 30030;

  PREDICTION_TRAJ_DIFF = 30040;

  /* 30000~30999 Prediction */
  /* 31000~31999 Planning*/
  /* 32000~32999 Aeb */
  /* 33000~33999 Control*/
  /* 34000~39999 Reserve*/

  PLANNING_INIT_CAR_CONFIG_INVALID = 31000;
  PLANNING_INPUT_ROUTING_INVALID = 31100;
  PLANNING_INPUT_PERCEPTION_TIMESTAMP_INVALID = 31101;
  PLANNING_INPUT_PERCEPTION_DATA_INVALID = 31102;
  PLANNING_INPUT_CAR_STATE_TIMESTAMP_INVALID = 31103;
  PLANNING_INPUT_CAR_STATE_DATA_INVALID = 31104;
  PLANNING_INPUT_RAS_MAP_TIMESTAMP_INVALID = 31105;
  PLANNING_INPUT_RAS_MAP_DATA_INVALID = 31106;
  PLANNING_INPUT_POSE_DRIFTING = 31107;
  PLANNING_RUNTIME_REFERENCE_LINE_INVALID = 31200;
  PLANNING_RUNTIME_TRAJECTORY_CHECKER_SPEED_INVALID = 31201;
  PLANNING_RUNTIME_ILQR_SOLVE_FAILED = 31202;
  PLANNING_OUTPUT_PLANNING_FAILD = 31300;
  PLANNING_OUTPUT_OFF_LANE = 31301;
  PLANNING_OUTPUT_LAT_UNCOMFORTABLE = 31302;
  PLANNING_OUTPUT_LON_HARD_BRAKE = 31303;
  PLANNING_OUTPUT_NOT_CONTROLABLE = 31304;
  PLANNING_OUTPUT_UNMOVEABLE_COLLISION = 31305;
  PLANNING_OUTPUT_FRONT_MOVING_OBJ_COLLISION = 31306;
  PLANNING_OUTPUT_NOT_ENOUGH_POINT = 31307;
  PLANNING_OUTPUT_TRAJECTORY_PERCEPTION_TIME_UNMATCHED = 31308;
  PLANNING_OUTPUT_HIGH_LATENCY_NCA = 31309;
  PLANNING_OUTPUT_HIGH_LATENCY_ICA = 31310;
  PLANNING_OUTPUT_HIGH_LATENCY_ACC = 31311;
  PLANNING_OUTPUT_HIGH_LATENCY_APA = 31312;
  PLANNING_OUTPUT_HIGH_LATENCY_VPA_ROUTING = 31313;
  PLANNING_OUTPUT_TRAJECTORY_SAFETY_ACTION = 31314;
  PLANNING_LANE_CHANGE_HUMAN_INTERVENE_TO_LANE_CHANGE = 31315;
  PLANNING_LANE_CHANGE_HUMAN_INTERVENE_TO_LANE_KEEP = 31316;
  PLANNING_REFLINE_COLLISION = 31317;
  PLANNING_ILQR_CONSTRAINT_CHECK_FAILED = 31318;
  PLANNING_STITCH_POINTS_CONSTRAINT_CHECK_FAILED = 31319;
  PLANNING_LANE_CHANGE_SWING = 31320;
  DIVERGE_WRONG_WAY = 31321;

  // 31500 start for openspace
  PLANNING_OPENSPACE_PRE_PLAN = 31500;

  AEB_INIT_CONFIG_INVALID = 32000;
  AEB_INPUT_UPSTREAM_DATA_EMPTY = 32100;
  AEB_INPUT_PERCEPTION_TIMESTAMP_INVALID = 32101;
  AEB_INPUT_CAR_INFO_TIMESTAMP_INVALID = 32102;
  AEB_INPUT_CAR_STATE_TIMESTAMP_INVALID = 32103;
  AEB_INPUT_POSE_DRIFTING = 32104;
  AEB_OUTPUT_AEB_ACTIVE = 32300;
  AEB_OUTPUT_FCW_ACTIVE = 32301;
  AEB_OUTPUT_EBA_ACTIVE = 32302;
  AEB_OUTPUT_FCTA_ACTIVE = 32303;
  AEB_OUTPUT_RAEB_ACTIVE = 32304;
  AEB_OUTPUT_RCTB_ACTIVE = 32305;
  AEB_OUTPUT_FCTB_ACTIVE = 32306;
  AEB_OUTPUT_MEB_ACTIVE = 32307;
  AEB_OUTPUT_ESA_ACTIVE = 32308;
  AEB_OUTPUT_AEB_TRIGGER = 32309;  //info proto definition: TriggerEventInfo from  common/proto/aeb/trigger_event.proto
  AEB_OUTPUT_FCTB_TRIGGER = 32310; //info proto definition: TriggerEventInfo from  common/proto/aeb/trigger_event.proto
  AEB_OUTPUT_MEB_TRIGGER = 32311;  //info proto definition: TriggerEventInfo from  common/proto/aeb/trigger_event.proto
  AEB_OUTPUT_RCTB_TRIGGER = 32312; //info proto definition: TriggerEventInfo from  common/proto/aeb/trigger_event.proto
  AEB_OUTPUT_ESA_TRIGGER = 32313;

  CONTROL_CANBUS_LOST_HEARTBEAT = 33000 [deprecated = true];
  CONTROL_CANBUS_INCOMPLETE_DATA = 33001 [deprecated = true];
  CONTROL_CANBUS_TIMESTAMP_INVALID = 33002 [deprecated = true];
  CONTROL_CANBUS_POSE_INVALID = 33003 [deprecated = true];
  CONTROL_CANBUS_YAW_INVALID = 33004 [deprecated = true];
  CONTROL_CANBUS_PITCH_INVALID = 33005 [deprecated = true];
  CONTROL_CANBUS_VELOCITY_INVALID = 33006 [deprecated = true];
  CONTROL_CANBUS_WHEEL_ANGLE_INVALID = 33007;
  CONTROL_CANBUS_ACCELERATION_INVALID = 33008 [deprecated = true];
  CONTROL_PLANNING_LOST_HEARTBEAT = 33009;
  CONTROL_PLANNING_INCOMPLETE_DATA = 33010;
  CONTROL_PLANNING_LONGITUDINAL_INVALID = 33011;
  CONTROL_PLANNING_POSE_INVALID = 33012;
  CONTROL_PLANNING_GEAR_INVALID = 33013;
  CONTROL_PLANNING_ABNORMAL = 33014;
  CONTROL_PLANNING_YAW_INVALID = 33015;
  CONTROL_PLANNING_KAPPA_INVALID = 33016;
  CONTROL_LATERAL_ERROR_INVALID = 33017;
  CONTROL_YAW_ERROR_INVALID = 33018;
  CONTROL_KAPPA_INVALID = 33019;
  CONTROL_ACCELERATION_INVALID = 33020;
  CONTROL_LON_POSITION_ERROR_INVALID = 33021;
  CONTROL_LON_SPEED_ERROR_INVALID = 33022;
  CONTROL_CMD_INCOMPLETE_DATA = 33023;
  CONTROL_STEERING_CMD_INVALID = 33024;
  CONTROL_ACCELERATION_CMD_INVALID = 33025;
  CONTROL_APA_CMD_INVALID = 33026;
  CONTROL_GEAR_CMD_INVALID = 33027;
  CONTROL_ABNORMAL_CREEP = 33028;
  CONTROL_ABNORMAL_STANDSTILL = 33029;
  CONTROL_PLANNING_RELATIVE_TIME_INVALID = 33030;
  CONTROL_STEER_WHEEL_OFFSET_ABNORMAL = 33031;
  CONTROL_LAT_ACTUATOR_DELAY_TIME_ABNORMAL = 33032;
  CONTROL_ANGLE_TRACKING_ABNORMAL = 33033;
  CONTROL_TIRE_SIDESLIP_ABNORMAL = 33034;
  CONTROL_LAT_ERROR_ABNORMAL = 33035;
  CONTROL_LAT_ACC_ABNORMAL = 33036;
  CONTROL_LAT_JERK_ABNORMAL = 33037;
  CONTROL_LOCALIZATION_INCOMPLETE_DATA = 33038;
  CONTROL_LOCALIZATION_TIMESTAMP_INVALID = 33039;
  CONTROL_LOCALIZATION_POSE_INVALID = 33040;
  CONTROL_LOCALIZATION_YAW_INVALID = 33041;
  CONTROL_LOCALIZATION_PITCH_INVALID = 33042;
  CONTROL_LOCALIZATION_VELOCITY_INVALID = 33043;
  CONTROL_LOCALIZATION_ACCELERATION_INVALID = 33044;
  CONTROL_LOCALIZATION_LOST_HEARTBEAT = 33045;


  /* 40000~44999 BLC/SAFETY/DSM */
  /* 40000 ~ 40999 BLC Driving */
  /* 41000 ~ 41999 BLC Parking */
  /* 42000 ~ 42999 SAFETY SOC*/
  /* 43000 ~ 43999 SAFETY SSM*/
  /* 44000 ~ 44199 DSM */
  /* 44200 ~ 44999 Reserve*/

  BLC_STATE_ACC_STATUS_UPDATE_EVENT =
      40000;  // info proto definition: from report_event.proto
  BLC_STATE_ICA_STATUS_UPDATE_EVENT =
      40001;  // info proto definition: from report_event.proto
  BLC_STATE_NCA_STATUS_UPDATE_EVENT =
      40002;  // info proto definition: from report_event.proto
  BLC_STATE_APA_STATUS_UPDATE_EVENT =
      40003;  // info proto definition: from report_event.proto
  BLC_STATE_AEB_STATUS_UPDATE_EVENT =
      40004;  // info proto definition: from report_event.proto
  BLC_STATE_HMA_STATUS_UPDATE_EVENT =
      40005;  // info proto definition: from report_event.proto
  BLC_STATE_BSD_STATUS_UPDATE_EVENT =
      40006;  // info proto definition: from report_event.proto
  BLC_STATE_LCA_STATUS_UPDATE_EVENT =
      40007;  // info proto definition: from report_event.proto
  BLC_STATE_DOW_STATUS_UPDATE_EVENT =
      40008;  // info proto definition: from report_event.proto
  BLC_STATE_RCTA_STATUS_UPDATE_EVENT =
      40009;  // info proto definition: from report_event.proto
  BLC_STATE_RCW_STATUS_UPDATE_EVENT =
      40010;  // info proto definition: from report_event.proto
  BLC_STATE_RCTB_STATUS_UPDATE_EVENT =
      40011;  // info proto definition: from report_event.proto
  BLC_STATE_AVM_STATUS_UPDATE_EVENT =
      40012;  // info proto definition: from report_event.proto
  BLC_STATE_LDW_STATUS_UPDATE_EVENT =
      40013;  // info proto definition: from report_event.proto
  BLC_STATE_RDP_STATUS_UPDATE_EVENT =
      40014;  // info proto definition: from report_event.proto
  BLC_STATE_RPA_STATUS_UPDATE_EVENT =
      40015;  // info proto definition: from report_event.proto
  BLC_STATE_ACTIVE_STATUS_UPDATE_EVENT =
      40016;  // info proto definition: from report_event.proto
  BLC_DRIVER_TAKEOVER_EVENT =
      40017;  // info proto definition: from report_event.proto
  BLC_ALARM_DOOR_OPEN_EVENT =
      40018;  // info proto definition: from report_event.proto
  BLC_ALARM_LANE_DEPARTURE_EVENT =
      40019;  // info proto definition: from report_event.proto
  BLC_ALARM_BLINDSPOT_EVENT =
      40020;  // info proto definition: from report_event.proto
  BLC_ALARM_COLLISION_EVENT =
      40021;  // info proto definition: from report_event.proto
  BLC_ALARM_SPEED_LIMIT_EVENT =
      40022;  // info proto definition: from report_event.proto
  BLC_STATE_DRIVER_HANDOFF_EVENT =
      40023;  // info proto definition: from report_event.proto
  BLC_USER_CLICK_TRIGGER_EVENT = 40024;
  BLC_FEATURE_ABORT_EVENT = 40025;        // info proto FeatureAbort
                                          // from report_event.proto
  BLC_DRIVING_DOWNGRADING_EVENT = 40026;  // info proto DrivingDowngrading
                                          // from report_event.proto
  BLC_INHIBIT_EVENT = 40027;  // InhibitReportInfo report_event.proto
  BLC_DRIVER_EMERGENCY_TAKEOVER_EVENT =
      40028;  // info proto definition: from report_event.proto
  BLC_INHIBIT_TRIGGER_EVENT = 40029;  // InhibitTagInfo report_event.proto
  BLC_USER_CLICK_HIGH_PRIORITY_TRIGGER_EVENT = 40030;
  BLC_STATE_VPA_STATUS_UPDATE_EVENT =
      40031;  // info proto definition: from report_event.proto
  BLC_STATE_RADS_STATUS_UPDATE_EVENT =
      40032;  // info proto definition: from report_event.proto
  BLC_FEATURE_AUTO_ABORT_EVENT = 40033;        // info proto FeatureAbort
                                          // from report_event.proto
  BLC_DRIVING_AUTO_DOWNGRADING_EVENT = 40034;  // info proto DrivingDowngrading
                                          // from report_event.proto
  BLC_STATE_ABP_STATUS_UPDATE_EVENT =
      40035;  // info proto definition: from report_event.proto
  BLC_STATE_AWB_STATUS_UPDATE_EVENT =
      40036;  // info proto definition: from report_event.proto
  BLC_STATE_ESA_STATUS_UPDATE_EVENT =
      40037;  // info proto definition: from report_event.proto
  BLC_STATE_FCTA_STATUS_UPDATE_EVENT =
      40038;  // info proto definition: from report_event.proto
  BLC_STATE_FCTB_STATUS_UPDATE_EVENT =
      40039;  // info proto definition: from report_event.proto
  BLC_STATE_FCW_STATUS_UPDATE_EVENT =
      40040;  // info proto definition: from report_event.proto
  BLC_STATE_MEB_STATUS_UPDATE_EVENT =
      40041;  // info proto definition: from report_event.proto
  BLC_STATE_SCHEDULED_UPDATE_EVENT =
      40042;  // info proto definition: from report_event.proto

  // blc driving from 40100-40199
  BLC_ACC_ACTIVE_FAILED =
      40100;  // info proto definition: DrivingActiveFailInfo
              // from report_event.proto
  BLC_ACC_ACTIVE_SUCCESS =
      40101;  // info proto definition: DrivingActiveSuccessInfo
              // from report_event.proto
  BLC_ICA_ACTIVE_FAILED =
      40102;  // info proto definition: DrivingActiveFailInfo
              // from report_event.proto
  BLC_ICA_ACTIVE_SUCCESS =
      40103;  // info proto definition: DrivingActiveSuccessInfo
              // from report_event.proto
  BLC_NCA_ACTIVE_FAILED =
      40104;  // info proto definition: DrivingActiveFailInfo
              // from report_event.proto
  BLC_NCA_ACTIVE_SUCCESS =
      40105;  // info proto definition: DrivingActiveSuccessInfo
              // from report_event.proto
  BLC_PREVIEW_ROUTING_SUCCESS =
      40106;  // info proto definition: PreviewRoutingSuccessInfo from
              // report_event.proto
  BLC_PREVIEW_ROUTING_FAILED = 40107;
  BLC_START_ROUTING_SUCCESS = 40108;
  BLC_START_ROUTING_FAILED = 40109;
  BLC_DRIVING_ADJUST_SPEED_SUCCESS = 40120;  // 限速调节
  BLC_DRIVING_ADJUST_SPEED_FAILED = 40121;
  BLC_BCM_TOGGLE_CHANGE_LANE_SUCCESS = 40122;  // 拨杆变道
  BLC_BCM_TOGGLE_CHANGE_LANE_FAILED = 40123;
  BLC_ADJUST_TCC_SUCCESS = 40124;  // 时距调节
  BLC_ADJUST_TCC_FAILED = 40125;
  BLC_LATEM_EXIT_RECOVER_SUCCESS =
      40126;  // info proto definition: DrivingActiveSuccessInfo
              // from report_event.proto
  BLC_LATEM_EXIT_RECOVER_FAILED =
      40127;  // info proto definition: DrivingActiveFailInfo
              // from report_event.proto
  BLC_ADAS_REQUEST_TAOKEVER =
      40128;  // info proto definition: AdasRequestTakeOverInfo
              // from report_event.proto
  BLC_LANE_CHANGE_STATUS = 40129;  // info proto definition: LaneChangeInfo
                                   // from report_event.proto
  BLC_PREVIEW_ROUTING_FULL_LINK_SUCCESS =
      40130;  // info proto definition: PreviewRoutingFullLinkSuccess
              // from report_event.proto
  BLC_MRM_SAFE_STOP = 40131;
  BLC_ELK_LCS_ACTIVE = 40132;
  BLC_ELK_O_ACTIVE = 40133;
  BLC_ACC_BRAKE_TAKEOVER_EVENT = 40134;
  BLC_ACC_BRAKE_EMERGENCY_TAKEOVER_EVENT = 40135;
  BLC_ACC_THROTTLE_TAKEOVER_EVENT = 40136;
  BLC_ACC_THROTTLE_EMERGENCY_TAKEOVER_EVENT = 40137;
  BLC_ICA_BRAKE_TAKEOVER_EVENT = 40138;
  BLC_ICA_BRAKE_EMERGENCY_TAKEOVER_EVENT = 40139;
  BLC_ICA_THROTTLE_TAKEOVER_EVENT = 40140;
  BLC_ICA_THROTTLE_EMERGENCY_TAKEOVER_EVENT = 40141;
  BLC_ICA_STEER_TAKEOVER_EVENT = 40142;
  BLC_ICA_STEER_EMERGENCY_TAKEOVER_EVENT = 40143;
  BLC_NCA_BRAKE_TAKEOVER_EVENT = 40144;
  BLC_NCA_BRAKE_EMERGENCY_TAKEOVER_EVENT = 40145;
  BLC_NCA_THROTTLE_TAKEOVER_EVENT = 40146;
  BLC_NCA_THROTTLE_EMERGENCY_TAKEOVER_EVENT = 40147;
  BLC_NCA_STEER_TAKEOVER_EVENT = 40148;
  BLC_NCA_STEER_EMERGENCY_TAKEOVER_EVENT = 40149;
  BLC_ELK_RE_ACTIVE = 40150;
  BLC_ELK_GO_ACTIVE =40151;
  BLC_UNFORCED_LANE_CHANGE_CLOSE_EVENT =
      40152;  // info proto definition LaneChangeSwitch: from report_event.proto
  BLC_UNFORCED_LANE_CHANGE_OPEN_EVENT =
      40153;  // info proto definition LaneChangeSwitch: from report_event.proto
  BLC_ELK_S_ACTIVE = 40154;
  BLC_SNOW_VEHICLE_SLIP_AND_RASMAP_CLEAR = 40155;
  BLK_LKA_ACTIVE_TOO_LONG = 40156;
  BLK_ELK_ACTIVE_TOO_LONG = 40157;


  // blc parking from 40200-40299

  BLC_APA_ACTIVE_SUCCESS =
      40200;  // info proto definition: BLCBusinessInfo from report_event.proto
  BLC_APA_ACTIVE_FAILED =
      40201;  // info proto definition: BLCBusinessInfo from report_event.proto
  BLC_APA_PARKING_SUCCESS =
      40202;  // info proto definition: BLCBusinessInfo from report_event.proto
  BLC_APA_PARKING_FAILURE =
      40203;  // info proto definition: BLCBusinessInfo from report_event.proto
  BLC_APA_PARKING_CANCEL =
      40204;  // info proto definition: BLCBusinessInfo from report_event.proto
  BLC_APA_PARKING_REPORT =
      40205;  // info proto definition: APAReport from report_event.proto
  BLC_PARKING_FAILURE_BUSINESS_REPORT =
      40206;  // info proto definition: ParkingFailureInfo from
              // report_event.proto
  BLC_PARKING_DRIVER_TAKEOVER_EVENT = 40207;
  // info proto definition: DriverTakeOverInfo from report_event.proto
  BLC_PARKING_SYSTEM_FAULT_EVENT = 40208;
  // info proto definition: ParkingSystemFaultInfo from report_event.proto
  BLC_PARKING_DRIVER_EMERGENCY_TAKEOVER_EVENT = 40209;
  // info proto definition: DriverTakeOverInfo from report_event.proto
  BLC_PARKING_BRAKE_TAKEOVER_EVENT = 40210;
  BLC_PARKING_STEER_TAKEOVER_EVENT = 40211;
  BLC_PARKING_SHIFT_TAKEOVER_EVENT = 40212;
  BLC_PARKING_VEHICLE_BLOCK_EVENT = 40213;
  BLC_PARKING_THROTTLE_TAKEOVER_EVENT = 40214;
  BLC_PARKING_DRIVER_EXIT_EVENT = 40215;
  BLC_CRUISING_BRAKE_TAKEOVER_EVENT = 40216;
  BLC_CRUISING_STEER_TAKEOVER_EVENT = 40217;
  BLC_CRUISING_SHIFT_TAKEOVER_EVENT = 40218;
  BLC_CRUISING_THROTTLE_TAKEOVER_EVENT = 40219;
  BLC_CRUISING_DRIVER_EXIT_EVENT = 40220;
  BLC_PARKING_EPB_TAKEOVER_EVENT = 40221;
  BLC_CRUISING_EPB_TAKEOVER_EVENT = 40222;
  BLC_PARKING_DOOR_TAKEOVER_EVENT = 40224;
  BLC_CRUISING_DOOR_TAKEOVER_EVENT = 40225;
  BLC_CRUISING_COLLISION_EVENT = 40226;
  // info proto definition: AVMEventInfo from report_event.proto
  BLC_AVM_ABNORMAL = 40227;
  BLC_PARKING_SPEED_OVERLIMIT_EVENT = 40228;
  BLC_CRUISING_SPEED_OVERLIMIT_EVENT = 40229;
  BLC_PARKING_STANDBYAREA_LIMITED_EVENT = 40230;
  BLC_RPA_APP_SUSPEND_EVENT = 40231;

  BLC_RADS_REPORT =
      40232;  // info proto definition: RADSReport from report_event.proto
  BLC_VPA_LEARNING_REPORT =
      40233;  // info proto definition: VPALearningReport from report_event.proto
  BLC_VPA_ROUTING_REPORT =
      40234;  // info proto definition: VPARoutingReport from report_event.proto

  // blc aeb from 40300-40399
  BLC_AEB_STATUS_REPORT =
      40300;  // info proto definition: AEBStatusInfo from report_event.proto

  // blc rear warning from 40400~40499
  BLC_RCW_ACTIVE = 40400;
  BLC_RCTA_ACTIVE = 40401;
  BLC_RCTA_ACTIVE_TRIGGER_EVENT = 40402; // use to record bag

  // blc proc warning from 40500~40599
  BLC_PROC_SCHEDULE_TIMEOUT_EVENT = 40500;
  // info proto definition: BlcProcTimeWarnInfo from report_event.proto
  BLC_PROC_RUNTIME_TIMEOUT_EVENT = 40501;
  // info proto definition: BlcProcTimeWarnInfo from report_event.proto

  // info proto definition: dr.operationstatus.ReasonInfo from operation_status.proto
  BLC_PARKING_ENTER_APA_FAILED_EVENT = 40502;
  BLC_PARKING_ENTER_RADS_FAILED_EVENT = 40503;
  BLC_PARKING_ENTER_RPA_FAILED_EVENT = 40504;
  BLC_PARKING_ENTER_VPA_LEARNING_FAILED_EVENT = 40505;
  BLC_PARKING_ENTER_VPA_ROUTING_FAILED_EVENT = 40506;

  SAFETY_VEHICLE_STATE_UPDATED_EVENT =
      42000;  // info proto definition: VehicleInfo from
              // common/proto/safety/vehicle_info.proto
  SAFETY_DTC_EVENT = 42001;

  SAFETY_FRAME_RATIO_WARNING_EVENT =
      42002;  // info proto definition: FrameRatioInfo from
              // common/proto/church/frame_ratio_info.proto

  SAFETY_LOG_METRICS_EVENT = 42003;
  // info proto definition: SafetyLogMetrics from
  // common/proto/safety/safety_event.proto

  SAFETY_UPLOAD_METRICS_EVENT = 42004;
  // info proto definition: SafetyUploadMetrics from
  // common/proto/safety/safety_event.proto

  SAFETY_VHR_HEADER = 42005;
  SAFETY_RECEIVE_SOC_STATE_TIMEOUT_EVENT = 42006;
  SAFETY_RECEIVE_MCU_STATE_TIMEOUT_EVENT = 42007;

  SAFETY_FRAME_RATIO_TRACE_EVENT = 42008;
  // info proto definition: FrameRatioInfo from
  // common/proto/church/frame_ratio_info.proto

  SAFETY_FRAME_ANALYSIS_EVENT = 42009;
  // info proto definition: FrameRatioInfo from
  // common/proto/church/frame_ratio_info.proto

  SAFETY_CPU_TEMPERATURE_ABNORMAL = 42010;
  SAFETY_GPU_TERPERATURE_ABNORMAL = 42011;
  SAFETY_MEMORY_ABNORMAL = 42012;
  SAFETY_FRAME_RATIO_FATAL_EVENT = 42013;
  // no output topics
  SAFETY_TRIP_START_EVENT = 42014;
  // info proto definition: TripStartEvent from
  // common/proto/trip/trip_start.proto
  SAFETY_TRIP_END_EVENT = 42015;
  // info proto definition: TripEndEvent from
  // common/proto/trip/trip_end.proto
  SAFETY_COLLISION_OCCUR_EVENT = 42016;
  SAFETY_COLLISION_LOCK_OCCUR_EVENT = 42017;
  SAFETY_COLLISION_RISK_OCCUR_EVENT = 42018;
  SAFETY_DETECT_DEM_HEARTBEAT_TIMEOUT_EVENT = 42019;
  SAFETY_VCU_READY_STS_OFF_EVENT = 42020;
  SAFETY_VCU_READY_STS_ON_EVENT = 42021;
  SAFETY_FILTER_CONFIG_EVENT = 42022;
  SAFETY_TIME_READY_EVENT = 
      42023; // proto definition: TimeReadyReport from safety/safety_event.proto
  SAFETY_SYSTEM_ERROR_EVENT = 42024; // replace DEM_MODULE_CRASH
  SAFETY_LOW_SPEED_COLLISION_EVENT = 42025;

  /* dsm 44000 ~ 44199 */
  DSM_REPORT_TRACKING_DATA = 44000;
  // proto definition: TimeRecordToWeb from
  // drapi/dsm.proto

  DSM_INIT_SUCCESS = 44001;
  DSM_INIT_FAIL = 44002;
  DSM_SWITCH_MODE_SUCCESS = 44003;
  DSM_SWITCH_MODE_FAIL = 44004;
  DSM_STARTUP_FAIL_COUNT_POWER_ON =
      44005;  // proto definition: StartupFailReport from dsm/dsm.proto
  DSM_VANGUARD_START_OVERTIME = 44006;  // AVM相关模块启动就绪时间超时
  DSM_ALL_FUNCTION_START_OVERTIME = 44007;  // 全功能模块启动就绪超时
  DSM_SET_STATE_OVERTIME = 44008;           // 调用SetState接口超时
  DSM_SWITCH_MODE_OVERTIME = 44009;         // 模式切换超时

  /* viz 44200 ~ 44499 */
  VIZ_MAP_GETPATH_STATISTICS =
      44200;  // proto definition: GetPathTimeStatistics from
              // vis_3dmap/vis_3dmap.proto

  /* 45000~49999 BSW */
  /*47000~49999 RESERVE*/

  /*45000~45199 STARTER*/
  STARTER_WILL_START_DRIVING_MODULES = 45000;

  STARTER_PARSE_CONFIG_FAILED =
      45001;  // parse starter/all_config.jsonnet failed, info proto
              // definition: starter/starter_event.proto

  STARTER_MODULE_START_FAILED =
      45002;                     // module start failed, info proto definition:
                                 // starter/starter_event.proto
  STARTER_MODULE_CRASH = 45003;  // module crash, info proto definition:
                                 // starter/starter_event.proto

  STARTER_ABNORMAL_EXIT = 45004;
  STARTER_TASK_EXIT = 45005;
  STARTER_MODULE_ABNORMAL_EXIT = 45006;
  STARTER_BEGIN_UPGRADE = 45007;
  STARTER_END_UPGRADE_AND_EXIT = 45008;

  /*45200~45499 dem*/
  // info proto definition: DemReportEvent from dem/dem_event.proto
  DEM_START_SUCCESS = 45200;        // json_info["module_name"] = "dem"
  DEM_START_FAIL = 45201;           // json_info["module_name"] = "dem"
  DEM_MODULE_START_FAIL = 45202;    // json_info["module_name"] = "xxx module"
  DEM_MODULE_CRASH = 45203;         // json_info["module_name"] = "xxx module"
  DEM_MODULE_CRASH_EXPIRE = 45204;  // json_info["module_name"] = "xxx module"
  DEM_MODULE_RESTART_FAIL = 45205;  // json_info["module_name"] = "xxx module"
  DEM_EXIT = 45206;                 // json_info["module_name"] = "dem"
  DEM_MODULE_ABNOMAL_EXIT_WHEN_SHUTDOWN = 45207;         // json_info["module_name"] = "xxx module"

  /*45500~45999 PERF*/
  /*45000~45519 PERF TRACE EVENTS*/
  PERF_SYS_WARNING_EVENT =
      45500;  // system warning event, info proto definition: SysWarning from
              // perf_collector/perf_event.proto
  PERF_TASK_WARNING_EVENT =
      45501;  // tasks warning event, info proto definition: TaskWarning from
              // perf_collector/perf_event.proto
  PERF_TRACE_SYS = 45502;   // system infomation, info proto definition:
                            // SysTrace from perf_collector/perf_event.proto
  PERF_TRACE_TASK = 45503;  // tasks infomation, info proto definition:
                            // TaskTrace from perf_collector/perf_event.proto
  PERF_TRACE_PARTITION =
      45504;  // partition's usage, info proto definition:
              // PartitionTrace from perf_collector/perf_event.proto
  PERF_WARNING_PARTITION =
      45505;  // Partition warning infomation, info proto definition:
              // PartitionWarning from perf_collector/perf_event.proto
  PERF_TRACE_NETDEVICE =
      45506;  // network devices's stat, info proto definition:
              // NetDeviceTrace from perf_collector/perf_event.proto

  PERF_TRACE_CPUCORES =
      45507;  // cpucores's stat, info proto definition:
              // CpuCoreTrace from perf_collector/perf_event.proto
  PERF_TRACE_BLOCK =
      45508;  // block device's stat, info proto definition:
              // BlkIOTrace from perf_collector/perf_event.proto

  /*45520~45999 PERF WARNNING EVENTS*/
  PERF_SYS_CPU_LOAD =
      45520;              // system warning event, info proto definition:
                          // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_CTXT = 45521;  // system warning event, info proto definition:
                          // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_INTR = 45522;  // system warning event, info proto definition:
                          // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_SOFTIRQ =
      45523;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_PROCS_RUNNING =
      45524;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_PROCS_BLOCKED =
      45525;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_FREE_MEMORY =
      45526;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_DIRTY_MEMORY =
      45527;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_UNRECLAIMED_MEMORY =
      45528;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_PAGE_IN =
      45529;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_PAGE_FAULT =
      45530;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_PAGE_MAJFAULT =
      45531;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_CPU_TEMP =
      45532;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_GPU_TEMP =
      45533;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_BOARD_TEMP =
      45534;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_GPU_LOAD =
      45535;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_GPU_MEMORY =
      45536;  // system warning event, info proto definition:
              // PerfSysWarning from perf_collector/perf_event.proto
  PERF_SYS_NETWORK_INTERFACE_UP = 45537;    // system warning event:
                                            // The network interface is up
  PERF_SYS_NETWORK_INTERFACE_DOWN = 45538;  // system warning event:
                                            // The network interface is down
  PERF_SYS_SCHEDULER_STUCK = 45539;         // system warning event:
                                            // The system is very stuck
  PERF_SYS_LONG_IOWAIT =
      45540;  // system warning event:cpu spend long time in io operation
  PERF_SYS_LONG_IRQ = 45541;  // system warning event:cpu spend long time in irq
  PERF_SYS_LONG_SOFTIRQ =
      45542;  // system warning event:cpu spend long time in soft irq
  PERF_SYS_CLOCK_JUMP = 45543;  // system warning event:system clock jump event
  PERF_SYS_OOM_KILL = 45544;    // system warning event:system clock jump event
  PERF_SYS_HARD_DISK_ADD = 45545;
  PERF_SYS_HARD_DISK_REMOVE = 45546;
  PERF_SYS_TBOX_CONNECTED = 45547;
  PERF_SYS_TBOX_DISCONNECTED = 45548;
  PERF_SYS_RTTASK_DRIFT =
      45549;  // system warning event: task running in not recommended cores
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_THREADS =
      45550;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_VSS =
      45551;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_RSS =
      45552;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_CPU_LOAD =
      45553;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_WAIT_LONG =
      45554;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_BLOCKED_LONG =
      45555;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_ICS =
      45556;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_CS =
      45557;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_MIGRATION =
      45558;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_RCHAR =
      45559;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_WCHAR =
      45560;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_SYSC_READ =
      45561;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_SYSC_WRITE =
      45562;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_BI =
      45563;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_MORE_BO =
      45564;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_TASK_PANIC =
      45565;  // detect thread's panic from perf-collector for the time being
              // PerfWarnJson from perf_collector/perf_event.proto

  PERF_PARTITION_FULL =
      45574;  // system warning event, info proto definition:
              // PerfPartitionWarning from perf_collector/perf_event.proto
  PERF_PARTITION_READONLY =
      45575;  // system warning event, info proto definition:
              // PerfPartitionWarning from perf_collector/perf_event.proto
  PERF_SYS_SHMEM_OVERCOMMIT =
      45576;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto

  PERF_CGROUP_MEM_SOFTLIMIT_EXCESS =
      45600;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_CGROUP_MEM_OOM =
      45601;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto
  PERF_CGROUP_MEM_FAILCNT_INCREASING =
      45602;  // system warning event, info proto definition:
              // PerfWarnJson from perf_collector/perf_event.proto

  /*46000~46499 CHURCH*/
  CHURCH_FRAME_RATIO_EVENT =
      46000;  // info proto definition: FrameRatioInfo from
              // common/proto/church/frame_ratio_info.proto
  CHURCH_PROC_TIMEOUT_EVENT =
      46001;  // info proto definition:  ComponentProcTimeout from
              // common/proto/church/component_proc_timeout.proto
  CHURCH_MAX_EVENT_NUM_EVENT = 46002;
  CHURCH_MSG_TRANS_LOSS =
      46003;  // common/proto/church/trans_anomaly_events.proto
  CHURCH_MSG_TRANS_LONG_TIME =
      46004;  // common/proto/church/trans_anomaly_events.proto
  CHURCH_MSG_TRANS_SUSPEND =
      46005;  // common/proto/church/trans_anomaly_events.proto

  /*46500~46999 SENTRY*/
  SENTRY_APT_INSTALL_START = 46500;
  SENTRY_APT_INSTALL_SUCCESS = 46501;
  SENTRY_APT_INSTALL_FAIL = 46502;  // {"msg": "reason"}
  SENTRY_TRIP_START = 46503;        // {"trip_name": "YR_XXX"}
  SENTRY_TRIP_STOP = 46504;         // {"trip_name": "YR_XXX"}
  SENTRY_NO_SPACE_LEFT = 46505;
  SENTRY_HARD_DISK_WRITE_ERROR = 46506;
  SENTRY_BAG_ERROR = 46507;
  SENTRY_SENSORS_EXCEPTION = 46508;
  SENTRY_DEVICES_EXCEPTION = 46509;
  SENTRY_RECORD_PATH_EXCEPTION = 46510;
  SENTRY_TRIGGER_EVENT = 46511;   // trip/trigger_event.proto
  SENTRY_BAG_DELETE = 46512;      // {"bag_name": "xx", "del_reason": "xxx"}
  SENTRY_TRIGGER_DELETE = 46513;  // {trigger+, "del_reason": "xxx"}

  /*47000~47299 dts*/
  DTS_TIMESYNC_OFFSET_WARNING = 47001;  // timesync offset > 1ms
  DTS_TIMESYNC_OFFSET_ERROR = 47002;    // timesync offset > 10ms
  DTS_TIMESYNC_JUMPED = 47003;          // timesync's log appears jumped
  DTS_TIMESYNC_HEARTBEAT_TIMEOUT =
      47004;  // timesync's log doesn't update for 5 seconds

  /*47300~47399 self-check*/
  PKI_CERTIFICATE_VALIDATION_FAILURE = 47300;
  PKI_CERTIFICATE_SELF_CHECK_VALIDATION_FAILURE = 47301;
  PKI_CERTIFICATE_APPLICATION_FAILURE = 47302;
  VIN_NOT_WRITTEN = 47303;
  CONFIGURATION_WORD_NOT_WRITTEN = 47304;

  /* 50000~50999 ADS Platform Diagnostics events */
  DSV_CONTROLLER_LOCAL_SLEEP_WAKEUP_ABNORMAL = 50000;
  DSV_ADS_OVER_VOLTAGE = 50001;
  DSV_ADS_UNDER_VOLTAGE = 50002;
  DSV_BUS_OFF_ADAS_CANFD1 = 50003;
  DSV_BUS_OFF_ADAS_CANFD2 = 50004;
  DSV_BUS_OFF_PDC_CANFD = 50005;
  DSV_BUS_OFF_ADAS_SUB_CANFD = 50006;
  DSV_RFCM_L_LOST_COMMUNICATION = 50008;
  DSV_RFCM_R_LOST_COMMUNICATION = 50009;
  DSV_BLE_LOST_COMMUNICATION = 50010;
  DSV_DMS_LOST_COMMUNICATION = 50011;
  DSV_F_PBOX_LOST_COMMUNICATION = 50012;
  DSV_R_PBOX_LOST_COMMUNICATION = 50013;
  DSV_HUT_LOST_COMMUNICATION = 50014;
  DSV_PEPS_LOST_COMMUNICATION = 50017;
  DSV_CSA2_LOST_COMMUNICATION = 50020;
  DSV_DCT_LOST_COMMUNICATION = 50021;
  DSV_ELECTRICAL_STABLE_PROGRAM_LOST_COMMUNICATION = 50023;
  DSV_INTEGRATED_BRAKE_CONTROL_LOST_COMMUNICATION = 50024;
  DSV_AIRBAG_MODULE_LOST_COMMUNICATION = 50026;
  DSV_INSTRUMENT_PANEL_LOST_COMMUNICATION = 50027;
  DSV_BODY_CONTROL_MODULE_LOST_COMMUNICATION = 50029;
  DSV_AC_LOST_COMMUNICATION = 50030;
  DSV_ELECTRIC_POWER_STEERING_SYSTEM_LOST_COMMUNICATION = 50032;
  DSV_DOOR_CONTROL_MODULE_LOST_COMMUNICATION = 50033;
  DSV_DIGITAL_VIDEO_RECORDER_LOST_COMMUNICATION = 50037;
  DSV_TYRE_PRESSURE_MONITORING_SYSTEMS_LOST_COMMUNICATION = 50041;
  DSV_HYBRID_VEHICLE_CONTROL_UNIT_LOST_COMMUNICATION = 50042;
  DSV_VEHICLE_CONTROL_UNIT_LOST_COMMUNICATION = 50043;
  DSV_COMBINATIONSWITCH_ASSEMBLY_LOST_COMMUNICATION = 50044;
  DSV_ABM_THE_INVALID_SERIAL_PORT_DATA = 50047;
  DSV_BCM_THE_INVALID_SERIAL_PORT_DATA = 50049;
  DSV_CSATHE_INVALID_SERIAL_PORT_DATA = 50052;
  DSV_EPS_THE_INVALID_SERIAL_PORT_DATA = 50053;
  DSV_ESP_THE_INVALID_SERIAL_PORT_DATA = 50054;
  DSV_HCU_THE_INVALID_SERIAL_PORT_DATA = 50055;
  DSV_IBC_THE_INVALID_SERIAL_PORT_DATA = 50057;
  DSV_IP_THE_INVALID_SERIAL_PORT_DATA = 50059;
  DSV_PEPS_THE_INVALID_SERIAL_PORT_DATA = 50061;
  DSV_VCU_THE_INVALID_SERIAL_PORT_DATA = 50066;
  DSV_ABM_CALCULATION_ERROR = 50068;
  DSV_BCM_CALCULATION_ERROR = 50070;
  DSV_BLE_CALCULATION_ERROR = 50071;
  DSV_CSA_CALCULATION_ERROR = 50072;
  DSV_DCT_CALCULATION_ERROR = 50073;
  DSV_EPS_CALCULATION_ERROR = 50074;
  DSV_ESP_CALCULATION_ERROR = 50075;
  DSV_HCU_CALCULATION_ERROR = 50076;
  DSV_HUT_CALCULATION_ERROR = 50077;
  DSV_IBC_CALCULATION_ERROR = 50078;
  DSV_VCU_CALCULATION_ERROR = 50084;
  DSV_FRONT_RADAR_CALCULATION_ERROR = 50085;
  DSV_LEFT_REAR_RADAR_CALCULATION_ERROR = 50086;
  DSV_RIGHT_REAR_RADAR_CALCULATION_ERROR = 50087;
  DSV_PDC_CALCULATION_ERROR = 50088;
  DSV_EPS_FAILURE = 50091;
  DSV_ESP_FAULT = 50092;
  DSV_F_PBOX_FAILURE = 50093;
  DSV_HCM_FAULT = 50094;
  DSV_R_PBOX_FAULT = 50095;
  DSV_RFCM_MALFUNCTION = 50096;
  DSV_ABM_SECOC_FAILED = 50097;
  DSV_BCM_SECOC_FAILED = 50098;
  DSV_ECM_SECOC_FAILED = 50099;
  DSV_ESP_SECOC_FAILED = 50101;
  DSV_VCU_HCU_SECOC_FAILED = 50102;
  DSV_HUT_SECOC_FAILED = 50103;
  DSV_IP_SECOC_FAILED = 50104;
  DSV_R_PBOX_SECOC_FAILED = 50105;
  DSV_T_BOX_SECOC_FAILED = 50106;
  DSV_TPMS_SECOC_FAILED = 50107;
  DSV_DEFAULT_CODE_VERIFY_SYNCHRONIZE_MESSAGES = 50108;
  DSV_DEFAULT_CODE_VERIFY_SECURE_PDU = 50109;
  DSV_SECOC_KEY_MISSING = 50110;
  DSV_SECOC_KEY_STORAGE_ERROR = 50111;
  DSV_SECOC_SYNCHRONIZATION_AUTHENTICATION_MESSAGE_FAILED = 50112;
  DSV_SECOC_SOFTWARE_MODULE_RUNNING_ERROR = 50113;
  DSV_NVM_SYNCHRONIZATION_FAILED = 50114;
  DSV_INMU_ETHERNET_CRC_ERRORS = 50115;
  DSV_CEM_P3_ETHERNET_CRC_ERRORS = 50116;
  DSV_CEM_P2_ETHERNET_CRC_ERRORS = 50117;
  DSV_FLM_ETHERNET_CRC_ERRORS = 50118;
  DSV_INMU_INSUFFICIENT_SQI = 50119;
  DSV_CEM_P3_INSUFFICIENT_SQI_BETWEEN_ADC = 50120;
  DSV_CEM_P2_INSUFFICIENT_SQI_BETWEEN_ADC = 50121;
  DSV_FLM_INSUFFICIENT_SQI_BETWEEN_ADC = 50122;
  DSV_INMU_LINK_DOWN = 50123;
  DSV_CEM_P3_LINK_DOWN = 50124;
  DSV_CEM_P2_LINK_DOWN = 50125;
  DSV_FLM_LINK_DOWN = 50126;
  DSV_LIQUID_COOLING_SYSTEM_INTERNAL_FAILURE = 50127;
  DSV_LIQUID_COOLING_TEMPERATURE_FAULT = 50128;
  DSV_MCU_LEVEL1_HIGH_TEMPERATURE_FAULT = 50129;
  DSV_MCU_LEVEL2_HIGH_TEMPERATURE_FAULT = 50130;
  DSV_SOC_LEVEL1_HIGH_TEMPERATURE_FAULT = 50131;
  DSV_SOC_LEVEL2_HIGH_TEMPERATURE_FAULT = 50132;
  FUSA_MCU_SHUTDOWN_ERROR = 50133;
  FUSA_SOC_SHUTDOWN_ERROR = 50134;
  FUSA_SOC_MCU_ERROR_REPORTING_ERROR = 50135;
  FUSA_ORIN_TMON_SHUTDOWN_ERROR = 50136;
  FUSA_ORIN_VMON_E_THREE_POWER_ON_FAULT = 50137;
  FUSA_STP_IST_OPERATION_FAILURE = 50138;
  FUSA_STP_IST_FAILURE = 50139;
  FUSA_STP_HTX_ABIST_RESULT_TO_CUSTOMER = 50140;
  FUSA_MCU_TMON_ALERT_ERROR = 50141;
  FUSA_ORIN_TMON_ALERT_ERROR = 50142;
  FUSA_ORIN_TMON_REPORT_ERROR = 50143;
  FUSA_MCU_VMON_CAN2_5V_A_OUV_ERROR = 50144;
  FUSA_MCU_VMON_GMSL_OUV_ERROR = 50145;
  FUSA_MCU_VMON_ETH_OUV_ERROR = 50146;
  FUSA_MCU_VMON_MAX20087_12V_OUV_ERROR = 50147;
  FUSA_STP_STARTUP_TEST_ERROR = 50148;
  FUSA_STP_RUNNING_TEST_ERROR = 50149;
  FUSA_SOC_MCU_HEARTBEAT_GPIO_ERROR = 50150;
  FUSA_FSI_DMA_UE = 50151;
  FUSA_GPU_ERROR = 50152;
  FUSA_HOST1X_ERROR = 50153;
  FUSA_ISP_ERROR = 50154;
  FUSA_MGBE_ERROR = 50155;
  FUSA_NVDLA_ERROR = 50156;
  FUSA_NVVIDEOENCODER_ERROR = 50157;
  FUSA_RCE_ERROR = 50158;
  FUSA_VIC_UE = 50159;
  DSV_PARKING_BRAKE_NOT_AVAILABLE = 50162;
  DSV_AIRBAG_FAILURE = 50163;
  DSV_VEHICLE_CONTROLLER_FAILURE = 50164;
  DSV_DRIVING_FUNCTION_TOGGLE_MALFUNCTION = 50165;
  DSV_BLIND_SPOT_WARNING_LIGHT_MALFUNCTION = 50166;
  DSV_DRIVER_SIDE_BLIND_SPOT_WARNING_LIGHT_LOST_COMMUNICATION = 50167;
  DSV_PASSENGER_SIDE_BLIND_SPOT_WARNING_LIGHT_LOST_COMMUNICATION = 50168;
  DSV_INSTRUMENT_COMMUNICATION_FAULTY = 50169;
  DSV_DRIVING_EPS_HOLD_HANDS_FAILURE = 50170;
  DSV_AEB_FUNCTION_HANDSHAKE_NOT_AVAILABLE = 50171;
  DSV_MASTER_CYLINDER_PRESSURE_INVALID = 50172;
  DSV_EPB_MALFUNCTION = 50173;
  DSV_ACC_INTERFACE_NOT_RESPONDING = 50174;
  DSV_CSA_THE_INVALID_SERIAL_PORT_DATA = 50175;
  DSV_DVR_CAR_RECORDER_MALFUNCTION = 50176;
  DSV_AC_INVALID_CABIN_TEMPERATURE_FEEDBACK = 50177;
  DSV_CRUISE_SPEED_ADJUSTMENT_PADDLE_MALFUNCTION = 50178;
  DSV_EPS_CORNERING_INVALID = 50179;
  DSV_EPS_HAND_TORQUE_NOT_AVAILABLE = 50180;
  DSV_PARKING_POWER_NOT_AVAILABLE = 50181;
  DSV_ACCELERATOR_PEDAL_INVALID = 50182;
  DSV_AEB_SENSITIVITY_ADJUSTMENT_SWITCH_INVALID = 50183;
  DSV_VEHICLE_STANDSTILL_INVALID = 50185;
  DSV_LSS_WARNING_MODE_SELECTOR_SWITCH_INVALID = 50186;
  DSV_PARKING_MODE_SELECTION_INVALID = 50187;
  DSV_CONTINUED_PARKING_SELECTION_INVALID = 50188;
  DSV_SCREEN_TAP_INVALID = 50189;
  DSV_WORKSHOP_HOURLY_RANGE_ADJUSTMENT_PADDLES_MALFUNCTION = 50190;
  DSV_INVALID_ESP_RESPONSE_RCTB = 50191;
  DSV_ESP_HANDSHAKE_FAILURE = 50192;
  DSV_GEAR_POSITION_INVALID = 50193;
  FUSA_SAFETY_SERV_FSI_EPS_BSW_ERR_QUEUE_OVERFLOW = 50194;

  /* 51000~55999 CALIBRATION */
  /* 51000~51499 CAMERA CALIBRATION */
  /* 51500~51999 LiDAR CALIBRATION */
  /* 52000~52499 INS CALIBRATION */
  /* 52500~52999 RADAR CALIBRATION */
  /* 53000~53499 USS CALIBRATION */
  /* 53500~559999 RESERVE   */

  /* 51000~51499 CAMERA CALIBRATION */
  CALIBRATION_CAMEAR_MISSING_CALIB_PARAM = 51000;

  // 前视近距相机标定丢失 [对应 camera_1]
  CALIBRATION_CAMEAR_FRONT_WIDE_MISSING_CALIB_PARAM = 51001;

  // 前视中距相机标定丢失 [对应traffic_1 预留]
  CALIBRATION_CAMEAR_FRONT_MID_MISSING_CALIB_PARAM = 51002;

  // 前视远距相机摄像头标定丢失 [对应 traffic_2]
  CALIBRATION_CAMEAR_FRONT_FAR_MISSING_CALIB_PARAM = 51003;

  // 后视摄像头标定丢失 [对应camera_4]
  CALIBRATION_CAMEAR_REAR_MISSING_CALIB_PARAM = 51004;

  // 左侧中距相机（向前看）标定丢失 [对应 camera_5]
  CALIBRATION_CAMEAR_LEFT_FRONT_MISSING_CALIB_PARAM = 51005;

  // 左侧中距相机（向后看）标定丢失 [对应 camera_6]
  CALIBRATION_CAMEAR_LEFT_REAR_MISSING_CALIB_PARAM = 51006;

  // 右侧中距相机（向前看）标定丢失 [对应camera_3]
  CALIBRATION_CAMEAR_RIGHT_FRONT_MISSING_CALIB_PARAM = 51007;
  // 右侧中距相机（向后看）标定丢失 [对应 camera_2]
  CALIBRATION_CAMEAR_RIGHT_REAR_MISSING_CALIB_PARAM = 51008;

  // 前侧鱼眼相机标定丢失 [对应panoramic_1]
  CALIBRATION_CAMEAR_FRONT_FISHEYE_MISSING_CALIB_PARAM = 51009;
  // 后侧鱼眼相机标定丢失 [对应 panoramic_3]
  CALIBRATION_CAMEAR_REAR_FISHEYE_MISSING_CALIB_PARAM = 51010;

  // 左侧鱼眼相机标定丢失 [对应 panoramic_3]
  CALIBRATION_CAMEAR_LEFT_FISHEYE_MISSING_CALIB_PARAM = 51011;
  // 右侧鱼眼相机标定丢失 [对应 panoramic_2]
  CALIBRATION_CAMEAR_RIGHT_FISHEYE_MISSING_CALIB_PARAM = 51012;

  //启动标定录制
  CALIBRATION_START_CALIB_BAG_RECORD = 51100;
  CALIBRATION_START_ONLINE_CALIB_BAG_RECORD = 51101;
  CALIBRATION_START_CALIB_MONITOR_BAG_RECORD = 51102;
  CALIBRATION_START_AVM_CALIB_BAG_RECORD = 51103;
  CALIBRATION_START_LIDAR_CALIB_BAG_RECORD = 51104;


  // 标定参数异常 [adas_calib_monitor]
  // 前视近距相机标定参数异常 [对应 camera_1]
  CALIBRATION_CAMEAR_FRONT_WIDE_CALIB_PARAM_ABNORMAL = 51201;

  // 前视中距相机标定参数异常 [对应traffic_1 预留]
  CALIBRATION_CAMEAR_FRONT_MID_CALIB_PARAM_ABNORMAL = 51202;

  // 前视远距相机摄像头标定参数异常 [对应 traffic_2]
  CALIBRATION_CAMEAR_FRONT_FAR_CALIB_PARAM_ABNORMAL = 51203;

  // 后视摄像头标定参数异常 [对应camera_4]
  CALIBRATION_CAMEAR_REAR_CALIB_PARAM_ABNORMAL = 51204;

  // 左侧中距相机（向前看）标定参数异常 [对应 camera_5]
  CALIBRATION_CAMEAR_LEFT_FRONT_CALIB_PARAM_ABNORMAL = 51205;

  // 左侧中距相机（向后看）标定参数异常 [对应 camera_6]
  CALIBRATION_CAMEAR_LEFT_REAR_CALIB_PARAM_ABNORMAL = 51206;

  // 右侧中距相机（向前看）标定参数异常 [对应camera_3]
  CALIBRATION_CAMEAR_RIGHT_FRONT_CALIB_PARAM_ABNORMAL = 51207;
  // 右侧中距相机（向后看）标定参数异常 [对应 camera_2]
  CALIBRATION_CAMEAR_RIGHT_REAR_CALIB_PARAM_ABNORMAL = 51208;

  // 前侧鱼眼相机标定参数异常 [对应panoramic_1]
  CALIBRATION_CAMEAR_FRONT_FISHEYE_CALIB_PARAM_ABNORMAL = 51209;
  // 后侧鱼眼相机标定参数异常 [对应 panoramic_3]
  CALIBRATION_CAMEAR_REAR_FISHEYE_CALIB_PARAM_ABNORMAL = 51210;

  // 左侧鱼眼相机标定参数异常 [对应 panoramic_4]
  CALIBRATION_CAMEAR_LEFT_FISHEYE_CALIB_PARAM_ABNORMAL = 51211;
  // 右侧鱼眼相机标定参数异常 [对应 panoramic_2]
  CALIBRATION_CAMEAR_RIGHT_FISHEYE_CALIB_PARAM_ABNORMAL = 51212;

  // sn不匹配(未标定)
  // 前视近距相机SN不匹配 [对应 camera_1]
  CALIBRATION_CAMERA_FRONT_WIDE_SN_MISMATCH = 51301;

  // 前视中距相机SN不匹配 [对应traffic_1 预留]
  CALIBRATION_CAMERA_FRONT_MID_SN_MISMATCH = 51302;

  // 前视远距相机摄像头SN不匹配 [对应 traffic_2]
  CALIBRATION_CAMERA_FRONT_LONG_SN_MISMATCH = 51303;

  // 后视摄像头SN不匹配 [对应camera_4]
  CALIBRATION_CAMERA_REAR_SN_MISMATCH = 51304;

  // 左侧中距相机（向前看）SN不匹配 [对应 camera_5]
  CALIBRATION_CAMERA_LEFT_FRONT_SN_MISMATCH = 51305;

  // 左侧中距相机（向后看）SN不匹配 [对应 camera_6]
  CALIBRATION_CAMERA_LEFT_REAR_SN_MISMATCH = 51306;

  // 右侧中距相机（向前看）SN不匹配 [对应camera_3]
  CALIBRATION_CAMERA_RIGHT_FRONT_SN_MISMATCH = 51307;
  // 右侧中距相机（向后看）SN不匹配 [对应 camera_2]
  CALIBRATION_CAMERA_RIGHT_REAR_SN_MISMATCH = 51308;

  // 前侧鱼眼相机SN不匹配 [对应panoramic_1]
  CALIBRATION_CAMERA_FRONT_FISHEYE_SN_MISMATCH = 51309;
  // 后侧鱼眼相机SN不匹配 [对应 panoramic_3]
  CALIBRATION_CAMERA_REAR_FISHEYE_SN_MISMATCH = 51310;

  // 左侧鱼眼相机SN不匹配 [对应 panoramic_4]
  CALIBRATION_CAMERA_LEFT_FISHEYE_SN_MISMATCH = 51311;
  // 右侧鱼眼相机SN不匹配 [对应 panoramic_2]
  CALIBRATION_CAMERA_RIGHT_FISHEYE_SN_MISMATCH = 51312;

  // Lidar标定丢失 INMU标定丢失 INMU动标异常
  /* 51500~51999 LiDAR CALIBRATION */
  // Lidar标定丢失
  CALIBRATION_LIDAR_MISSING_CALIB_PARAM = 51500;

  // Lidar标定参数异常
  CALIBRATION_LIDAR_CALIB_PARAM_ABNORMAL = 51600;
  // Lidar SN号不匹配
  CALIBRATION_LIDAR_FLL_SN_MISMATCH = 51700;

  /* 52000~52499 INS CALIBRATION */
  // IMU静态标定结果丢失
  CALIBRATION_IMU_MISSING_CALIB_PARAM = 52000;

  // IMU静态标定结果丢失
  CALIBRATION_IMU_MISSING_STATIC_CALIB_PARAM = 52001;

  // IMU动态态标定结果丢失
  CALIBRATION_IMU_MISSING_DYNAMIC_CALIB_PARAM = 52002;

  // IMU静态标定结果异常
  CALIBRATION_IMU_STATIC_CALIB_PARAM_ABNORMAL = 52101;

  // IMU动态态标定结果异常
  CALIBRATION_IMU_DYNAMIC_CALIB_PARAM_ABNORMAL = 52102;

  /* 52500~52999 RADAR CALIBRATION */
  CALIBRATION_RADAR_MISSING_CALIB_PARAM = 52500;

  /* 53000~53499 USS CALIBRATION */
  CALIBRATION_USS_MISSING_CALIB_PARAM = 53000;

  /* 56000~60999 SOMEIP_ADAPTER */
  /* 56000~56499 ADPT_CANBUS */
  ADPT_CANBUS_INIT_FAILED = 56000;
  ADPT_CANBUS_SERVICE_STATE =
      56001;  // common/proto/someip_adapter/service_state.proto
  ADPT_CANBUS_DATA_PARSE_FAILED = 56002;
  ADPT_CANBUS_E2E_CHECK_FAILED = 56003;
  ADPT_CANBUS_CALLBACK_TIMEOUT = 56004;
  ADPT_CANBUS_FRAME_TIMEOUT = 56005;
  ADPT_CANBUS_FIND_SERVICE_TIMEOUT = 56006;
  ADPT_CANBUS_START_EXCEPTION_CATCH = 56007;

  /* 56500~56999 ADPT_DOWNSTREAM */
  ADPT_DOWNSTREAM_INIT_FAILED = 56500;
  ADPT_DOWNSTREAM_SERVICE_STATE =
      56501;  // common/proto/someip_adapter/service_state.proto
  ADPT_DOWNSTREAM_DATA_PARSE_FAILED = 56502;
  ADPT_DOWNSTREAM_E2E_CHECK_FAILED = 56503;
  ADPT_DOWNSTREAM_CALLBACK_TIMEOUT = 56504;
  ADPT_DOWNSTREAM_FRAME_TIMEOUT = 56505;
  ADPT_DOWNSTREAM_FIND_SERVICE_TIMEOUT = 56506;
  ADPT_DOWNSTREAM_START_EXCEPTION_CATCH = 56507;

  /* 57000~57499 ADPT_UPSTREAM */
  ADPT_UPSTREAM_INIT_FAILED = 57000;
  ADPT_UPSTREAM_SERVICE_STATE =
      57001;  // common/proto/someip_adapter/service_state.proto
  ADPT_UPSTREAM_DATA_PARSE_FAILED = 57002;
  ADPT_UPSTREAM_E2E_CHECK_FAILED = 57003;
  ADPT_UPSTREAM_CALLBACK_TIMEOUT = 57004;
  ADPT_UPSTREAM_FRAME_TIMEOUT = 57005;
  ADPT_UPSTREAM_FIND_SERVICE_TIMEOUT = 57006;
  ADPT_UPSTREAM_START_EXCEPTION_CATCH = 57007;

  /* 57500~57999 ADPT_SENSOR_INS */
  ADPT_SENSOR_INS_INIT_FAILED = 57500;
  ADPT_SENSOR_INS_SERVICE_STATE =
      57501;  // common/proto/someip_adapter/service_state.proto
  ADPT_SENSOR_INS_DATA_PARSE_FAILED = 57502;
  ADPT_SENSOR_INS_E2E_CHECK_FAILED = 57503;
  ADPT_SENSOR_INS_CALLBACK_TIMEOUT = 57504;
  ADPT_SENSOR_INS_FRAME_TIMEOUT = 57505;
  ADPT_SENSOR_INS_FIND_SERVICE_TIMEOUT = 57506;
  ADPT_SENSOR_INS_START_EXCEPTION_CATCH = 57507;
  ADPT_SENSOR_INS_IMU_RAW_DATA_STRUCT_E2E_CHECK_FAILED = 57508;
  ADPT_SENSOR_INS_DIAG_INFO_DATA_STRUCT_E2E_CHECK_FAILED = 57509;
  ADPT_SENSOR_INS_GPS_DATA_STRUCT_E2E_CHECK_FAILED = 57510;
  ADPT_SENSOR_INS_IMU_RAW_DATA_STRUCT_LOST = 57511;
  ADPT_SENSOR_INS_DIAG_INFO_DATA_STRUCT_LOST = 57512;
  ADPT_SENSOR_INS_GPS_DATA_STRUCT_LOST = 57513;
  ADPT_SENSOR_INS_IMU_RAW_DATA_STRUCT_LOST_TOO_LONG = 57514;

  /* 58000~58499 ADPT_DOMAINV2 */
  ADPT_DOMAINV2_INIT_FAILED = 58000;
  ADPT_DOMAINV2_SERVICE_STATE =
      58001;  // common/proto/someip_adapter/service_state.proto
  ADPT_DOMAINV2_DATA_PARSE_FAILED = 58002;
  ADPT_DOMAINV2_E2E_CHECK_FAILED = 58003;
  ADPT_DOMAINV2_CALLBACK_TIMEOUT = 58004;
  ADPT_DOMAINV2_FRAME_TIMEOUT = 58005;
  ADPT_DOMAINV2_FIND_SERVICE_TIMEOUT = 58006;
  ADPT_DOMAINV2_START_EXCEPTION_CATCH = 58007;

  /* 58500~58999 ADPT_DOMAINV3 */
  ADPT_DOMAINV3_INIT_FAILED = 58500;
  ADPT_DOMAINV3_SERVICE_STATE =
      58501;  // common/proto/someip_adapter/service_state.proto
  ADPT_DOMAINV3_DATA_PARSE_FAILED = 58502;
  ADPT_DOMAINV3_E2E_CHECK_FAILED = 58503;
  ADPT_DOMAINV3_CALLBACK_TIMEOUT = 58504;
  ADPT_DOMAINV3_FRAME_TIMEOUT = 58505;
  ADPT_DOMAINV3_FIND_SERVICE_TIMEOUT = 58506;
  ADPT_DOMAINV3_START_EXCEPTION_CATCH = 58507;

  /* 61000~61099 DATA_AGENT */
  DATA_AGENT_DATA_SENSITIVE_DELETE = 61000;
  DATA_AGENT_DATA_DESENSITATION_DELETE = 61001;

  /* 61100~61199 UPLOADER */
  UPLOADER_DATA_MODIFIED = 61100;
  UPLOADER_BATCH_FAILED = 61101;
  UPLOADER_TRAFFIC_STATISTICS = 61102;
  UPLOADER_PERMISSION_ERROR = 61103;

  /* 61200~61299 SAFE_APP */
  SAFE_APP_FAILURE_TAG = 61200;  // common/proto/safe_app/safe_app_event.proto
                                 // message:SafeAppTagInfo
  SAFE_APP_SUCCESS_TAG = 61201;  // common/proto/safe_app/safe_app_event.proto
                                 // message:SafeAppTagInfo
  SAFE_APP_COLLECTION_TAG =
      61202;  // common/proto/safe_app/safe_app_event.proto
  SAFE_APP_DRIVER_INFO = 61203;   // 安全员（驾驶员）信息
  SAFE_APP_COLLECTION_START_RECORD = 61204;   // 数采，开始录制
  SAFE_APP_COLLECTION_PAUSE_RECORD = 61205;   // 数采，暂停录制

  /* 61300~62299 BYD_ALARM */
  BYD_RADAR_FRONT_CANBUS_E2E_FAULT = 61300;
  BYD_RADAR_LF_CAN_E2E_FAULT = 61301;
  BYD_RADAR_LR_CAN_E2E_FAULT = 61302;
  BYD_RADAR_RR_CAN_E2E_FAULT = 61303;
  BYD_RADAR_RF_CAN_E2E_FAULT = 61304;
  BYD_RADAR_FRONT_CAN_TIMEOUT = 61305;
  BYD_RADAR_LF_CAN_TIMEOUT = 61306;
  BYD_RADAR_LR_CAN_TIMEOUT = 61307;
  BYD_RADAR_RR_CAN_TIMEOUT = 61308;
  BYD_RADAR_RF_CAN_TIMEOUT = 61309;
  BYD_USS_CAN_E2E_FAULT = 61310;
  BYD_USS_CAN_TIMEOUT = 61311;
  BYD_GNSS_COM_TIMEOUT = 61312;
  BYD_GNSS_COM_CHECKSUM_FAULT = 61313;
  BYD_CAMERA_REAR_COM_TIMEOUT = 61314;
  BYD_CAMERA_FRONT_RIGHT_COM_TIMEOUT = 61315;
  BYD_CAMERA_FRONT_NARROW_COM_TIMEOUT = 61316;
  BYD_CAMERA_LEFT_FRONT_COM_TIMEOUT = 61317;
  BYD_CAMERA_RIGHT_FRONT_COM_TIMEOUT = 61318;
  BYD_CAMERA_RIGHT_REAR_COM_TIMEOUT = 61319;
  BYD_CAMERA_LEFT_REAR_COM_TIMEOUT = 61320;
  BYD_CAMERA_FRONT_PANORAMIC_COM_TIMEOUT = 61321;
  BYD_CAMERA_RIGHT_PANORAMIC_COM_TIMEOUT = 61322;
  BYD_CAMERA_REAR_PANORAMIC_COM_TIMEOUT = 61323;
  BYD_CAMERA_LEFT_PANORAMIC_COM_TIMEOUT = 61324;
  BYD_CAMERA_FRONT_LEFT_COM_TIMEOUT = 61325;
  BYD_CAMERA_REAR_FRAME_RATIO_FAULT = 61326;
  BYD_CAMERA_FRONT_RIGHT_FRAME_RATIO_FAULT = 61327;
  BYD_CAMERA_FRONT_NARROW_FRAME_RATIO_FAULT = 61328;
  BYD_CAMERA_LEFT_FRONT_FRAME_RATIO_FAULT = 61329;
  BYD_CAMERA_RIGHT_FRONT_FRAME_RATIO_FAULT = 61330;
  BYD_CAMERA_RIGHT_REAR_FRAME_RATIO_FAULT = 61331;
  BYD_CAMERA_LEFT_REAR_FRAME_RATIO_FAULT = 61332;
  BYD_CAMERA_FRONT_PANORAMIC_FRAME_RATIO_FAULT = 61333;
  BYD_CAMERA_RIGHT_PANORAMIC_FRAME_RATIO_FAULT = 61334;
  BYD_CAMERA_REAR_PANORAMIC_FRAME_RATIO_FAULT = 61335;
  BYD_CAMERA_LEFT_PANORAMIC_FRAME_RATIO_FAULT = 61336;
  BYD_CAMERA_FRONT_LEFT_FRAME_RATIO_FAULT = 61337;
  BYD_CAMERA_REAR_FRAME_DELAY = 61338;
  BYD_CAMERA_FRONT_RIGHT_FRAME_DELAY = 61339;
  BYD_CAMERA_FRONT_NARROW_FRAME_DELAY = 61340;
  BYD_CAMERA_LEFT_FRONT_FRAME_DELAY = 61341;
  BYD_CAMERA_RIGHT_FRONT_FRAME_DELAY = 61342;
  BYD_CAMERA_RIGHT_REAR_FRAME_DELAY = 61343;
  BYD_CAMERA_LEFT_REAR_FRAME_DELAY = 61344;
  BYD_CAMERA_FRONT_PANORAMIC_FRAME_DELAY = 61345;
  BYD_CAMERA_RIGHT_PANORAMIC_FRAME_DELAY = 61346;
  BYD_CAMERA_REAR_PANORAMIC_FRAME_DELAY = 61347;
  BYD_CAMERA_LEFT_PANORAMIC_FRAME_DELAY = 61348;
  BYD_CAMERA_FRONT_LEFT_FRAME_DELAY = 61349;
  BYD_CAMERA_REAR_HARDWARE_FAULT = 61350;
  BYD_CAMERA_FRONT_RIGHT_HARDWARE_FAULT = 61351;
  BYD_CAMERA_FRONT_NARROW_HARDWARE_FAULT = 61352;
  BYD_CAMERA_LEFT_FRONT_HARDWARE_FAULT = 61353;
  BYD_CAMERA_RIGHT_FRONT_HARDWARE_FAULT = 61354;
  BYD_CAMERA_RIGHT_REAR_HARDWARE_FAULT = 61355;
  BYD_CAMERA_LEFT_REAR_HARDWARE_FAULT = 61356;
  BYD_CAMERA_FRONT_PANORAMIC_HARDWARE_FAULT = 61357;
  BYD_CAMERA_RIGHT_PANORAMIC_HARDWARE_FAULT = 61358;
  BYD_CAMERA_REAR_PANORAMIC_HARDWARE_FAULT = 61359;
  BYD_CAMERA_LEFT_PANORAMIC_HARDWARE_FAULT = 61360;
  BYD_CAMERA_FRONT_LEFT_HARDWARE_FAULT = 61361;
  BYD_CAMERA_REAR_THREAD_SUSPEND = 61362;
  BYD_CAMERA_FRONT_RIGHT_THREAD_SUSPEND = 61363;
  BYD_CAMERA_FRONT_NARROW_THREAD_SUSPEND = 61364;
  BYD_CAMERA_LEFT_FRONT_THREAD_SUSPEND = 61365;
  BYD_CAMERA_RIGHT_FRONT_THREAD_SUSPEND = 61366;
  BYD_CAMERA_RIGHT_REAR_THREAD_SUSPEND = 61367;
  BYD_CAMERA_LEFT_REAR_THREAD_SUSPEND = 61368;
  BYD_CAMERA_FRONT_PANORAMIC_THREAD_SUSPEND = 61369;
  BYD_CAMERA_RIGHT_PANORAMIC_THREAD_SUSPEND = 61370;
  BYD_CAMERA_REAR_PANORAMIC_THREAD_SUSPEND = 61371;
  BYD_CAMERA_LEFT_PANORAMIC_THREAD_SUSPEND = 61372;
  BYD_CAMERA_FRONT_LEFT_THREAD_SUSPEND = 61373;
  BYD_CAMERA_REAR_OVER_TEMPERATURE_LV1 = 61374;
  BYD_CAMERA_FRONT_RIGHT_OVER_TEMPERATURE_LV1 = 61375;
  BYD_CAMERA_FRONT_NARROW_OVER_TEMPERATURE_LV1 = 61376;
  BYD_CAMERA_LEFT_FRONT_OVER_TEMPERATURE_LV1 = 61377;
  BYD_CAMERA_RIGHT_FRONT_OVER_TEMPERATURE_LV1 = 61378;
  BYD_CAMERA_RIGHT_REAR_OVER_TEMPERATURE_LV1 = 61379;
  BYD_CAMERA_LEFT_REAR_OVER_TEMPERATURE_LV1 = 61380;
  BYD_CAMERA_FRONT_LEFT_OVER_TEMPERATURE_LV1 = 61381;
  BYD_CAMERA_REAR_OVER_TEMPERATURE_LV2 = 61382;
  BYD_CAMERA_FRONT_RIGHT_OVER_TEMPERATURE_LV2 = 61383;
  BYD_CAMERA_FRONT_NARROW_OVER_TEMPERATURE_LV2 = 61384;
  BYD_CAMERA_LEFT_FRONT_OVER_TEMPERATURE_LV2 = 61385;
  BYD_CAMERA_RIGHT_FRONT_OVER_TEMPERATURE_LV2 = 61386;
  BYD_CAMERA_RIGHT_REAR_OVER_TEMPERATURE_LV2 = 61387;
  BYD_CAMERA_LEFT_REAR_OVER_TEMPERATURE_LV2 = 61388;
  BYD_CAMERA_FRONT_LEFT_OVER_TEMPERATURE_LV2 = 61389;
  BYD_SOC_CPU_OVERLOAD = 61390;
  BYD_SOC_MEMORY_OVERLOAD = 61391;
  BYD_SOC_STORAGE_OVERLOAD = 61392;
  BYD_ETH_OVERLOAD_MCU = 61393;
  BYD_CAMERA_REAR_POWER_OFF = 61394;
  BYD_CAMERA_FRONT_RIGHT_POWER_OFF = 61395;
  BYD_CAMERA_FRONT_NARROW_POWER_OFF = 61396;
  BYD_CAMERA_LEFT_FRONT_POWER_OFF = 61397;
  BYD_CAMERA_RIGHT_FRONT_POWER_OFF = 61398;
  BYD_CAMERA_RIGHT_REAR_POWER_OFF = 61399;
  BYD_CAMERA_LEFT_REAR_POWER_OFF = 61400;
  BYD_CAMERA_FRONT_PANORAMIC_POWER_OFF = 61401;
  BYD_CAMERA_RIGHT_PANORAMIC_POWER_OFF = 61402;
  BYD_CAMERA_REAR_PANORAMIC_POWER_OFF = 61403;
  BYD_CAMERA_LEFT_PANORAMIC_POWER_OFF = 61404;
  BYD_CAMERA_FRONT_LEFT_POWER_OFF = 61405;
  BYD_SOC_OVER_TEMPERATURE = 61406;
  BYD_SOC_UNDER_TEMPERATURE = 61407;
  BYD_SOC_SOFTWARE_SELF_STARTING_FAULT = 61408;
  BYD_SOC_HEARTBEAT_FAULT_SOC_EM = 61409;
  BYD_DILINK_ETH_CRC_FAULT = 61410;
  BYD_LIDAR_ETH_CRC_FAULT = 61411;
  BYD_ADAS_DOMAIN_ETH_CRC_FAULT = 61412;
  BYD_DILINK_ETH_LINKDOWN = 61413;
  BYD_LIDAR_ETH_LINKDOWN = 61414;
  BYD_ADAS_DOMAIN_ETH_LINKDOWN = 61415;
  BYD_DILINK_ETH_SQI_FAULT = 61416;
  BYD_LIDAR_ETH_SQI_FAULT = 61417;
  BYD_OTA_UPDATE_FAULT_ROLLBACK_SUCCESS = 61418;
  BYD_OTA_UPDATE_FAULT_ROLLBACK_FAIL = 61419;
  BYD_CAMERA_REAR_CRC_FAULT = 61420;
  BYD_CAMERA_FRONT_RIGHT_CRC_FAULT = 61421;
  BYD_CAMERA_FRONT_NARROW_CRC_FAULT = 61422;
  BYD_CAMERA_LEFT_FRONT_CRC_FAULT = 61423;
  BYD_CAMERA_RIGHT_FRONT_CRC_FAULT = 61424;
  BYD_CAMERA_LEFT_REAR_CRC_FAULT = 61425;
  BYD_CAMERA_RIGHT_REAR_CRC_FAULT = 61426;
  BYD_CAMERA_FRONT_PANORAMIC_CRC_FAULT = 61427;
  BYD_CAMERA_RIGHT_PANORAMIC_CRC_FAULT = 61428;
  BYD_CAMERA_REAR_PANORAMIC_CRC_FAULT = 61429;
  BYD_CAMERA_LEFT_PANORAMIC_CRC_FAULT = 61430;
  BYD_CAMERA_FRONT_LEFT_CRC_FAULT = 61431;
  BYD_IMU_COM_CHECKSUM_FAULT = 61432;
  BYD_IMU_COM_TIMEOUT = 61433;
  BYD_IMU_INERNAL_FAULT = 61434;
  BYD_CAMERA_REAR_INTRINSIC_PARAMETER_LODA_FAIL = 61435;
  BYD_CAMERA_FRONT_RIGHT_INTRINSIC_PARAMETER_LODA_FAIL = 61436;
  BYD_CAMERA_FRONT_NARROW_INTRINSIC_PARAMETER_LODA_FAIL = 61437;
  BYD_CAMERA_LEFT_FRONT_INTRINSIC_PARAMETER_LODA_FAIL = 61438;
  BYD_CAMERA_RIGHT_FRONT_INTRINSIC_PARAMETER_LODA_FAIL = 61439;
  BYD_CAMERA_RIGHT_REAR_INTRINSIC_PARAMETER_LODA_FAIL = 61440;
  BYD_CAMERA_LEFT_REAR_INTRINSIC_PARAMETER_LODA_FAIL = 61441;
  BYD_CAMERA_FRONT_PANORAMIC_INTRINSIC_PARAMETER_LODA_FAIL = 61442;
  BYD_CAMERA_RIGHT_PANORAMIC_INTRINSIC_PARAMETER_LODA_FAIL = 61443;
  BYD_CAMERA_REAR_PANORAMIC_INTRINSIC_PARAMETER_LODA_FAIL = 61444;
  BYD_CAMERA_LEFT_PANORAMIC_INTRINSIC_PARAMETER_LODA_FAIL = 61445;
  BYD_CAMERA_FRONT_LEFT_INTRINSIC_PARAMETER_LODA_FAIL = 61446;
  BYD_MCU_OVER_TEMPERATURE_LEVEL1 = 61447;
  BYD_MCU_OVER_TEMPERATURE_LEVEL2 = 61448;
  BYD_MCU_UNDER_TEMPERATURE = 61449;
  BYD_EPS_CAN_E2E_FAULT = 61450;
  BYD_IPB_CAN_E2E_FAULT = 61451;
  BYD_CCU_CAN_E2E_FAULT = 61452;
  BYD_BMS_CAN_E3E_FAULT = 61453;
  BYD_SRS_CAN_E2E_FAULT = 61454;
  BYD_LEFT_BCM_CAN_E2E_FAULT = 61455;
  BYD_REAR_BCM_CAN_E4E_FAULT = 61456;
  BYD_RIGHT_BCM_CAN_E5E_FAULT = 61457;
  BYD_CS_CAN_E6E_FAULT = 61458;
  BYD_SWS_HOD_CAN_E7E_FAULT = 61459;
  BYD_DISUS_CAN_E2E_FAULT = 61460;
  BYD_DEDIA_CAN_E2E_FAULT = 61461;
  BYD_LTE4G_CAN_E8E_FAULT = 61462;
  BYD_SEC_CAN_E9E_FAULT = 61463;
  BYD_SCU_CAN_E10E_FAULT = 61464;
  BYD_DCDC_CAN_E11E_FAULT = 61465;
  BYD_CPD_CAN_E12E_FAULT = 61466;
  BYD_VCU_CAN_E2E_FAULT = 61467;
  BYD_TCU_CAN_E2E_FAULT = 61468;
  BYD_EPS_CAN_TIMEOUT = 61469;
  BYD_IPB_CAN_TIMEOUT = 61470;
  BYD_CCU_CAN_TIMEOUT = 61471;
  BYD_ECM_CAN_TIMEOUT = 61472;
  BYD_BMS_CAN_TIMEOUT = 61473;
  BYD_SRS_CAN_TIMEOUT = 61474;
  BYD_LEFT_BCM_CAN_TIMEOUT = 61475;
  BYD_REAR_BCM_CAN_TIMEOUT = 61476;
  BYD_RIGHT_BCM_CAN_TIMEOUT = 61477;
  BYD_CS_CAN_TIMEOUT = 61478;
  BYD_SWS_HOD_CAN_TIMEOUT = 61479;
  BYD_DISUS_CAN_TIMEOUT = 61480;
  BYD_MEDIA_CAN_TIMEOUT = 61481;
  BYD_LTE4G_CAN_TIMEOUT = 61482;
  BYD_SEC_CAN_TIMEOUT = 61483;
  BYD_SCU_CAN_TIMEOUT = 61484;
  BYD_FLL_FLR_CAN_TIMEOUT = 61485;
  BYD_MSR_DRV_CAN_TIMEOUT = 61486;
  BYD_DCDC_CAN_TIMEOUT = 61487;
  BYD_DHC_P_CAN_TIMEOUT = 61488;
  BYD_CDP_CAN_TIMEOUT = 61489;
  BYD_PPS_CAN_TIMEOUT = 61490;
  BYD_VCU_CAN_TIMEOUT = 61491;
  BYD_TCU_CAN_TIMEOUT = 61492;
  BYD_SCPA_CAN_TIMEOUT = 61493;
  BYD_SOC_POWERSUPPLY_OV_UV_5V = 61494;
  BYD_MCU_POWERSUPPLY_OV_UV_5V = 61495;
  BYD_SOC_POWERSUPPLY_OV_UV_3V3 = 61496;
  BYD_MCU_POWERSUPPLY_OV_UV_3V3 = 61497;
  BYD_IMU_GNSS_POWERSUPPLY_FAULT = 61498;
  BYD_GNSS_ANT_POWERSUPPLY_OV_UV_3V3 = 61499;
  BYD_SOC_HEARTBEAT_FAULT_MCU = 61500;
  BYD_MCU_HARDWARE_FATAL_FAULT = 61501;
  BYD_MCU_RESET_REASON = 61502;
  BYD_MCU_OS_FAULT = 61503;
  BYD_BATTERY_OVER_VOLTAGE_LV3 = 61504;
  BYD_BATTERY_OVER_VOLTAGE_LV2 = 61505;
  BYD_BATTERY_OVER_VOLTAGE_LV1 = 61506;
  BYD_BATTERY_UNDER_VOLTAGE_LV1 = 61507;
  BYD_BATTERY_UNDER_VOLTAGE_LV2 = 61508;
  BYD_BATTERY_UNDER_VOLTAGE_LV3 = 61509;
  BYD_ADAS_CAN_BUSOFF = 61510;
  BYD_MEDIA_BUSOFF = 61511;
  BYD_CHASSIS_CAN_BUSOFF = 61512;
  BYD_USS_CAN_BUSOFF = 61513;
  BYD_RADAR_LF_CAN_BUSOFF = 61514;
  BYD_RADAR_RF_CAN_BUSOFF = 61515;
  BYD_RADAR_FRONT_BUSOFF = 61516;
  BYD_RADAR_LR_CAN_BUSOFF = 61517;
  BYD_RADAR_RR_CAN_BUSOFF = 61518;
  BYD_MCU_DATATIME_SYNC_FAULT = 61519;
  BYD_MCU_MANAGETIME_SYNC_FAULT = 61520;
  BYD_CAN_TRANS_POWERSUPPLY_FAULT_5V = 61521;
  BYD_PANORAMIC_CAMERA_SER_DES_POWERSUPPLY_FAULT = 61522;
  BYD_ETH_POWERSUPPLY_FAULT = 61523;
  BYD_SOC_POWERSUPPLY_FAULT_5V = 61524;
  BYD_ETH_CAMERA_SER_DES_POWERSUPPLY_FAULT_5V = 61525;
  BYD_MCU_TO_SOC_ETH_E2E_FAULT = 61526;
  BYD_SOC_TO_MCU_ETH_E2E_FAULT = 61527;
  BYD_CAMERA_REAR_POWERESUPPLY_OV_UV_12V = 61528;
  BYD_CAMERA_FRONT_RIGHT_POWERESUPPLY_OV_UV_12V = 61529;
  BYD_CAMERA_FRONT_NARROW_POWERESUPPLY_OV_UV_12V = 61530;
  BYD_CAMERA_LEFT_FRONT_POWERESUPPLY_OV_UV_12V = 61531;
  BYD_CAMERA_RIGHT_FRONT_POWERESUPPLY_OV_UV_12V = 61532;
  BYD_CAMERA_RIGHT_REAR_POWERESUPPLY_OV_UV_12V = 61533;
  BYD_CAMERA_LEFT_REAR_POWERESUPPLY_OV_UV_12V = 61534;
  BYD_CAMERA_FRONT_PANORAMIC_POWERESUPPLY_OV_UV_12V = 61535;
  BYD_CAMERA_RIGHT_PANORAMIC_POWERESUPPLY_OV_UV_12V = 61536;
  BYD_CAMERA_REAR_PANORAMIC_POWERESUPPLY_OV_UV_12V = 61537;
  BYD_CAMERA_LEFT_PANORAMIC_POWERESUPPLY_OV_UV_12V = 61538;
  BYD_CAMERA_FRONT_LEFT_POWERESUPPLY_OV_UV_12V = 61539;
  BYD_LIDAR_TIME_SYNC_FAULT = 61540;
  BYD_LIDAR_VOLTAGE_FAULT = 61541;
  BYD_LIDAR_BLIND_FAULT = 61542;
  BYD_LIDAR_TEMP_FAULT = 61543;
  BYD_LIDAR_INTERNAL_FAULT = 61544;
  BYD_LIDAR_FRAME_RATIO_FAULT = 61545;
  BYD_LIDAR_FRAME_RATIO_UNNORMAL = 61546;
  BYD_LIDAR_COM_TIMEOUT = 61547;

  /* 62300~62799 SMART_ALARM */
  SMART_USS9_FAULT = 62300;
  SMART_USS9_BLINDNESS = 62301;
  SMART_USS8_FAULT = 62302;
  SMART_USS8_BLINDNESS = 62303;
  SMART_USS7_FAULT = 62304;
  SMART_USS7_BLINDNESS = 62305;
  SMART_USS6_FAULT = 62306;
  SMART_USS6_BLINDNESS = 62307;
  SMART_USS5_FAULT = 62308;
  SMART_USS5_BLINDNESS = 62309;
  SMART_USS4_FAULT = 62310;
  SMART_USS4_BLINDNESS = 62311;
  SMART_USS3_FAULT = 62312;
  SMART_USS3_BLINDNESS = 62313;
  SMART_USS2_FAULT = 62314;
  SMART_USS2_BLINDNESS = 62315;
  SMART_USS12_FAULT = 62316;
  SMART_USS12_BLINDNESS = 62317;
  SMART_USS11_FAULT = 62318;
  SMART_USS11_BLINDNESS = 62319;
  SMART_USS10_FAULT = 62320;
  SMART_USS10_BLINDNESS = 62321;
  SMART_USS1_FAULT = 62322;
  SMART_USS1_BLINDNESS = 62323;
  SMART_USS_TIME_SYNC_FAULT = 62324;
  SMART_SOCB_HEARTBEAT_FAULT = 62325;
  SMART_SOCA_HEARTBEAT_FAULT = 62326;
  SMART_SOC2_TIME_SYNC_FAULT = 62327;
  SMART_SOC2_COMMUNICATION_SOMEIP_TIMEOUT = 62328;
  SMART_SOC1_TIME_SYNC_FAULT = 62329;
  SMART_SOC1_COMMUNICATION_SOMEIP_TIMEOUT = 62330;
  SMART_RADAR_RIGHT_REAR_TIME_SYNC_FAULT = 62331;
  SMART_RADAR_RIGHT_FRONT_TIME_SYNC_FAULT = 62332;
  SMART_RADAR_LEFT_REAR_TIME_SYNC_FAULT = 62333;
  SMART_RADAR_LEFT_FRONT_TIME_SYNC_FAULT = 62334;
  SMART_RADAR_FRONT_TIME_SYNC_FAULT = 62335;
  SMART_RADAR_FRONT_STATUS_SIGNAL_E2E_FAULT = 62336;
  SMART_PARK_DISTANCE_CONTROL_SYSTEM_INTERNAL_FAILURE = 62337;
  SMART_ORINB_SELFTEST_FAULT = 62338;
  SMART_ORINB_POWERSUPPLY_FAULT = 62339;
  SMART_ORINA_SELFTEST_FAULT = 62340;
  SMART_ORINA_POWERSUPPLY_FAULT = 62341;
  SMART_MCU2_RESET_FLAG = 62342;
  SMART_MCU2_HEARTBEAT_FAULT = 62343;
  SMART_MCU1_RESET_FLAG = 62344;
  SMART_MCU1_HEARTBEAT_FAULT = 62345;
  SMART_DES4_CAMERA_EXPOSURE_SIGNAL_FAULT = 62346;
  SMART_DES3_CAMERA_EXPOSURE_SIGNAL_FAULT = 62347;
  SMART_DES2_CAMERA_EXPOSURE_SIGNAL_FAULT = 62348;
  SMART_DES1_CAMERA_EXPOSURE_SIGNAL_FAULT = 62349;
  SMART_CEM_RAINFALL_AMOUNT_FAULT = 62350;
  SMART_CEM_EXTERNAL_LIGHT_STATUS_SIGNAL_E2E_FAULT = 62351;
  SMART_CAMERA_RIGHT_REAR_SN_READ_FRILURE = 62352;
  SMART_CAMERA_RIGHT_REAR_INTERNAL_FAULT = 62353;
  SMART_CAMERA_RIGHT_PANORAMIC_SN_UNMATCHED = 62354;
  SMART_CAMERA_RIGHT_PANORAMIC_SN_READ_FAILURE = 62355;
  SMART_CAMERA_RIGHT_PANORAMIC_INTERNAL_FAULT = 62356;
  SMART_CAMERA_RIGHT_FRONT_SN_READ_FRILURE = 62357;
  SMART_CAMERA_RIGHT_FRONT_INTERNAL_FAULT = 62358;
  SMART_CAMERA_REAR_SN_READ_FRILURE = 62359;
  SMART_CAMERA_REAR_PANORAMIC_SN_UNMATCHED = 62360;
  SMART_CAMERA_REAR_PANORAMIC_SN_READ_FAILURE = 62361;
  SMART_CAMERA_REAR_PANORAMIC_INTERNAL_FAULT = 62362;
  SMART_CAMERA_REAR_INTERNAL_FAULT = 62363;
  SMART_CAMERA_LEFT_REAR_INTERNAL_FAULT = 62364;
  SMART_CAMERA_LEFT_PANORAMIC_SN_UNMATCHED = 62365;
  SMART_CAMERA_LEFT_PANORAMIC_SN_READ_FAILURE = 62366;
  SMART_CAMERA_LEFT_PANORAMIC_INTERNAL_FAULT = 62367;
  SMART_CAMERA_LEFT_FRONT_SN_READ_FRILURE = 62368;
  SMART_CAMERA_LEFT_FRONT_INTERNAL_FAULT = 62369;
  SMART_CAMERA_FRONT_WIDE_SN_READ_FRILURE = 62370;
  SMART_CAMERA_FRONT_WIDE_INTERNAL_FAULT = 62371;
  SMART_CAMERA_FRONT_TIME_SYNC_FAULT = 62372;
  SMART_CAMERA_FRONT_STATUS_SIGNAL_E2E_FAULT = 62373;
  SMART_CAMERA_FRONT_PANORAMIC_SN_UNMATCHED = 62374;
  SMART_CAMERA_FRONT_PANORAMIC_SN_READ_FAILURE = 62375;
  SMART_CAMERA_FRONT_PANORAMIC_INTERNAL_FAULT = 62376;
  SMART_CAMERA_FRONT_NARROW_SN_READ_FRILURE = 62377;
  SMART_CAMERA_FRONT_NARROW_INTERNAL_FAULT = 62378;
  SMART_BCM2_WHEEL_DIRECTION_INVALID_FAULT = 62379;
  SMART_BCM2_VEHCILE_MOTION_STATUS_E2E_FAULT = 62380;
  SMART_BCM2_LONGITUDINAL_CONTROL_STATUS_E2E_FAULT = 62381;
  SMART_BBM_WHEEL_ROTATION_COUNTER_BACKUP_SIGNAL_E2E_FAULT = 62382;
  SMART_BBM_VEHICLE_SPEED_BACKUP_SIGNAL_E2E_FAULT = 62383;
  SMART_BBM_VEHICLE_MOTION_STATUS_BACKUP_SIGNAL_E2E_FAULT = 62384;
  SMART_ARROUND_VIEW_MONITOR_FAULT = 62385;
  SMAR_CAMERA_LEFT_REAR_SN_READ_FRILURE = 62386;
  SAMRT_SOCB_RESET_FLAG = 62387;
  SAMRT_SOCA_RESET_FLAG = 62388;
  SMART_RADAR_FRONT_INTERNAL_FAULT = 62389;
  SMART_RADAR_FRONT_BLIND_FAULT = 62390;
  SMART_RADAR_FRONT_CALIBRATION_FAULT = 62391;
  SMART_RADAR_LEFT_REAR_BLIND_FAULT = 62392;
  SMART_RADAR_LEFT_REAR_UNAVALIABLE_FAULT = 62393;
  SMART_RADAR_LEFT_REAR_INTERNAL_FAULT = 62394;
  SMART_RADAR_LEFT_REAR_CALIBRATION_FAULT = 62395;
  SMART_RADAR_LEFT_REAR_COMMUNICATION_FAULT = 62396;
  SMART_RADAR_RIGHT_REAR_BLIND_FAULT = 62397;
  SMART_RADAR_RIGHT_REAR_UNAVALIABLE_FAULT = 62398;
  SMART_RADAR_RIGHT_REAR_INTERNAL_FAULT = 62399;
  SMART_RADAR_RIGHT_REAR_CALIBRATION_FAULT = 62400;
  SMART_RADAR_RIGHT_REAR_COMMUNICATION_FAULT = 62401;
  SMART_RADAR_RIGHT_FRONT_BLIND_FAULT = 62402;
  SMART_RADAR_RIGHT_FRONT_UNAVALIABLE_FAULT = 62403;
  SMART_RADAR_RIGHT_FRONT_INTERNAL_FAULT = 62404;
  SMART_RADAR_RIGHT_FRONT_CALIBRATION_FAULT = 62405;
  SMART_RADAR_RIGHT_FRONT_COMMUNICATION_FAULT = 62406;
  SMART_RADAR_LEFT_FRONT_BLIND_FAULT = 62407;
  SMART_RADAR_LEFT_FRONT_UNAVALIABLE_FAULT = 62408;
  SMART_RADAR_LEFT_FRONT_INTERNAL_FAULT = 62409;
  SMART_RADAR_LEFT_FRONT_CALIBRATION_FAULT = 62410;
  SMART_RADAR_LEFT_FRONT_COMMUNICATION_FAULT = 62411;
  SMART_MCU1_LP_DEGRADE = 62412;
  SMART_MCU1_NSP_DEGRADE = 62413;
  SMART_MCU1_LCC_DEGRADE = 62414;
  SMART_MCU1_ACC_DEGRADE = 62415;

  /* 62416~62429 SMART_RESERVE */
  SMART_HUT_UPLOAD_TAG = 62416;

  DSV_WIDE_ANGLE_VISION_MODULE_COMMUNICATION_TIMEOUT = 62430;
  DSV_WHEEL_ROTATE_DIRECTION_FAULT = 62431;
  DSV_VIN_NOT_MATCH = 62432;
  DSV_VGM_COMMUNICATION_TIMEOUT = 62433;
  DSV_VGM_COMMUNICATION_LOST = 62434;
  DSV_VGM_COMMUNICAITON_NO_LINK = 62435;
  DSV_VGM_BUS_SIGNAL_IP_INCOMPATIBLE = 62436;
  DSV_VEHICLE_SPEED_FAULT = 62437;
  DSV_VEHICLE_PARAMETER_NOT_INVALID = 62438;
  DSV_VEHICLE_PARAMETER_NOT_CONFIG = 62439;
  DSV_VDDM_DYNAMICS_DOMAIN_COMMUNICATION_TIMEOUT = 62440;
  DSV_TRAILER_MODULE_COMMUNICATION_TIMEOUT = 62441;
  DSV_TCAM_UDP_COMMUNICATION_NOT_RUN = 62442;
  DSV_TCAM_BUS_SIGNAL_IP_INCOMPATIBLE = 62443;
  DSV_SYSTEM_PROGRAMMING_FAILURE = 62444;
  DSV_SRS_CRASH_STATUS_SIGNAL_E2E_FAULT = 62445;
  DSV_SRS_COMMUNICATION_TIMEOUT = 62446;
  DSV_SOCB_VIC_UE = 62447;
  DSV_SOCB_RCE_ERROR = 62448;
  DSV_SOCB_PVA_ERROR = 62449;
  DSV_SOCB_OFA_UE = 62450;
  DSV_SOCB_NVDLA_ERROR = 62451;
  DSV_SOCB_NV_ENCODER_ERROR = 62452;
  DSV_SOCB_MGBE_ERROR = 62453;
  DSV_SOCB_ISP_ERROR = 62454;
  DSV_SOCB_HOST1X_ERROR = 62455;
  DSV_SOCB_GPU_FMON_UE = 62456;
  DSV_SOCB_GPU_ERROR = 62457;
  DSV_SOCB_FSI_DMA_UE = 62458;
  DSV_SOCB_EQOS_MACSEC_UE = 62459;
  DSV_SOCB_EQOS_MAC_UE = 62460;
  DSV_SOCA_VIC_UE = 62461;
  DSV_SOCA_RCE_ERROR = 62462;
  DSV_SOCA_PVA_ERROR = 62463;
  DSV_SOCA_OFA_UE = 62464;
  DSV_SOCA_NVDLA_ERROR = 62465;
  DSV_SOCA_NV_ENCODER_ERROR = 62466;
  DSV_SOCA_MGBE_ERROR = 62467;
  DSV_SOCA_ISP_ERROR = 62468;
  DSV_SOCA_HOST1X_ERROR = 62469;
  DSV_SOCA_GPU_FMON_UE = 62470;
  DSV_SOCA_GPU_ERROR = 62471;
  DSV_SOCA_FSI_DMA_UE = 62472;
  DSV_SOCA_EQOS_MACSEC_UE = 62473;
  DSV_SOCA_EQOS_MAC_UE = 62474;
  DSV_SOC2_UNDER_TEMPERATURE = 62475;
  DSV_SOC2_OVER_TEMPERATURE = 62476;
  DSV_SOC2_LOST_HEARTBEAT_WITH_SOC1 = 62477;
  DSV_SOC2_ETH_INTERFACE_NO_LINK = 62478;
  DSV_SOC1_UNDER_TEMPERATURE = 62479;
  DSV_SOC1_OVER_TEMPERATURE = 62480;
  DSV_SOC1_LOST_HEARTBEAT_WITH_SOC2 = 62481;
  DSV_SOC1_ETH_INTERFACE_NO_LINK = 62482;
  DSV_SEATBELT_LOCK_STATUS_FAULT = 62483;
  DSV_SAS_STEERING_ANGLE_SENSOR_COMMUNICATION_TIMEOUT = 62484;
  DSV_RESTRAINT_MODULE_LEFT_TIMEOUT = 62485;
  DSV_REAR_RIGHT_WHEEL_ROTATE_DIRECTION_FAULT = 62486;
  DSV_REAR_RIGHT_DOOR_MODULE_COMMUNICATION_TIMEOUT = 62487;
  DSV_REAR_LEFT_SEAT_MODULE_TIMEOUT = 62488;
  DSV_REAR_LEFT_DOOR_MODULE_COMMUNICATION_TIMEOUT2 = 62489;
  DSV_REAR_LEFT_DOOR_MODULE_COMMUNICATION_TIMEOUT1 = 62490;
  DSV_RBCM_RUNNING_BOARD_CONGROL_MODULE_COMMUNICAITON_TIMEOUT = 62491;
  DSV_RADAR_RIGHT_REAR_TIMEOUT = 62492;
  DSV_RADAR_RIGHT_FRONT_TIMEOUT = 62493;
  DSV_RADAR_LEFT_REAR_TIMEOUT = 62494;
  DSV_RADAR_LEFT_FRONT_TIMEOUT = 62495;
  DSV_RADAR_FRONT_TIMEOUT = 62496;
  DSV_PSCM_STEER_SYSTEM_STATUS_SIGNAL_E2E_FAULT = 62497;
  DSV_PSCM_STEER_SYSTEM_STATUS_BACKUP_SIGNAL_E2E_FAULT = 62498;
  DSV_PSCM_STEER_PINION_TORQUE_BACKUP_SIGNAL_FAULT = 62499;
  DSV_PSCM_STEER_PINION_STATUS_SIGNAL_E2E_FAULT = 62500;
  DSV_PSCM_STEER_PINION_STATUS_BACKUP_SIGNAL_E2E_FAULT = 62501;
  DSV_PSCM_STEER_PINION_ANGLE_QUALITY_FACTOR_FAULT = 62502;
  DSV_PSCM_PINION_STEER_TORQUE_QUALITY_FACTOR_FAULT = 62503;
  DSV_PSCM_PINION_STEER_ANGLE_QUALITY_FACTOR_FAULT = 62504;
  DSV_PSCM_DRIVER_STEER_STATUS_SIGNAL_E2E_FAULT = 62505;
  DSV_PSCM_COMMUNICATION_TIMEOUT = 62506;
  DSV_PROGRAM_SECURITY_CONSTANT_NOT_WRITTEN = 62507;
  DSV_PROGRAM_PUBLIC_KEY_NOT_WRITTEN = 62508;
  DSV_PCMU_GEAR_LEVEL_FAUTL = 62509;
  DSV_PCMU_ACCELERATOR_PEDAL_SIGNAL_E2E_FAULT = 62510;
  DSV_PASSIVE_SAFETY_CAN_TRANSCEIVER_ERROR = 62511;
  DSV_PASSIVE_SAFETY_CAN_BUSOFF = 62512;
  DSV_PASSENGER_DOOR_MODULE_COMMUNICATION_TIMEOUT2 = 62513;
  DSV_PASSENGER_DOOR_MODULE_COMMUNICATION_TIMEOUT1 = 62514;
  DSV_MCU2_UNDER_TEMPERATURE = 62515;
  DSV_MCU2_OVER_TEMPERATURE = 62516;
  DSV_MCU2_NVM_WRITE_PROTECTION_TRIGGERED = 62517;
  DSV_MCU2_NVM_INTERNAL_FAULT = 62518;
  DSV_MCU2_NVM_DATA_INTEGRITY_FAULT = 62519;
  DSV_MCU2_FLEXRY_TIMEOUT = 62520;
  DSV_MCU2_EXTERNAL_WATCHDOG_RESET = 62521;
  DSV_MCU2_ETH_INTERFACE_NO_LINK = 62522;
  DSV_MCU2_BATTERY_VOLTAGE_MEASURE_FUALT = 62523;
  DSV_MCU1_UNDER_TEMPERATURE = 62524;
  DSV_MCU1_OVER_TEMPERATURE = 62525;
  DSV_MCU1_NVM_WRITE_PROTECTION_TRIGGERED = 62526;
  DSV_MCU1_NVM_INTERNAL_FAULT = 62527;
  DSV_MCU1_NVM_DATA_INTEGRITY_FAULT = 62528;
  DSV_MCU1_FLEXRY_TIMEOUT = 62529;
  DSV_MCU1_EXTERNAL_WATCHDOG_RESET = 62530;
  DSV_MCU1_ETH_INTERFACE_NO_LINK = 62531;
  DSV_MCU1_BATTERY_VOLTAGE_MEASURE_FUALT = 62532;
  DSV_LIDAR_FRONT_LINK_DOWN = 62533;
  DSV_IMU_LONGITUDINAL_ACCELERATION_LOW_QUALITY = 62534;
  DSV_IMU_LATERAL_ACCELERATION_LOW_QUALITY = 62535;
  DSV_IMU_DATA_SIGNAL_E2E_FAULT = 62536;
  DSV_IMU_ANGULAR_RATE_LOW_QUALITY = 62537;
  DSV_IHU_HANGS_ON_DECTECTION_STATUS_SIGNAL_E2E_FAULT = 62538;
  DSV_IHU_COMMUNICATION_TIMEOUT = 62539;
  DSV_HOD_COMMUNICATION_TIMEOUT = 62540;
  DSV_FOG_LIGHT_FAULT = 62541;
  DSV_ETH_100M_PHY1_SQI_LOW_QUALITY = 62542;
  DSV_ETH_1000M_PHY6_SQI_LOW_QUALITY = 62543;
  DSV_ETH_1000M_PHY5_SQI_LOW_QUALITY = 62544;
  DSV_ETH_1000M_PHY4_SQI_LOW_QUALITY = 62545;
  DSV_ETH_1000M_PHY3_SQI_LOW_QUALITY = 62546;
  DSV_ETH_1000M_PHY2_SQI_LOW_QUALITY = 62547;
  DSV_ETH_1000M_PHY1_SQI_LOW_QUALITY = 62548;
  DSV_ECU_QUIESCENT_CURRENT_HIGH_FAULT = 62549;
  DSV_DRIVER_DOOR_MODULE_COMMUNICATION_TIMEOUT2 = 62550;
  DSV_DRIVER_DOOR_MODULE_COMMUNICATION_TIMEOUT1 = 62551;
  DSV_COMMUNICATION_FLEXRAY_NOT_ESTABLISH = 62552;
  DSV_COMMUNICATION_FLEXRAY_ELECTRIC_FAILURE = 62553;
  DSV_CENTRAL_ELECTRONIC_MODULE_COMMUNICATION_TIMEOUT = 62554;
  DSV_CEM_PARK_ASSIST_REQUEST_SIGNAL_E2E_FAULT = 62555;
  DSV_CANFD6_BUSOFF = 62556;
  DSV_CANFD5_BUSOFF = 62557;
  DSV_CANFD4_BUSOFF = 62558;
  DSV_CANFD3_BUSOFF = 62559;
  DSV_CANFD2_BUSOFF = 62560;
  DSV_CANFD11_BUSOFF = 62561;
  DSV_CANFD10_BUSOFF = 62562;
  DSV_CANFD1_BUSOFF = 62563;
  DSV_CAN1_BUSOFF = 62564;
  DSV_CAN_TRANSCEIVER_ERROR = 62565;
  DSV_CAMERA_RIGHT_REAR_STG = 62566;
  DSV_CAMERA_RIGHT_REAR_OPEN_CIRCUIT = 62567;
  DSV_CAMERA_RIGHT_PANORAMIC_STG = 62568;
  DSV_CAMERA_RIGHT_PANORAMIC_OPEN_CIRCUIT = 62569;
  DSV_CAMERA_RIGHT_FRONT_STG = 62570;
  DSV_CAMERA_RIGHT_FRONT_OPEN_CIRCUIT = 62571;
  DSV_CAMERA_REAR_STG = 62572;
  DSV_CAMERA_REAR_PANORAMIC_STG = 62573;
  DSV_CAMERA_REAR_PANORAMIC_OPEN_CIRCUIT = 62574;
  DSV_CAMERA_REAR_OPEN_CIRCUIT = 62575;
  DSV_CAMERA_LEFT_REAR_STG = 62576;
  DSV_CAMERA_LEFT_REAR_OPEN_CIRCUIT = 62577;
  DSV_CAMERA_LEFT_PANORAMIC_STG = 62578;
  DSV_CAMERA_LEFT_PANORAMIC_OPEN_CIRCUIT = 62579;
  DSV_CAMERA_LEFT_FRONT_STG = 62580;
  DSV_CAMERA_LEFT_FRONT_OPEN_CIRCUIT = 62581;
  DSV_CAMERA_FRONT_WIDE_STG = 62582;
  DSV_CAMERA_FRONT_WIDE_OPEN_CIRCUIT = 62583;
  DSV_CAMERA_FRONT_TIMEOUT = 62584;
  DSV_CAMERA_FRONT_PANORAMIC_STG = 62585;
  DSV_CAMERA_FRONT_PANORAMIC_OPEN_CIRCUIT = 62586;
  DSV_CAMERA_FRONT_NO_LINK = 62587;
  DSV_CAMERA_FRONT_NARROW_STG = 62588;
  DSV_CAMERA_FRONT_NARROW_OPEN_CIRCUIT = 62589;
  DSV_BLUETOOTH_SHORT_RANGE_MODULE_COMMUNICATION_TIMEOUT = 62590;
  DSV_BCM2_WHEEL_ROTATION_DIRECTION_SIGNAL_E2E_FAULT = 62591;
  DSV_BCM2_WHEEL_ROTATION_COUNTER_SIGNAL_E2E_FAULT = 62592;
  DSV_BCM2_VEHICLE_WHEEL_SPEED_QUALITY_FACTOR_FAULT = 62593;
  DSV_BCM2_VEHICLE_SPEED_QUALITY_FACTOR_FAULT = 62594;
  DSV_BCM2_VEHICLE_FAST_SPEED_QUALITY_FACTOR_FAULT = 62595;
  DSV_BCM2_RIGHT_REAR_WHEEL_SENSOR_QUALITY_FACTOR_FAULT = 62596;
  DSV_BCM2_RIGHT_FRONT_WHEEL_SENSOR_QUALITY_FACTOR_FAULT = 62597;
  DSV_BCM2_LEFT_REAR_WHEEL_SENSOR_QUALITY_FACTOR_FAULT = 62598;
  DSV_BCM2_LEFT_FRONT_WHEEL_SENSOR_QUALITY_FACTOR_FAULT = 62599;
  DSV_BCM2_ESC_FAULT = 62600;
  DSV_BCM2_EPB_STATUS_FAULT = 62601;
  DSV_BCM2_BRAKE_PEDAL_POSITION_QUALITY_FACTOR_FAULT = 62602;
  DSV_BCM2_BRAKE_CYLINDER_PRESSURE_QUALITY_FACTOR_FAULT = 62603;
  DSV_BCM_COMMUNICATION_TIMEOUT = 62604;
  DSV_BATTERY_PACK_OVER_TEMPERATURE = 62605;
  DSV_ADPU_ETH_INTERFACE_NO_LINK = 62606;
  DSV_ADCU_UNDER_VOLTAGE_MCU2_MEASURE_SUPPLY_B = 62607;
  DSV_ADCU_UNDER_VOLTAGE_MCU2_MEASURE_SUPPLY_A = 62608;
  DSV_ADCU_UNDER_VOLTAGE_MCU1_MEASURE_SUPPLY_B = 62609;
  DSV_ADCU_UNDER_VOLTAGE_MCU1_MEASURE_SUPPLY_A = 62610;
  DSV_ADCU_OVER_VOLTAGE_MCU2_MEASURE_SUPPLY_B = 62611;
  DSV_ADCU_OVER_VOLTAGE_MCU2_MEASURE_SUPPLY_A = 62612;
  DSV_ADCU_OVER_VOLTAGE_MCU1_MEASURE_SUPPLY_B = 62613;
  DSV_ADCU_OVER_VOLTAGE_MCU1_MEASURE_SUPPLY_A = 62614;
  DSV_ADAS_REDUNDANCY_CAN_TRANSCEIVER_ERROR = 62615;
  DSV_ADAS_REDUNDANCY_CAN_BUSOFF = 62616;
  DSV_WAM_PDCM_COMMUNICATION_TIMEOUT = 62617;
  DSV_PSCM_ADAS_LATERAL_CONTROL_STATUS_SIGNAL_E2E_FAULT = 62618;

  /* 62619~62649 SMART_RESERVE */

  /* SMART CANBUS SOME/IP EVENTS */
  SOMEIP_CANBUS_SMART_WHEEL_INFO_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62650;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62651;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62652;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_TOPIC_E2E_CHECK_FAILED = 62653;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_TOPIC_CALLBACK_TIMEOUT = 62654;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_TOPIC_FRAME_TIMEOUT = 62655;
  SOMEIP_CANBUS_SMART_DHU_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62656;
  SOMEIP_CANBUS_SMART_DHU_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62657;
  SOMEIP_CANBUS_SMART_DHU_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62658;
  SOMEIP_CANBUS_SMART_HMI_INFO_FROM_BACKBONE_TOPIC_E2E_CHECK_FAILED = 62659;
  SOMEIP_CANBUS_SMART_HMI_INFO_FROM_BACKBONE_TOPIC_CALLBACK_TIMEOUT = 62660;
  SOMEIP_CANBUS_SMART_HMI_INFO_FROM_BACKBONE_TOPIC_FRAME_TIMEOUT = 62661;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62662;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_OUTPUT_A4SOC_SERVICE_INIT_FIND_SERVICE_TIMEOUT = 62663;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_OUTPUT_A4SOC_SERVICE_INIT_UNAVAILABLE = 62664;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_TOPIC_E2E_CHECK_FAILED = 62665;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_TOPIC_CALLBACK_TIMEOUT = 62666;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_TOPIC_FRAME_TIMEOUT = 62667;
  SOMEIP_CANBUS_SMART_DEEPROUTE_CONTROL_VEHICLE_2ICC_SERVICE_INIT_FAILED = 62668;
  SOMEIP_CANBUS_SMART_VEHICLE_CONTROL_TOPIC_CALLBACK_TIMEOUT = 62669;
  SOMEIP_CANBUS_SMART_VEHICLE_CONTROL_TOPIC_FRAME_TIMEOUT = 62670;

  /* SMART SENSOR_USS SOME/IP EVENTS */
  SOMEIP_USS_SMART_USS_PERCEPTION_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62671;
  SOMEIP_USS_SMART_USS_PERCEPTION_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62672;
  SOMEIP_USS_SMART_USS_PERCEPTION_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62673;
  SOMEIP_USS_SMART_USS_OBJECTS_TOPIC_E2E_CHECK_FAILED = 62674;
  SOMEIP_USS_SMART_USS_OBJECTS_TOPIC_CALLBACK_TIMEOUT = 62675;
  SOMEIP_USS_SMART_USS_OBJECTS_TOPIC_FRAME_TIMEOUT = 62676;
  SOMEIP_USS_SMART_USS_PARKING_SLOTS_E2E_CHECK_FAILED = 62677;
  SOMEIP_USS_SMART_USS_PARKING_SLOTS_CALLBACK_TIMEOUT = 62678;
  SOMEIP_USS_SMART_USS_PARKING_SLOTS_FRAME_TIMEOUT = 62679;
  SOMEIP_USS_SMART_USS_SENSOR_STATUS_TOPIC_E2E_CHECK_FAILED = 62680;
  SOMEIP_USS_SMART_USS_SENSOR_STATUS_TOPIC_CALLBACK_TIMEOUT = 62681;
  SOMEIP_USS_SMART_USS_SENSOR_STATUS_TOPIC_FRAME_TIMEOUT = 62682;
  SOMEIP_USS_SMART_USS_SENSOR_DISTANCE_TOPIC_E2E_CHECK_FAILED = 62683;
  SOMEIP_USS_SMART_USS_SENSOR_DISTANCE_TOPIC_CALLBACK_TIMEOUT = 62684;
  SOMEIP_USS_SMART_USS_SENSOR_DISTANCE_TOPIC_FRAME_TIMEOUT = 62685;

  /* SMART SENSOR_INS_ONLINE SOME/IP EVENTS */
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62686;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62687;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62688;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_IMU_TOPIC_E2E_CHECK_FAILED = 62689;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_IMU_TOPIC_CALLBACK_TIMEOUT = 62690;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_IMU_TOPIC_FRAME_TIMEOUT = 62691;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_GNSS_TOPIC_E2E_CHECK_FAILED = 62692;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_GNSS_TOPIC_CALLBACK_TIMEOUT = 62693;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_GNSS_TOPIC_FRAME_TIMEOUT = 62694;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_INS_TOPIC_E2E_CHECK_FAILED = 62695;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_INS_TOPIC_CALLBACK_TIMEOUT = 62696;
  SOMEIP_INS_SMART_SENSOR_INS_SENSOR_ADPU_INS_TOPIC_FRAME_TIMEOUT = 62697;

  /* SMART ADAS_ONLINE_CALIBRATOR SOME/IP EVENTS */
  SOMEIP_CALIB_SMART_CAMERA_INTRINSIC_SERVICE_INIT_FAILED = 62698;
  SOMEIP_CALIB_SMART_CAMERA_INTRINSIC_SERVICE_FIND_SERVICE_TIMEOUT = 62699;
  SOMEIP_CALIB_SMART_CAMERA_INTRINSIC_SERVICE_UNAVAILABLE = 62700;
  SOMEIP_CALIB_SMART_GET_CAMERA_INTRINSIC_CALLBACK_TIMEOUT = 62701;
  SOMEIP_CALIB_SMART_PARAMETER_SERVICE_INIT_FAILED = 62702;
  SOMEIP_CALIB_SMART_PARAMETER_SERVICE_FIND_SERVICE_TIMEOUT = 62703;
  SOMEIP_CALIB_SMART_PARAMETER_SERVICE_UNAVAILABLE = 62704;
  SOMEIP_CALIB_SMART_GET_PARAMETERS_VALUE_CALLBACK_TIMEOUT = 62705;
  SOMEIP_CALIB_SMART_CALIBRATOR_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62706;
  SOMEIP_CALIB_SMART_CALIBRATOR_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62707;
  SOMEIP_CALIB_SMART_CALIBRATOR_SENSOR_ADPU_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62708;
  SOMEIP_CALIB_SMART_CALIBRATOR_SENSOR_ADPU_IMU_TOPIC_E2E_CHECK_FAILED = 62709;

  /* SMART DIAGNOSTIC_APP SOME/IP EVENTS */
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_A4SOC_SERVICE_INIT_FAILED = 62710;
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62711;
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_A4SOC_SERVICE_UNAVAILABLE = 62712;
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_STATE_TOPIC_E2E_CHECK_FAILED = 62713;
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_STATE_TOPIC_FRAME_TIMEOUT = 62714;
  SOMEIP_DIAG_SMART_VEHICLE_E2E_DEGRADE_STATE_TOPIC_CALLBACK_TIMEOUT = 62715;

  /* SMART BLC SOME/IP EVENTS */
  SOMEIP_BLC_SMART_DEEPROUTE_MODE_VEHICLE_2ICC_SERVICE_INIT_FAILED = 62716;
  SOMEIP_BLC_SMART_DEEPROUTE_MODE_STATUS_TOPIC_CALLBACK_TIMEOUT = 62717;
  SOMEIP_BLC_SMART_DEEPROUTE_MODE_STATUS_TOPIC_FRAME_TIMEOUT = 62718;

  /* SMART DTA SOME/IP EVENTS */
  SOMEIP_DTA_SMART_PARKING_DEEPROUTE_SERVICE_INIT_FAILED = 62719;
  SOMEIP_DTA_SMART_PARKING_LP_MAP_INFO_TOPIC_CALLBACK_TIMEOUT = 62720;
  SOMEIP_DTA_SMART_PARKING_LP_FRAME_DATA_TOPIC_CALLBACK_TIMEOUT = 62721;
  SOMEIP_DTA_SMART_PARKING_LP_FRAME_DATA_TOPIC_FRAME_TIMEOUT = 62722;
  SOMEIP_DTA_SMART_DRIVING_DEEPROUTE_SERVICE_INIT_FAILED = 62723;
  SOMEIP_DTA_SMART_CROSS_BOUNDARY_INFO_TOPIC_CALLBACK_TIMEOUT = 62724;
  SOMEIP_DTA_SMART_CROSS_BOUNDARY_INFO_TOPIC_FRAME_TIMEOUT = 62725;
  SOMEIP_DTA_SMART_OBSTACLE_INFO_TOPIC_CALLBACK_TIMEOUT = 62726;
  SOMEIP_DTA_SMART_OBSTACLE_INFO_TOPIC_FRAME_TIMEOUT = 62727;
  SOMEIP_DTA_SMART_INS_DATA_DEEPROUTE_SERVICE_INIT_FAILED = 62728;
  SOMEIP_DTA_SMART_INS_DATA_TOPIC_CALLBACK_TIMEOUT = 62729;
  SOMEIP_DTA_SMART_INS_DATA_TOPIC_FRAME_TIMEOUT = 62730;
  SOMEIP_DTA_SMART_SOFT_SWITCH_STATE_DEEPROUTE_SERVICE_INIT_FAILED = 62731;
  SOMEIP_DTA_SMART_SMART_BUSINESS_DATA_TOPIC_CALLBACK_TIMEOUT = 62732;
  SOMEIP_DTA_SMART_SMART_BUSINESS_DATA_TOPIC_FRAME_TIMEOUT = 62733;
  SOMEIP_DTA_SMART_NAVI_PATH_DEEPROUTE_SERVICE_INIT_FAILED = 62734;
  SOMEIP_DTA_SMART_NAVI_DATA_METHOD_CALLBACK_TIMEOUT = 62735;
  SOMEIP_DTA_SMART_REAL_TIME_DATA_METHOD_CALLBACK_TIMEOUT = 62736;
  SOMEIP_DTA_SMART_USER_OPERATION_METHOD_CALLBACK_TIMEOUT = 62737;
  SOMEIP_DTA_SMART_DHU_COMMAND_SMART_SERVICE_INIT_FAILED = 62738;
  SOMEIP_DTA_SMART_DHU_COMMAND_SMART_SERVICE_FIND_SERVICE_TIMEOUT = 62739;
  SOMEIP_DTA_SMART_DHU_COMMAND_SMART_SERVICE_UNAVAILABLE = 62740;
  SOMEIP_DTA_SMART_SMART_DHU_COMMAND_TOPIC_CALLBACK_TIMEOUT = 62741;
  SOMEIP_DTA_SMART_SMART_DHU_COMMAND_TOPIC_FRAME_TIMEOUT = 62742;
  SOMEIP_DTA_SMART_BUSINESS_STATE_SMART_SERVICE_INIT_FAILED = 62743;
  SOMEIP_DTA_SMART_BUSINESS_STATE_SMART_SERVICE_FIND_SERVICE_TIMEOUT = 62744;
  SOMEIP_DTA_SMART_BUSINESS_STATE_SMART_SERVICE_UNAVAILABLE = 62745;
  SOMEIP_DTA_SMART_HMI_BUSINESS_STATES_TOPIC_E2E_CHECK_FAILED = 62746;
  SOMEIP_DTA_SMART_HMI_BUSINESS_STATES_TOPIC_CALLBACK_TIMEOUT = 62747;

  /* SMART CALIB DATA INTEGRITY_CHECK */
  CALIBRATION_IMU_DATA_INTEGRITY_CHECK_FAILED = 62748;
  CALIBRATION_CAMERA_FRONT_PANORAMIC_DATA_INTEGRITY_CHECK_FAILED = 62749;
  CALIBRATION_CAMERA_LEFT_PANORAMIC_DATA_INTEGRITY_CHECK_FAILED = 62750;
  CALIBRATION_CAMERA_RIGHT_PANORAMIC_DATA_INTEGRITY_CHECK_FAILED = 62751;
  CALIBRATION_CAMERA_REAR_PANORAMIC_DATA_INTEGRITY_CHECK_FAILED = 62752;

  /* SMART DTA SOME/IP EVENTS */
  SOMEIP_DTA_SMART_HMI_BUSINESS_STATES_TOPIC_FRAME_TIMEOUT = 62753;

  /* SMART CANBUS SOME/IP EVENTS */
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_A4SOC_SERVICE_INIT_FAILED = 62754;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62755;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_TOPIC_E2E_CHECK_FAILED = 62756;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_TOPIC_CALLBACK_TIMEOUT = 62757;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_TOPIC_FRAME_TIMEOUT = 62758;

  /* SMART BLC SOME/IP EVENTS */
  SOMEIP_BLC_SMART_SMART_CAMERA_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62759;
  SOMEIP_BLC_SMART_SMART_CAMERA_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62760;
  SOMEIP_BLC_SMART_SENSOR_FLC20_SIGNALS_TOPIC_E2E_CHECK_FAILED = 62761;
  SOMEIP_BLC_SMART_SENSOR_FLC20_SIGNALS_TOPIC_CALLBACK_TIMEOUT = 62762;
  SOMEIP_BLC_SMART_SENSOR_FLC20_SIGNALS_TOPIC_FRAME_TIMEOUT = 62763;

  /* SMART additional SOME/IP events */
  SOMEIP_BLC_SMART_SMART_CAMERA_DATA_OUTPUT_A4SOC_SERVICE_UNAVAILABLE = 62764;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_A4SOC_SERVICE_UNAVAILABLE = 62765;
  SOMEIP_UPLOADER_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_INIT_FAILED = 62766;
  SOMEIP_UPLOADER_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_FIND_SERVICE_TIMEOUT = 62767;
  SOMEIP_UPLOADER_SMART_HMI_SOFT_BUTTON_SETTING_INFO_SERVICE_INIT_FAILED = 62768;
  SOMEIP_UPLOADER_SMART_HMI_SOFT_BUTTON_SETTING_INFO_SERVICE_FIND_SERVICE_TIMEOUT = 62769;
  SOMEIP_UPLOADER_SMART_HMI_SOFT_BUTTON_SETTING_INFO_E2E_CHECK_FAILED = 62770;
  SOMEIP_UPLOADER_SMART_HMI_SOFT_BUTTON_SETTING_INFO_FRAME_TIMEOUT = 62771;
  SOMEIP_UPLOADER_SMART_HMI_SOFT_BUTTON_SETTING_INFO_CALLBACK_TIMEOUT = 62772;
  SOMEIP_MAP_ENGINE_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_INIT_FAILED = 62773;
  SOMEIP_MAP_ENGINE_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_FIND_SERVICE_TIMEOUT = 62774;
  SOMEIP_SD_ROUTING_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_INIT_FAILED = 62775;
  SOMEIP_SD_ROUTING_SMART_CERT_PRIV_KEY_TRANSMIT_SERVICE_FIND_SERVICE_TIMEOUT = 62776;
  SOMEIP_USS_ECHO_DISTANCE_TOPIC_E2E_CHECK_FAILED = 62777;
  SOMEIP_USS_ECHO_DISTANCE_TOPIC_CALLBACK_TIMEOUT = 62778;
  SOMEIP_USS_ECHO_DISTANCE_TOPIC_FRAME_TIMEOUT = 62779;
  SOMEIP_USS_STATUS_DATA_OUTPUT_A4SOC_SERVICE_INIT_FAILED = 62780;
  SOMEIP_USS_STATUS_DATA_OUTPUT_A4SOC_SERVICE_FIND_SERVICE_TIMEOUT = 62781;

  /* SMART SOME/IP EVENT LOSE EVENTS */
  SOMEIP_INS_SMART_SENSOR_ADPU_IMU_TOPIC_LOSE_TIMEOUT = 62782;
  SOMEIP_INS_SMART_SENSOR_ADPU_GNSS_TOPIC_LOSE_TIMEOUT = 62783;
  SOMEIP_INS_SMART_SENSOR_ADPU_INS_TOPIC_LOSE_TIMEOUT = 62784;
  SOMEIP_USS_SMART_USS_OBJECTS_TOPIC_LOSE_TIMEOUT = 62785;
  SOMEIP_USS_SMART_USS_PARKING_SLOTS_TOPIC_LOSE_TIMEOUT = 62786;
  SOMEIP_USS_SMART_USS_ECHO_DISTANCE_TOPIC_LOSE_TIMEOUT = 62787;
  SOMEIP_CANBUS_SMART_WHEEL_INFO_TOPIC_LOSE_TIMEOUT = 62788;
  SOMEIP_CANBUS_SMART_VEHICLE_DATA_TOPIC_LOSE_TIMEOUT = 62789;
  SOMEIP_CANBUS_SMART_FUNCTION_ARBITRATION_STATE_TOPIC_LOSE_TIMEOUT = 62790;

  /* 62800~62899 CAMERA QQUALITY */
  PERCEPTION_BLIND_CAMERA_FRONT_PANORAMIC = 62800;
  PERCEPTION_BLIND_CAMERA_REAR_PANORAMIC = 62801;
  PERCEPTION_BLIND_CAMERA_LEFT_PANORAMIC = 62802;
  PERCEPTION_BLIND_CAMERA_RIGHT_PANORAMIC = 62803;
  PERCEPTION_BLIND_CAMERA_REAR = 62804;
  PERCEPTION_BLIND_CAMERA_LEFT_FRONT = 62805;
  PERCEPTION_BLIND_CAMERA_LEFT_REAR = 62806;
  PERCEPTION_BLIND_CAMERA_RIGHT_FRONT = 62807;
  PERCEPTION_BLIND_CAMERA_RIGHT_RAER = 62808;
  PERCEPTION_BLIND_CAMERA_FRONT_WIDE = 62809;
  PERCEPTION_BLIND_CAMERA_FRONT_NARROW = 62810;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_FRONT_PANORAMIC = 62811;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_REAR_PANORAMIC = 62812;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_LEFT_PANORAMIC = 62813;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_RIGHT_PANORAMIC = 62814;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_REAR = 62815;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_LEFT_FRONT = 62816;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_LEFT_REAR = 62817;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_RIGHT_FRONT = 62818;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_RIGHT_RAER = 62819;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_FRONT_WIDE = 62820;
  PERCEPTION_INPUT_LOW_QUALITY_CAMERA_FRONT_NARROW = 62821;

  /* 62900~63199 MODULE CRASH EVENT */
  MODULE_DRPKI_CRASH_EVENT = 62900;
  MODULE_DSM_CRASH_EVENT = 62901;
  MODULE_MP_SENTRY_CRASH_EVENT = 62902;
  MODULE_ONBOARD_MAPS_CRASH_EVENT = 62903;
  MODULE_ADAS_CALIB_MONITOR_CRASH_EVENT = 62904;
  MODULE_ADAS_INS_CALIBRATOR_CRASH_EVENT = 62905;
  MODULE_ADAS_ONLINE_CALIBRATOR_CRASH_EVENT = 62906;
  MODULE_ADPT_CANBUS_CRASH_EVENT = 62907;
  MODULE_ADPT_DOMAINV2_CRASH_EVENT = 62908;
  MODULE_ADPT_DOMAINV3_CRASH_EVENT = 62909;
  MODULE_ADPT_DOWNSTREAM_CRASH_EVENT = 62910;
  MODULE_ADPT_SENSOR_INS_CRASH_EVENT = 62911;
  MODULE_ADPT_UPSTREAM_CRASH_EVENT = 62912;
  MODULE_AEB_CRASH_EVENT = 62913;
  MODULE_AROUND_VIEW_MONITOR_CRASH_EVENT = 62914;
  MODULE_COMPLIANCER_CRASH_EVENT = 62915;
  MODULE_DATA_AGENT_CRASH_EVENT = 62916;
  MODULE_DIAGNOSTIC_APP_CRASH_EVENT = 62917;
  MODULE_DTA_CRASH_EVENT = 62918;
  MODULE_DTS_CRASH_EVENT = 62919;
  MODULE_GPS_LOCALIZATION_CRASH_EVENT = 62920;
  MODULE_LOCALIZATION_MATCHING_CRASH_EVENT = 62921;
  MODULE_LIDAR_MANAGER_CRASH_EVENT = 62922;
  MODULE_ONLINE_RECORDER_LIGHT_CRASH_EVENT = 62923;
  MODULE_ONLINE_RECORDER_MEDIUM_CRASH_EVENT = 62924;
  MODULE_ONLINE_RECORDER_HEAVY_CRASH_EVENT = 62925;
  MODULE_OSBRIDGE_CRASH_EVENT = 62926;
  MODULE_PBOX_MANAGER_CRASH_EVENT = 62927;
  MODULE_PERF_COLLECTOR_CRASH_EVENT = 62928;
  MODULE_RTK_POSE_FORWARD_CRASH_EVENT = 62929;
  MODULE_SENSOR_CAMERA_PANORAMIC_CRASH_EVENT = 62930;
  MODULE_SENSOR_LIDAR_CRASH_EVENT = 62931;
  MODULE_SENTRY_CRASH_EVENT = 62932;
  MODULE_TOB_SAFETY_APP_CRASH_EVENT = 62933;
  MODULE_UPLOADER_CRASH_EVENT = 62934;
  MODULE_PLANNING_CRASH_EVENT = 62935;
  MODULE_PERCEP_ADAS_CRASH_EVENT = 62936;
  MODULE_BLC_CRASH_EVENT = 62937;
  MODULE_MAP_ENGINE_CRASH_EVENT = 62938;
  MODULE_SD_ROUTING_CRASH_EVENT = 62939;
  MODULE_SENSOR_INS_ONLINE_CRASH_EVENT = 62940;
  MODULE_CANBUS_CRASH_EVENT = 62941;
  MODULE_CONTROL_CRASH_EVENT = 62942;
  MODULE_PERCEPTION_CRASH_EVENT = 62943;
  MODULE_MPS_SERVER_CRASH_EVENT = 62944;
  MODULE_SENSOR_CAMERA_CRASH_EVENT = 62945;
  MODULE_ROUDI_CRASH_EVENT = 62946;
  MODULE_SENSOR_USS_CRASH_EVENT = 62947;
  MODULE_SENSOR_RADAR_CRASH_EVENT = 62948;
  MODULE_PCAP_CAPTURE_CRASH_EVENT = 62949;
  MODULE_GRADING_ONBOARD_CRASH_EVENT = 62950;
  MODULE_BLACKBOX_CRASH_EVENT = 62951;
  MODULE_DEVICE_MANAGER_CRASH_EVENT = 62952;
  MODULE_ROUTINGD_V88_CRASH_EVENT = 62953;
  MODULE_ROUTINGD_V168_CRASH_EVENT = 62954;
  MODULE_BRIDGE_BLC_CRASH_EVENT = 62955;
  MODULE_BRIDGE_SAFETY_CRASH_EVENT = 62956;
  MODULE_MCU_LOG_ADAPTER_CRASH_EVENT = 62957;

  /* 63200~63399 CLOUD EVENT */
  CLOUD_CONFIG_ERROR_EVENT = 63200;  // info proto definition: DataConfigErrorEventInfo from report_event.proto

  /* 63400~63499 PERCEPTION SCENE RECOGNITION EVENT */
  PERCEPTION_SCENE_WEATHER_SNOWY_EVENT = 63400;
  PERCEPTION_SCENE_WEATHER_RAINY_EVENT = 63401;
  PERCEPTION_SCENE_WEATHER_HEAVY_RAINY_EVENT = 63402;
  PERCEPTION_SCENE_WEATHER_FOGGY_EVENT = 63403;
  PERCEPTION_SCENE_WEATHER_SAND_STORM_EVENT = 63404;
  PERCEPTION_SCENE_WEATHER_CLOUDY_EVENT = 63405;

  /* 63500~63599 GRADING ONBOARD EVENT */
  GRADING_ONBOARD_PASS_JUNCTION_EVENT = 63500;
  GRADING_ONBOARD_LANE_CHANGE_EVENT = 63501;
  GRADING_ONBOARD_INBOUND_RAMP_EVENT = 63502;
  GRADING_ONBOARD_OUTBOUND_RAMP_EVENT = 63503;
  GRADING_ONBOARD_DETOUR_EVENT = 63504;
  GRADING_ONBOARD_YIELD_VRU_EVENT = 63505;
  GRADING_ONBOARD_CUT_IN_EVENT = 63506;
  GRADING_ONBOARD_COLLISION_RISK_EVENT = 63507;

  /* 63600~63699 DTA ONBOARD EVENT */
  DTA_ONBOARD_ONCE_TRIP_EVENT = 63600;

  /* 63700~89999 RESERVE */

  /* 90000~99999 COMMON */
  LEVEL_1_ABNORMALITY_FUNCTION_SOFTWARE = 90001;
  LEVEL_2_ABNORMALITY_FUNCTION_SOFTWARE = 90002;
  LEVEL_3_ABNORMALITY_FUNCTION_SOFTWARE = 90003;

  EVENT_MAX_ID = 100000;
}

message EventInfo {
  optional Module module = 1;
  optional Event event = 2;
  optional uint64 timestamp = 3;  // timestamp for nano-second
  optional int32 sequence_num = 4;
  oneof info {
    string json_info = 5;
    bytes serialized_proto_info = 6;
  }
  optional uint32 delay_time = 7;
}

// topic: "/common/module_events"
message ModuleEvents {
  //@C_Mark@50@;
  repeated EventInfo event_infos = 1;
}
