syntax = "proto2";

package deeproute.localization;

import "common/geometry.proto";
import "drivers/gnss/ins.proto";

message LockOnRoadResult {
  optional int64 time_us = 1;                      // time of pose
  optional string sd_link_id = 2;                  // corresponding link id of returned SD map
  optional uint64 dr_link_id = 15;                 // corresponding link id of returned DR map
  optional int32 sd_link_index = 9;               // link index ranging from [0, link_size]
  optional double sd_distance_to_link_start = 3;   // geo referenced point distance along the link from link start
  optional double sd_distance_to_link_end = 4;     // geo referenced point distance along the link to link end

  enum Status{
    NO_ROUTING = 0;                                // no routing response has received
    NORMAL = 1;                                    // matching succeeded
    YAWED = 2;                                     // if trajectory yawed from planned path.
    PASSOVER = 3;                                  // when vehicle takes a short path, skipping many links
    GNSS_POSE_INVALID = 4;                         // when gnss pose is invalid, such as all zeros
  }

  optional Status status = 5;                      // whether or not the vehicle is deviated from the previous planned route
  optional string request_id = 6;                  // acquired from routing response
  optional common.Point3D matched_position_wgs84 = 7;      // matched position in wgs84, note this field will be deprecated in the future.
  optional common.Point3D matched_position_gcj02 = 12;     // matched position in gcj02
  optional double matched_heading = 8;             // matched point heading

  message ShadowMode {
    enum Status {
      MATCH = 0;
      MISMATCH = 1;
    }
    optional Status status = 1;
    optional string lock_on_road_link_id = 2;
    optional uint64 lock_on_road_dr_link_id = 4;
    optional string ddmm_link_id = 3;
    optional uint64 ddmm_dr_link_id = 5;
  }

  optional ShadowMode shadow_mode_result = 10;

  optional deeproute.drivers.gnss.SensorsIns.Type gnss_type = 11;         // gnss type

  message LinkProperties{
    optional uint32 priority = 1;                  // 路段等级
    optional bool tunnel = 2;                      // 是否在隧道
    optional bool elevated = 3;                    // 是否为高架
    optional string sdmap_version = 4;
    optional string sdmap_id = 5;
    optional uint64 dr_link_id = 6;

    enum SdMapSourceType {
      TENCENT = 0;
      BAIDU = 1;
      TENCENT_PLUS = 2;
    }
    optional SdMapSourceType sdmap_source_type = 7;
  }
  optional LinkProperties link_properties = 13;

  optional uint32 route_seg_idx = 14;              // segment idx of a route

  message NaviStatus{
    optional Status status = 1;
    
    enum Source{
      LOCK_ON_ROAD = 0;
      LOCAL_ROUTING = 1;
    }
    
    optional Source source = 2;
  }

  repeated NaviStatus navi_status_internal = 16;

  message SdLocalizationStatus {
    enum ConfidenceStatus {
      LOW = 0;
      HIGH = 1;
    }

    message Confidence {
      optional double confidence_value = 1;
      optional ConfidenceStatus confidence_status = 2;
    }

    optional ConfidenceStatus confidence = 1;
    optional Confidence lateral_position_confidence = 2;
    optional Confidence longitudinal_position_confidence = 3;
  }

  optional SdLocalizationStatus sd_localization_status = 17;

  enum YawSceneType {
    UNKNOWN = 0;                                  // 偏航, 但是场景未知
    ROUTING_MAIN_ROAD_VEHICLE_SECONDARY_ROAD = 1; // 导航为主路，自车在辅路
    ROUTING_SECONDARY_ROAD_VEHICLE_MAIN_ROAD = 2; // 导航为辅路，自车在主路
  }
  optional YawSceneType yaw_scene_type = 18;
}

message LaneIndexEstimationResult{
  optional int64 ras_map_time = 1;                 // time of ras map measurement
  optional int32 sd_lane_index = 2;                // estimation result, from left to right, of same direction
  optional int32 sd_lane_sum = 3;                  // number of same direction lanes, directly read from routing response
  
  enum Type{
    INVALID = 0;                                   // no observations are given
    INITIALIZING = 1;                              // waiting for enough obervations
    UNRELIABLE = 2;                                // when observations are not reliable, eg jittered perception etc.
    GOOD = 3;                                      // safe to use, lane estimtion has fully locked
  }
  optional Type type = 4;

  optional int32 ego_lane_id = 5;                  // infered from ras map

  enum ClosestCurb{
    LEFT = 0;
    RIGHT = 1;
    NO_CURB = 2;
  }

  optional ClosestCurb closest_curb = 6;           // closest curb of current ras map
}
