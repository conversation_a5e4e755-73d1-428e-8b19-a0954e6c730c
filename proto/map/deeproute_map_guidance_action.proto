syntax = "proto2";

package deeproute.map;

import "map/common_type.proto";
import "map/sd_map.proto";

message GuidanceAction {
  optional deeproute.map.CrossingType junction_type = 1;
  optional double distance_to_junction = 2;
  optional int32 junction_bm = 3;
  optional string junction_link_id = 4;
  optional int32 lane_num = 5;
  repeated deeproute.sd_map.LinkData.FormWay link_formway = 6;
  optional deeproute.map.LaneTurn turn_type = 7;
  repeated int32 pass_lane_id = 8;
  optional int32 junction_link_index = 9;
  optional bool has_unpassable_lane = 10;
}

message GuidanceActionList {
  repeated GuidanceAction guidance_action = 1;
}