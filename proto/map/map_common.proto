syntax = "proto2";

package deeproute.map;

import "map/common_type.proto";
import "map/sd_map.proto";
import "lock_on_road/lock_on_road.proto";

/**
 * 前背景车道
 */
message LaneActions {
  optional double distance_to_lane_actions = 1;  // 到前背景车道的距离
  repeated deeproute.map.LaneAction foreground_lanes = 2;      // 前景车道
  repeated deeproute.map.LaneAction background_lanes = 3;      // 背景车道
}

/**
 * 车道转向动作类型(内部维护，区分高德数据)
 */
enum LaneAction {
  LANE_ACTION_AHEAD = 0;                  // 直行
  LANE_ACTION_LEFT = 1;                   // 左转
  LANE_ACTION_AHEAD_LEFT = 2;             // 直行｜左转
  LANE_ACTION_RIGHT = 3;                  // 右转
  LANE_ACTION_AHEAD_RIGHT = 4;            // 直行｜右转
  LANE_ACTION_LU_TURN = 5;                // 左掉头
  LANE_ACTION_LEFT_RIGHT = 6;             // 左转｜右转
  LANE_ACTION_AHEAD_LEFT_RIGHT = 7;       // 直行｜左转｜右转
  LANE_ACTION_RU_TURN = 8;                // 右掉头
  LANE_ACTION_AHEAD_LU_TURN = 9;          // 直行｜左掉头
  LANE_ACTION_AHEAD_RU_TURN = 10;         // 直行｜右掉头
  LANE_ACTION_LEFT_LU_TURN = 11;          // 左转｜左掉头
  LANE_ACTION_RIGHT_RU_TURN = 12;         // 右转｜右掉头
  LANE_ACTION_LEFT_IN_AHEAD = 13;         // 直行｜拓展
  LANE_ACTION_LEFT_IN_LEFT_LU_TURN = 14;  // 左转｜左掉头｜拓展
  LANE_ACTION_RESERVED = 15;              // 保留
  LANE_ACTION_AHEAD_LEFT_LU_TURN = 16;    // 直行｜左转｜左掉头
  LANE_ACTION_RIGHT_RU_TURN_EX = 17;      // 右转｜左掉头
  LANE_ACTION_LEFT_RU_TURN = 18;          // 左转｜左掉头
  LANE_ACTION_AHEAD_RIGHT_RU_TURN = 19;   // 直行｜右转｜右掉头
  LANE_ACTION_LEFT_LU_TURN_EX = 20;       // 左转｜右掉头
  LANE_ACTION_BUS = 21;                   // 公交车道
  LANE_ACTION_EMPTY = 22;                 // 空车道
  LANE_ACTION_VARIABLE = 23;              // 可变车道
  LANE_ACTION_DEDICATED = 24;             // 专用车道
  LANE_ACTION_TIDAL = 25;                 // 潮汐车道
  LANE_ACTION_NONE = 255;                 // 无对应车道
}

/**
 * 基本导航动作
 */
enum MainAction {
  MAIN_ACTION_NULL = 0;             // 无基本导航动作
  MAIN_ACTION_TURN_LEFT = 1;        // 左转
  MAIN_ACTION_TURN_RIGHT = 2;       // 右转
  MAIN_ACTION_SLIGHT_LEFT = 3;      // 向左前方行驶
  MAIN_ACTION_SLIGHT_RIGHT = 4;     // 向右前方行驶
  MAIN_ACTION_TURN_HARD_LEFT = 5;   // 向左后方行驶
  MAIN_ACTION_TURN_HARD_RIGHT = 6;  // 向右后方行驶
  MAIN_ACTION_UTURN = 7;            // 左转调头
  MAIN_ACTION_CONTINUE = 8;         // 直行
  MAIN_ACTION_MERGE_LEFT = 9;       // 靠左
  MAIN_ACTION_MERGE_RIGHT = 10;     // 靠右
  MAIN_ACTION_ENTRY_RING = 11;      // 进入环岛
  MAIN_ACTION_LEAVE_RING = 12;      // 离开环岛
  MAIN_ACTION_SLOW = 13;            // 减速行驶
  MAIN_ACTION_PLUG_CONTINUE = 14;   // 插入直行（泛亚特有）
  MAIN_ACTION_ENTER_BUILDING = 65;  // 进入建筑物
  MAIN_ACTION_LEAVE_BUILDING = 66;  // 离开建筑物
  MAIN_ACTION_BY_ELEVATOR = 67;     // 电梯换层
  MAIN_ACTION_BY_STAIR = 68;        // 楼梯换层
  MAIN_ACTION_BY_ESCALATOR = 69;    // 扶梯换层
  MAIN_ACTION_COUNT = 70;  // 导航主动作最大个数 无特殊意义
}

/**********************FusionMap**********************/

/**
 * 辅助导航动作
 */
enum AssistantAction {
  ASSI_ACTION_NULL = 0;                      // 无辅助导航动作
  ASSI_ACTION_ENTRY_MAIN = 1;                // 进入主路
  ASSI_ACTION_ENTRY_SIDE_ROAD = 2;           // 进入辅路
  ASSI_ACTION_ENTRY_FREEWAY = 3;             // 进入高速
  ASSI_ACTION_ENTRY_SLIP = 4;                // 进入匝道
  ASSI_ACTION_ENTRY_TUNNEL = 5;              // 进入隧道
  ASSI_ACTION_ENTRY_CENTER_BRANCH = 6;       // 进入中间岔道
  ASSI_ACTION_ENTRY_RIGHT_BRANCH = 7;        // 进入右岔路
  ASSI_ACTION_ENTRY_LEFT_BRANCH = 8;         // 进入左岔路
  ASSI_ACTION_ENTRY_RIGHT_ROAD = 9;          // 进入右转专用道
  ASSI_ACTION_ENTRY_LEFT_ROAD = 10;          // 进入左转专用道
  ASSI_ACTION_ENTRY_MERGE_CENTER = 11;       // 进入中间道路
  ASSI_ACTION_ENTRY_MERGE_RIGHT = 12;        // 进入右侧道路
  ASSI_ACTION_ENTRY_MERGE_LEFT = 13;         // 进入左侧道路
  ASSI_ACTION_ENTRY_MERGE_RIGHT_SIDE = 14;   // 靠右行驶进入辅路
  ASSI_ACTION_ENTRY_MERGE_LEFT_SIDE = 15;    // 靠左行驶进入辅路
  ASSI_ACTION_ENTRY_MERGE_RIGHT_MAIN = 16;   // 靠右行驶进入主路
  ASSI_ACTION_ENTRY_MERGE_LEFT_MAIN = 17;    // 靠左行驶进入主路
  ASSI_ACTION_ENTRY_MERGE_RIGHT_RIGHT = 18;  // 靠右行驶进入右转专用道
  ASSI_ACTION_ENTRY_FERRY = 19;              // 到达航道
  ASSI_ACTION_LEFT_FERRY = 20;               // 驶离轮渡
  ASSI_ACTION_ALONG_ROAD = 23;               // 沿当前道路行驶
  ASSI_ACTION_ALONG_SIDE = 24;               // 沿辅路行驶
  ASSI_ACTION_ALONG_MAIN = 25;               // 沿主路行驶
  ASSI_ACTION_ARRIVE_EXIT = 32;              // 到达出口
  ASSI_ACTION_ARRIVE_SERVICE_AREA = 33;      // 到达服务区
  ASSI_ACTION_ARRIVE_TOLL_GATE = 34;         // 到达收费站
  ASSI_ACTION_ARRIVE_WAY = 35;               // 到达途经地
  ASSI_ACTION_ARRIVE_DESTINATION = 36;       // 到达目的地的
  ASSI_ACTION_ARRIVE_CHARGING_STATION = 37;  // 到达充电站,新能源汽车专用
  ASSI_ACTION_ENTRY_RING_LEFT_ = 48;         // 绕环岛左转
  ASSI_ACTION_ENTRY_RING_RIGHT = 49;         // 绕环岛右转
  ASSI_ACTION_ENTRY_RING_CONTINUE = 50;   // 绕环岛直行
  ASSI_ACTION_ENTRY_RING_UTURN = 51;      // 绕环岛右转
  ASSI_ACTION_SMALL_RING_NOT_COUNT = 52;  // 小环岛不数出口
  ASSI_ACTION_RIGHT_BRANCH_1 = 64;  // 到达复杂路口，走右边第一出口
  ASSI_ACTION_RIGHT_BRANCH_2 = 65;  // 到达复杂路口，走右边第二出口
  ASSI_ACTION_RIGHT_BRANCH_3 = 66;  // 到达复杂路口，走右边第三出口
  ASSI_ACTION_RIGHT_BRANCH_4 = 67;  // 到达复杂路口，走右边第四出口
  ASSI_ACTION_RIGHT_BRANCH_5 = 68;  // 到达复杂路口，走右边第五出口
  ASSI_ACTION_LEFT_BRANCH_1 = 69;  // 到达复杂路口，走左边第一出口
  ASSI_ACTION_LEFT_BRANCH_2 = 70;  // 到达复杂路口，走左边第二出口
  ASSI_ACTION_LEFT_BRANCH_3 = 71;  // 到达复杂路口，走左边第三出口
  ASSI_ACTION_LEFT_BRANCH_4 = 72;  // 到达复杂路口，走左边第四出口
  ASSI_ACTION_LEFT_BRANCH_5 = 73;  // 到达复杂路口，走左边第五出口
  ASSI_ACTION_ENTER_ULINE = 80;    // 进入调头专用路
  ASSI_ACTION_PASS_CROSSWALK = 90;           // 通过人行横道
  ASSI_ACTION_PASS_OVERPASS = 91;            // 通过过街天桥
  ASSI_ACTION_PASS_UNDERGROUND = 92;         // 通过地下通道
  ASSI_ACTION_PASS_SQUARE = 93;              // 通过广场
  ASSI_ACTION_PASS_PARK = 94;                // 通过公园
  ASSI_ACTION_PASS_STAIRCASE = 95;           // 通过扶梯
  ASSI_ACTION_PASS_LIFT = 96;                // 通过直梯
  ASSI_ACTION_PASS_CABLEWAY = 97;            // 通过索道
  ASSI_ACTION_PASS_SKY_CHANNEL = 98;         // 通过空中通道
  ASSI_ACTION_PASS_CHANNEL = 99;             // 通过建筑物穿越通道
  ASSI_ACTION_PASS_WALKROAD = 100;           // 通过行人道路
  ASSI_ACTION_PASS_BOAT_LINE = 101;          // 通过游船路线
  ASSI_ACTION_PASS_SIGHT_SEEING_LINE = 102;  // 通过观光车路线
  ASSI_ACTION_PASS_SKIDWAY = 103;            // 通过滑道
  ASSI_ACTION_PASS_LADDER = 105;             // 通过阶梯
  ASSI_ACTION_PASS_SLOPE = 106;              // 通过斜坡
  ASSI_ACTION_PASS_BRIDGE = 107;             // 通过桥
  ASSI_ACTION_PASS_FERRY = 108;              // 通过轮渡
  ASSI_ACTION_PASS_SUBWAY = 109;             // 通过地铁通道
  ASSI_ACTION_SOON_ENTER_BUILDING = 112;  // 即将进入建筑(当前未下发)
  ASSI_ACTION_SOON_LEAVE_BUILDING = 113;  // 即将离开建筑(当前未下发)
  ASSI_ACTION_ENTER_ROUNDABOUT = 114;     // 进入环岛(骑步特有)
  ASSI_ACTION_LEAVE_ROUNDABOUT = 115;     // 离开环岛(骑步特有)
  ASSI_ACTION_ENTER_PATH = 116;           // 进入小路
  ASSI_ACTION_ENTER_INNER = 117;          // 进入内部路
  ASSI_ACTION_ENTER_LEFT_BRANCH_TWO = 118;         // 进入左侧第二岔路
  ASSI_ACTION_ENTER_LEFT_BRANCH_THREE = 119;       // 进入左侧第三岔路
  ASSI_ACTION_ENTER_RIGHT_BRANCH_TWO = 120;        // 进入右侧第二岔路
  ASSI_ACTION_ENTER_RIGHT_BRANCH_THREE = 121;      // 进入右侧第三岔路
  ASSI_ACTION_ENTER_GAS_STATION = 122;             // 进入加油站道路
  ASSI_ACTION_ENTER_HOUSING_ESTATE = 123;          // 进入小区道路
  ASSI_ACTION_ENTER_PARK_ROAD = 124;               // 进入园区道路
  ASSI_ACTION_ENTER_OVERHEAD = 125;                // 上高架
  ASSI_ACTION_ENTER_CENTER_BRANCH_OVERHEAD = 126;  // 走中间岔路上高架
  ASSI_ACTION_ENTER_RIGHT_BRANCH_OVERHEAD = 127;  // 走最右侧岔路上高架
  ASSI_ACTION_ENTER_LEFT_BRANCH_OVERHEAD = 128;   // 走最左侧岔路上高架
  ASSI_ACTION_ALONG_STRAIGHT = 129;               // 沿当前道路直行
  ASSI_ACTION_DOWN_OVERHEAD = 130;                // 下高架
  ASSI_ACTION_ENTER_LEFT_OVERHEAD = 131;          // 走左侧道路上高架
  ASSI_ACTION_ENTER_RIGHT_OVERHEAD = 132;         // 走右侧道路上高架
  ASSI_ACTION_UP_TO_BRIDGE = 133;                 // 上桥
  ASSI_ACTION_ENTER_PARKING = 134;                // 进停车场
  ASSI_ACTION_ENTER_OVERPASS = 135;               // 进入立交桥
  ASSI_ACTION_ENTER_BRIDGE = 136;                 // 进入桥梁
  ASSI_ACTION_ENTER_UNDERPASS = 137;              // 进入地下通道
}

message ActionApproach {
  optional LaneActions lane_actions = 1;
  optional MainAction main_action = 2;
  optional AssistantAction assistant_action = 3;
}

/**
 * 交通拥堵状态
 */
enum TrafficStatus {
  TRAFFIC_STATUS_NULL = 0;            // 无效值
  TRAFFIC_STATUS_OPEN = 1;            // 畅通
  TRAFFIC_STATUS_SLOW = 2;            // 缓行
  TRAFFIC_STATUS_JAM = 3;             // 拥堵
  TRAFFIC_STATUS_CONGESTED = 4;       // 严重拥堵
  TRAFFIC_STATUS_EXTREMELY_OPEN = 5;  // 极度畅通
}

message CrossingInfo {
  optional deeproute.map.CrossingType crossing_type = 1;
  optional deeproute.map.LaneTurn crossing_turn = 2;
  /// 分正负，+表示未经过路口时，自车位置到路口的距离，-表示经过路口后，自车位置到路口的距离
  optional double crossing_remaining_s = 3;
  repeated int32 freeway_main_road_lane_ids = 4;
  optional bool is_on_main_road = 5;  // 是否在主路
  optional bool is_way_point = 6;     // 是否要在该路口汇出/汇入
  optional bool need_avoid_merging = 7;  // 是否需要避让汇入车辆
  optional deeproute.map.LaneTurn traffic_lights_turn_type = 8;  // 信号灯转向
  optional string crossing_link_id = 9;  // 路口所在link的id
}

message SdRoadInfo {
  optional deeproute.map.RoadClass road_class = 1;
  /// 当前路口信息
  optional CrossingInfo crossing_info = 2;
  optional deeproute.map.LinkUsage link_usage = 3;
  optional deeproute.sd_map.LinkData.FormWay link_formway = 4;
  repeated CrossingInfo crossing_infos = 5;
  optional bool is_on_main_road = 6;
  optional deeproute.localization.LockOnRoadResult.Status lock_on_road_status = 7;
}

message AreaInfo {
  enum AreaType {
    UNKNOWN = 0;
    REST_AREA = 1;   // 服务区
    TOLL = 2;        // 收费站
    ROUNDABOUT = 3;  // 环岛
  }
  optional AreaType area_type = 1;
  optional double distance_to_area_start = 2;
  optional double distance_to_area_end = 3;
}
