syntax = "proto2";

package deeproute.map;

import "common/geometry.proto";
import "drapi/gwm_navigation.proto";

/*
  Amap route planning proto.
  Refer to see https://lbs.amap.com/api/webservice/guide/api/newroute
  sdk: https://a.amap.com/lbs/static/unzip/Android_Navi_Doc/index.html
*/

message AmapDriveRoutePlanningRequest {
  optional string key = 10000;

  optional common.PointLLH origin = 1;
  optional common.PointLLH destination = 2;
  optional int32 originType = 3;
  optional int32 strategy = 4;
  repeated common.PointLLH waypoints = 5;
  repeated common.PointLLH avoidpolygons = 6;

  enum ShowFieldsCondType {
    COST = 0;
    TMCS = 1;
    NAVI = 2;
    CITIES = 3;
    POLYLINE = 4;
  }

  repeated ShowFieldsCondType show_fileds = 7;
}

message AmapLink {
  optional int32 id = 100;

  repeated common.PointLLH polyline_llh = 1;
  optional int32 link_length = 2;
  // Link type from amap open sdk
  //  0: 普通道路
  //  1: 航道
  //  2: 隧道
  //  3: 桥梁
  //  4: 高架桥
  optional int32 link_type = 3;
  // Road class from amap open sdk
  //  0: 高速公路
  //  1: 国道
  //  2: 省道
  //  3: 县道
  //  4: 乡公路
  //  5: 县乡村内部道路
  //  6: 主要大街、城市快速道
  //  7: 主要道路
  //  8: 次要道路
  //  9: 普通道路
  //  10: 非导航道路
  optional int32 road_class = 4;
  optional string road_name = 5;
  // Road type from amap open sdk
  //  1: 主路
  //  2: 路口内部道路
  //  3: JCT道路
  //  4: 环岛
  //  5: 服务区
  //  6: 匝道
  //  7: 辅路
  //  8: 匝道与JCT
  //  9: 出口
  //  10: 入口
  //  11: A类右转专用道
  //  12: B类右转专用道
  //  13: A类左转专用道
  //  14: B类左转专用道
  //  15: 普通道路
  //  16: 左右转专用道
  //  56: 服务区与匝道
  //  53: 服务区与JCT
  //  58: 服务区与匝道以及JCT
  optional int32 road_type = 6;
  optional bool traffic_light = 7;

  optional dr.gwm.navigation.MainAction main_action = 8;
  optional dr.gwm.navigation.AssistantAction assistant_action = 9;
}

message AmapStep {
  optional int32 id = 100;

  // For Amap open api
  optional string instruction = 1;
  optional string orientation = 2;
  optional string road_name = 3;
  optional string step_distance = 4;
  optional string polyline = 5;
  // For use
  repeated common.PointLLH polyline_llh = 6;
  repeated AmapLink links = 7;

  optional dr.gwm.navigation.MainAction main_action = 8;
  optional dr.gwm.navigation.AssistantAction assistant_action = 9;
}

message AmapPath {
  optional int32 path_id = 100;
  optional uint64 path_id_64 = 101;

  optional string distance = 1;
  optional string restriction = 2;
  repeated AmapStep steps = 3;
}

message AmapRoute {
  optional string origin = 1;
  optional string destination = 2;
  optional string taxi_cost = 3;
  repeated AmapPath paths = 4;
}

message AmapDriveRoutePlanningResponse {
  optional string status = 1;
  optional string info = 2;
  optional string infocode = 3;
  optional string count = 4;
  optional AmapRoute route = 5;
}