syntax = "proto2";

package deeproute.map;

import "common/geometry.proto";
import "map/sd_map.proto";
import "map/map_common.proto";
import "routing/navinfo_routing.proto";
import "drapi/gwm_navigation.proto";

message AmapNavigationInfo {
  optional uint64 path_id = 1;  // 导航路径Id,对应GlobalRoute中的path_id
  optional int32 curr_link_id = 2;           // 当前小路段Link Id
  optional int32 curr_step_id = 3;           // 当前导航段Step Id
  optional int32 distance_to_next_link = 4;  // 到下个Link的距离[m]
  optional int32 amap_has_traffic_light = 5;  // 是否有红绿灯，0无效值，1是，2否
  optional dr.gwm.navigation.TrafficLight traffic_light =
      6;  // 红绿灯，包含状态和倒计时

  optional deeproute.common.Point3D traffic_position_2d =
      7;  // 红绿灯位置世界坐标
}

message SdHorizonLinkData {
  repeated common.Point2D shape_points = 1;
}

// vehicle sd map
message SdHorizonMap {
  repeated SdHorizonLinkData sd_horizon_link_data = 1;
  repeated sd_map.LinkData sd_horizon_map_data = 2;

  // The current link index of sd_horizon_map_data
  optional uint32 current_link_index_of_horizon = 7;

  optional navinfo.Route horizon_routing_response_data = 3;
  // identification sd_horizon_map for each frame
  optional common.Point3D positon = 4;
  optional common.Point3D euler_angles = 5;
  optional int64 time_measurement = 6;

  // traffic light info from navagation
  optional AmapNavigationInfo amap_nav_info = 8;

  optional SdRoadInfo sd_road_info = 9;

  // Used to deal with the situation where the lor sd_link_index does not
  // correspond to the routing response after the horizon map is cropped.
  optional uint32 current_link_index_of_routing_response = 10;
  //  horizon maphn是否完整，在网络不好的情况下可能会是false
  optional bool is_complete = 11 [default = true];
}

/**
 * 红绿灯
 */
message TrafficLight {
  // int32 is_valid = 1;              // 0:该信息无效，1：该信息有效
  // int32 distance = 2;              // 到达红绿灯的距离
  // TrafficLightDir dir = 3;         // 红绿灯控制方向
  // int32 wait_num = 4;              // 等红绿灯轮数
  // repeated LightState states = 5;  // 实时状态和倒计时
  optional common.PointLLH gps_point = 6;  // 红绿灯位置坐标
  // int32 light_count_down = 7;      //红绿灯倒计时，单位秒
  // LightStateType light_type = 8;   // 红绿灯颜色状态
  optional double distance = 9;  // 红绿灯距离
}

message RealTimeGuidance {
  optional uint64 path_id = 1;  // 导航路径Id,对应GlobalRoute中的path_id
  optional MainAction step_main_action = 2;  // 当前基本导航动作
  optional AssistantAction step_assistant_action = 3;  // 辅助导航动作
  optional int32 distance_to_next_step = 4;  // 到下个Step的距离[m]

  optional LaneActions lane_actions = 11;  // 车道前背景, 巡航需要全部背景车道
  // 当前step+当前link导航动作
  optional MainAction main_action = 12;            // 基本导航动作
  optional AssistantAction assistant_action = 13;  // 辅助导航动作

  repeated LaneActions lane_actions_approaching = 14
      [deprecated = true];  // 当前,下一个,下下一个车道动作

  repeated ActionApproach actions_approaching =
      15;  // 上一个,当前,下一个,下下一个车道动作

  optional TrafficStatus traffic_status = 21;  // 交通拥堵状态

  // 下个红绿灯位置
  optional TrafficLight traffic_light = 31;  // 下个红绿灯

  // 引导信息来源
  optional deeproute.sd_map.FusionMapConfig fusion_map_source = 41;
}

message FusionSdMap {
  // 必需字段，用于标识等功能
  optional int64 time_measurement = 1;

  // 播报信息
  optional RealTimeGuidance guidance = 11;

  // TODO(HongyiZhang): 导航信息
  // TODO(HongyiZhang): 路网信息
}
