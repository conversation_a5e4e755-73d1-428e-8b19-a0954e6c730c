syntax = "proto2";

package deeproute.sd_map;

import "common/geometry.proto";
import "map/common_type.proto";
import "map/sd_map_type.proto";

// This file is extracted from the horizon map proto, and the namespace
// is changed from map to sd_map

message LaneCond {
  //生效车道，16bit从左到右表示每个车道是否生效
  optional uint32 laneinfo_cond = 1;
  //时间段，可空
  optional string date = 3;
  //限制车辆类型，可空
  repeated VehicleType vehicles = 4;
  //特殊时间类型，可空
  repeated SpecialTimeType special_times = 5;
}

message LaneData {
  optional uint32 arrow = 1;       // 车道线方向
  optional uint32 bitmap = 2;      // 以位来表示的可通行车道
  optional uint32 bus_bitmap = 3;  // 以位来表示的仅公交可通行车道
  // Deprecated! 左附加车道数, for link topo， 同一个link只有一个值
  optional int64 left_add = 4;
  // Deprecated! 左附加车道数, for link topo， 同一个link只有一个值
  optional int64 right_add = 5;
  optional uint64 lane_number = 6;  // 车道总数, 包括左右附加车道
  repeated uint64 out_dr_link_ids = 7;  // 车道出边的有向路段ID
  repeated LaneCond lane_conds = 8;     // 车道条件
}

message LaneTimeSpan {
  optional uint32 index = 1;     // 从左到右的车道下标
  optional string timespan = 2;  // 分时车道生效时段信息
}

message ConnectivityData {
  optional bool forbidden = 1;              // 是否是禁转路段
  optional uint64 directed_dr_link_id = 2;  // 路段的有向ID
  repeated uint64 pass_directed_dr_link_ids =
      3;  // 从入边到出边需要经过的路段ID
}

enum Arrow {
  ARROW_NONE = 0;      // 无 仅占位
  ARROW_STRAIGHT = 1;  // 直行
  ARROW_RIGHT = 4;     // 右转
  ARROW_U_TURN = 7;    // 掉头
  ARROW_LEFT = 10;     // 左转
  ARROW_EMPTY = 13;    // 空白
}

message LaneAttr {
  enum LaneExtensionType {
    LANE_EXTENSION_NORMAL = 0;         // 常规车道
    LANE_EXTENSION_BUS = 1;            // 公交车道
    LANE_EXTENSION_TIDE = 2;           // 潮汐车道
    LANE_EXTENSION_TURNALTERABLE = 3;  // 可变车道
  }
  repeated LaneExtensionType lane_extension_types = 1;  // 车道类型
  repeated Arrow arrows = 2;                            // 车道箭头
}

message SpecLaneAttribute {
  enum SpecLaneExtensionType {
    SPEC_LANE_EXTENSION_NORMAL = 0;               // 常规车道
    SPEC_LANE_EXTENSION_BICYCLE_LANE_ROAD = 1;    // 自行车道
    SPEC_LANE_EXTENSION_EMERGENCY_LANE_ROAD = 2;  // 应急车道
  }
  optional int32 index = 1;  // 自行车道、应急车道算为车道，从左到右0起始
  optional SpecLaneExtensionType lane_extension_type = 2;  // 车道类型
}

message SDLaneTopo {
  // 出边的有向路段ID
  optional uint64 out_dr_link_id = 4;
  // 当前link和outlink左侧车道变化数
  optional int32 left_change_num = 5;
  // 当前link和outlink右侧车道变化数
  optional int32 right_change_num = 6;
}

message LaneInfoData {
  // Deprecated
  // 左侧车道变化数
  optional int32 lane_left_change_num = 2;
  // Deprecated
  // 右侧车道变化数
  optional int32 lane_right_change_num = 3;
  // 特殊车道类型
  repeated SpecLaneAttribute lane_attributes = 4;
  // 车道左右附加变化
  repeated SDLaneTopo lane_topo = 5;
}

// 电子眼
message Camera {
  optional deeproute.map.CameraType type = 1;  //电子眼类型
  optional common.PointLLH coordinate = 2;     //坐标
  optional int32 speed_limit = 3;              //限速值
}

// 单向link
message LinkData {
  enum FormWay {
    ROAD_KIND_NONE = 0;            // 无属性
    ROAD_KIND_ROUNDABOUT = 1;      // 环岛
    ROAD_KIND_DIRECTION_SPIT = 2;  // 上下线分离
    ROAD_KIND_JCT = 3;             // JCT,连接高速道路
    ROAD_KIND_CROSS_INNER = 4;     // 交叉点内Link
    ROAD_KIND_IC = 5;              // IC,连接高速和其它不同等级道路
    ROAD_KIND_PA = 6;              // 停车区
    ROAD_KIND_SA = 7;              // 服务区
    ROAD_KIND_WALK_STREET = 8;     // 步行街
    ROAD_KIND_SECONDARY = 9;       // 辅路
    ROAD_KIND_RAMP = 10;           // 匝道
    ROAD_KIND_POI_CONNECT = 11;    // POI连接路
    ROAD_KIND_TUNNEL = 12;         // 隧道
    ROAD_KIND_RIGHT_TURN = 13;     // 提前右转
    ROAD_KIND_INNER_REGION = 14;   // 区域内道路
    ROAD_KIND_LEFT_TURN = 15;      // 提前左转
    ROAD_KIND_U_TURN = 16;         // 调头口
    ROAD_KIND_MAIN_SECONDARY_ENTRANCE = 17;    // 主辅路出入口
    ROAD_KIND_REVERSAL_LEFT = 18;              // 借道左转
    ROAD_KIND_MAIN_ROAD = 19;                  // 主路
    ROAD_KIND_ELEVATED = 20;                   // 高架路
    ROAD_KIND_TOLL = 21;                       // 收费站
    ROAD_KIND_HIGHWAY_PORT = 22;               // 高速出入口
    ROAD_KIND_AHEAD_TURN_RIGHT = 23;           // 提前右转
    ROAD_KIND_WIDE_LANE = 24;                  // 宽车道道路
    ROAD_KIND_BIKE_LANE = 25;                  // 自行车道路
    ROAD_KIND_SHOULDER = 26;                   // 应急车道道路
    ROAD_KIND_CROSS_LINE_OVERPASS = 29;        // 跨线天桥
    ROAD_KIND_SUNKEN_ROAD = 30;                // 下沉道路
    ROAD_KIND_BRIDGE = 31;                     // 固定桥
    ROAD_KIND_ENCLOSURE = 32;                  // 全封闭道路
    ROAD_KIND_UNDEFINED = 33;                  // 未定义交通区域
    ROAD_KIND_BUS = 34;                        // 公交专用道
    ROAD_KIND_VIEW = 35;                       // 风景路线
    ROAD_KIND_PARK_ENTRANCE = 36;              // 停车场出入口连接路
    ROAD_KIND_MOVABLE_BRIDGE = 37;             // 移动式桥
    ROAD_KIND_FRONT_DOOR = 38;                 // 门前路
    ROAD_KIND_TRUCK_LANE = 39;                 // 货车专用道
    ROAD_KIND_NORMAL = 40;                     // 普通道路
    ROAD_KIND_CONSTRUCTION = 41;               // 建设中道路
    ROAD_KIND_INTERSECTION = 42;               // 十字路口
    ROAD_KIND_HIGHWAY_CONNECTION = 43;         // 高速连接路
    ROAD_KIND_NONSTANDARD_ROUNDABOUT = 44;     // 非标准环岛
    ROAD_KIND_SPECIAL_CONNECTION = 45;         // 特殊连接路
    ROAD_KIND_PARKING_OCCUPY_ROAD = 46;        // 包含占道停车场
    ROAD_KIND_OWNERSHIP = 47;                  // 私道
    ROAD_KIND_INNER_VIRTUAL_CONNECT = 48;      // 区域内虚拟连接
    ROAD_KIND_TAXI = 49;                       // 出租车专用道
    ROAD_KIND_TIDE = 50;                       // 潮汐车道
    ROAD_KIND_STEP_ROAD = 51;                  // 阶梯道路
    ROAD_KIND_INNER_CROSS_ROAD = 52;           // 路口面道路
    ROAD_KIND_ENTRANCE_AND_EXIT_CONNECT = 53;  // 出入口连接路
    ROAD_KIND_RAMP_BOTH_PASS = 54;             // 匝道双通
    ROAD_KIND_MOUNTAIN_ROAD = 55;              // 山路
    ROAD_KIND_SUNKEN_ROAD_PORT = 56;           // 下沉道路出入口
    ROAD_KIND_OTHER = 57;                      // 其他
    ROAD_KIND_VIRTUAL_SOLID_CONNECTION = 58;   // 虚实连接
    ROAD_KIND_PARKING_INTERNAL_ROAD = 59;      // 停车场内部道路

    ROAD_KIND_ENTER_SINGLE_CROSS = 27;  // 进入复合路口道路
    ROAD_KIND_ENTER_MULTI_CROSS = 28;   // 进入单一路口道路
  }

  optional string shape_points = 3;

  optional uint64 dr_link_id = 1;   // 元戎有向路段id
  optional uint64 navinfo_id = 19;  // 路段id
  optional uint64 raw_id = 29;      // 母库路段id

  optional bool forward = 20;            // 是否为正向
  optional uint32 length = 2;            // 路段长度
  optional bool traffic_light = 4;       // 路段末尾是否有红绿灯
  optional uint32 speed_limit = 5;       // 道路限速km/h
  optional uint32 forward_lane_num = 6;  // 正向可用车道数
  optional string name = 7;              // 路段名称
  optional uint32 priority = 8;          // 路段等级
  repeated LaneData lanes = 9;           // 车信信息对象数组
  optional uint32 reversible_lane_bitmap =
      10;  // 以位来表示各车道是否是潮汐车道
  optional uint32 variable_turn_lane_bitmap =
      11;  // 以位来表示各车道是否是转向可变车道
  optional uint32 conditional_bus_lane_bitmap =
      12;  // 以位来表示各车道是否是分时公交专用车道
  repeated LaneTimeSpan conditional_bus_lane_timespans =
      13;  // 分时公交车道生效时间段信息, 数组长度为分时公交车道数
  repeated LaneTimeSpan reversible_lane_timespans =
      14;  // 潮汐车道生效时间段信息, 数组长度为潮汐车道数
  optional uint32 usage = 15;                     // 道路用途标识
  repeated FormWay formways = 23;                 // 多种道路形态
  optional bool tunnel = 16;                      // 是否为隧道
  repeated ConnectivityData in_links_data = 17;   // 入边信息对象数组
  repeated ConnectivityData out_links_data = 18;  // 出边信息对象数组
  optional bool elevated = 21;                    // 是否为高架
  optional bool toll_station_at_link_start = 22;  // 路段起点处是否有收费站
  repeated LaneAttr lane_attrs = 24;  // 车道级属性，从左到右表示所有车道的信息
  optional uint64 start_node_id = 25;              // 起点节点id
  optional uint64 end_node_id = 26;                // 终点节点id
  optional uint64 main_node_id = 27;               // 主节点id
  optional uint32 tile_id = 28;                    // 路段所在图幅ID
  optional uint32 experience_speed_limit = 30;     // 道路经验限速km/h
  optional int32 admin_code = 31;                  // 行政区划代码
  optional LaneInfoData lane_info_data = 32;       // 车道信息
  optional uint32 exact_speed_limit = 33;          // 道路确定限速km/h
  repeated Camera camera = 34;                     // 电子眼
  repeated SpeedLimitInfo speed_limit_infos = 35;  // 限速信息

  repeated common.PointLLH points = 101;   // 形状点, 使用编码02经纬度
  repeated common.Point2D points2d = 102;  // 形状点, 使用地图坐标系
}

message SdLinks {
  enum Frame {
    UNKNOWN = 0;
    WGS84 = 1;
    GCJ02 = 2;
    UTM = 3;
    VEHICLE = 4;
  }

  optional Frame link_frame = 1;  // coordinate frame where links are defined at
  repeated LinkData links =
      2;  // link data, its points are defined wrt. to link_frame
}

message SDMap {
  message Header {
    optional string map_version = 1;
    optional string release_date = 2;
  }
  optional Header header = 1;
  repeated LinkData links = 2;
  repeated uint64 not_found_dr_link_ids = 3;
}

message SDMapOnboard {
  optional int32 id = 1;
  optional SDMap sd_map = 2;
}

// also used in fusion map
message FusionMapConfig {
  enum FusionMapSource {
    FUSION_TENCENT = 1;
    FUSION_AMAP = 2;
  }
  optional FusionMapSource main_action_source = 1 [default = FUSION_AMAP];
  optional FusionMapSource assistant_action_source = 2 [default = FUSION_AMAP];
  optional FusionMapSource lane_action_source = 3 [default = FUSION_AMAP];
}

message SDMapVersionOnboard {
  message TencentSDVersion {
    message FormatVersion {
      optional uint32 main_version = 1;
      optional uint32 sub_version = 2;
    }
    optional FormatVersion format_version = 1;
    optional uint32 versoin_id = 2;
  }
  enum TransferProtocol {
    HTTP = 0;
    GRPC = 1;
  }

  message ConnectConfig {
    optional int32 request_timeout_sec = 1;
    optional int32 connection_timeout_sec = 2;
    optional bool use_long_connection = 3;
    optional string service_name = 4;
    // gzip/zstd/
    optional string encoding_type = 5;
  }

  message GraphMatchRoutingConfig {
    optional bool able_segmented_routing = 1;
    optional int32 pre_link_num = 2;
    optional uint64 max_segm_distance = 3;
    optional bool send_pre_polyline = 4;

    // 最大分段数量
    optional int32 max_segm_num = 5;
  }

  message ImageReportConfig {
    optional bool enable_for_lane_num = 1;
    optional bool enable_for_lane_topo = 2;
    optional bool enable_for_lane_marker = 3;
    optional bool enable_for_traffic_light = 4;
    optional bool enable_for_lane_attribute = 5;
  }
  message LaneNumReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;

    optional bool check_merge_in = 3;
    optional bool check_merge_out = 4;
    optional bool check_forward_merge_split = 5;
    optional bool check_lane_num_change = 6;
    optional int32 sample_report = 7 [default = 10];
    optional int32 merge_scene_distance_default = 8 [default = 20];
    optional int32 merge_scene_distance_highway = 9 [default = 50];

    optional bool check_special_formways = 10;
    optional bool is_use_diversion_and_curb = 11;
    // in sensing coordinate system
    optional double interest_pts_min_x = 12 [default = -5.0];
    optional double interest_pts_max_x = 13 [default = 10.0];
  }
  message TunnelTSRReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;
    // 同一个限速值的最小观测delta距离
    optional int32 min_delta_distance_observed = 3 [default = 0];
    // 同一个限速值观测的最大距离跳变，如果大于这个值，则分成不同的组
    optional int32 max_delta_distance_gap = 4 [default = 15];
  }
  message ObstacleReportConfig {
    optional bool enable_report_vhr = 1;

    optional int32 max_movement = 3 [default = 25]; // In meter
    optional int32 max_report_interval_sec = 4 [default = 10];

  }
  message MpInfoReportConfig {
    optional bool enable_report_vhr = 1;

    optional int32 max_accumulated_info_num = 3 [default = 10];
    optional int32 max_report_interval_sec = 4 [default = 10];
  }
  message LaneTopoReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;

    optional int32 windown_size = 3 [default = 3];
    optional bool skip_crossing = 4;
    optional int32 repeat_report_max = 5 [default = 10];
    optional bool check_topo_report_same = 6;

    optional int32 search_forward_meter = 7 [default = 100];
    optional int32 search_backward_meter = 8 [default = 100];

    optional bool skip_mismatch_lane_num_event = 9 [deprecated = true];
    optional bool allow_lane_num_mismatch = 10;
  }
  message AmapReportConfig {
    optional bool enable_report_vhr = 1;
    optional int32 link_max_num = 3 [default = 30];
    optional int32 min_report_interval_sec = 4 [default = 10];
    optional bool check_gps_navi_type = 5;
    optional bool check_speed_limit = 6;
    optional bool check_lane_num = 7;

    optional bool enable_report_traffic_light_vhr = 21;

    optional bool enable_report_camera_vhr = 41;
  }
  message LaneMarkerReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;
  }
  message TrafficLightReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;

    optional float min_score = 3 [default = 0.3];
    optional int32 max_cache_num = 4 [default = 100];

  }
  message LaneAttributesReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool enable_report_img = 2;
    optional int32 link_max_num = 3 [default = 30];
    optional int32 min_report_interval_sec = 4 [default = 10];
  }
  message ManualSpeedRegulationReportConfig {
    optional bool enable_report_vhr = 1;
  }
  message BoundaryReportConfig {
    optional bool enable_report_vhr = 1;
    optional bool is_only_tunnel = 2 [default = true];
    optional int32 sample_movement_distance = 3 [default = 1]; // In meter
    optional int32 max_cache_num = 4 [default = 10];
  }
  // Used to control map engine crowd-source report events.
  message DataCloseLoopConfig {
    optional LaneNumReportConfig lane_num_config = 1;
    optional ObstacleReportConfig obstacle_config = 2;
    optional MpInfoReportConfig mp_info_config = 3;
    optional LaneTopoReportConfig lane_topo_config = 4;
    optional AmapReportConfig amap_config = 5;
    optional LaneMarkerReportConfig lane_marker_config = 6;
    optional TrafficLightReportConfig traffic_light_config = 7;
    optional LaneAttributesReportConfig lane_attributes_config = 8;
    optional ManualSpeedRegulationReportConfig manaul_speed_regulation_config = 14;
    optional BoundaryReportConfig boundary_config = 15;
    optional TunnelTSRReportConfig tunnel_tsr_config = 16;
  }

  message TopoInfoConfig {
    optional bool enable_for_merge_in = 1;
    optional bool enable_for_split_out = 2;
    optional bool enable_lane_mismatch_topo = 3;
    optional bool enable_all_topo = 4;
  }
  message CrsConfig {
    optional bool turn_off_force_right_for_tunnel_near_right_split = 1;
    optional int32 force_right_highway_dist_threshold = 2 [default = 1000]; // In meter
    optional int32 force_right_default_dist_threshold = 3 [default = 500];
  }
  message StaticRoutingConfig {
    optional TopoInfoConfig topo_info_config = 1;
    optional CrsConfig crs_config = 2;
    optional bool switch_off_bus_lane_expand = 3;
    optional bool switch_off_reset_bm = 4;
    optional bool switch_off_reset_speed_limit = 5;
    optional bool switch_off_reset_lane_num = 6;
    optional bool switch_off_reset_arr = 7;
    
    optional bool enable_p0_from_server = 8;
    optional bool enable_crs_from_server = 9;
  }
  message LocalRoutingConfig {
    optional StaticRoutingConfig static_routing_config = 1;
  }

  optional string sd_map_plus_version = 1;
  optional TencentSDVersion tencent_sd_version = 2;
  optional TransferProtocol transfer_protocol = 3;
  repeated ConnectConfig connect_config = 4;

  optional GraphMatchRoutingConfig graph_match_routing_config = 5;

  // image_report_config can be deprecated when all ota2 version are not used on car.
  optional ImageReportConfig image_report_config = 6;
  // Can be deprecated when data_close_loop_config_json and local_routing_config_json
  // is used.
  optional DataCloseLoopConfig data_close_loop_config = 7;
  optional LocalRoutingConfig local_routing_config = 8;

  // DataCloseLoopConfig proto string. Used to replace data_close_loop_config.
  optional string data_close_loop_config_str = 9;
  // LocalRoutingConfig proto string. Used to replace local_routing_config
  optional string local_routing_config_str = 10;

  // DataCloseLoopConfig proto string. Used to replace data_close_loop_config.
  optional string data_close_loop_config_json = 11;
  // LocalRoutingConfig proto string. Used to replace local_routing_config
  optional string local_routing_config_json = 12;

  // Fusion map config
  optional FusionMapConfig fusion_map_config = 13;
}
