syntax = "proto2";

package deeproute.map;

message CrowdSourceReportData {
  // Each info is a json string of SDEventInfo proto, which is define at:
  // https://code.deeproute.ai/deeproute-org/localization-mapping-calibration/all/lam-common/-/blob/dev_master/lam_common/proto/lam_common/sd_material_server/sd_event.proto
  repeated string sd_event_info_jsons = 1;
}
//线下众包的benchmark使用的proto
message CrowdSourceBenchmarkDebugInfo {
  repeated CrowdSourceReportData crowd_source_report_datas = 1;
}

message SdMapDebugInfo {
  optional CrowdSourceReportData crowd_source_report_data = 1;
}