syntax = "proto2";

package deeproute.drivers;

message CanMsg {
  optional double timestamp = 1;  // in seconds
  optional uint32 arbitration_id = 2;  // can id
  optional uint32 channel = 3;
  optional uint32 dlc = 4;  // data length code
  optional bytes data = 5;
  optional bool is_extended_id = 6;
  optional bool is_error_frame = 7;
  optional bool is_remote_frame = 8;
  optional bool is_fd = 9;
  optional bool is_rx = 10;
  optional bool bitrate_switch = 11;
  optional bool error_state_indicator = 12;
}

// topic: /sensors/can_msg
message CanPkt {
  optional double timestamp = 1; // in seconds
  optional uint64 msg_num = 2;
  repeated CanMsg msg = 3;
}

message CanMsgSmpl {
  optional uint64 timestamp = 1;  // in micro-seconds
  optional uint32 can_id = 2;
  optional uint32 len_channel = 3;  // low 16bit: valid byte length of data, high 16bit: channel
  //@C_Mark@64@;
  optional bytes data = 4;
}

message CanRawArray5K {
  optional uint64 timestamp = 1; // in micro-seconds
  optional uint32 canframenum = 2; // actually can frame quantity
  optional uint32 rolling_counter = 3;
  //@C_Mark@59@;
  repeated CanMsgSmpl can_raw = 4;
  optional uint32 crc = 5;
}