syntax = "proto2";

package deeproute.drivers.gnss;

import "common/header.proto";
import "common/geometry.proto";

// Solution from a Global Navigation Satellite System (GNSS) receiver without
// fused with any IMU.
message Gnss {
  optional deeproute.common.Header header = 1;

  optional sfixed64 measurement_time = 2;  // in us.

  // When velocity is computed from differentiating successive position
  // computations, a non-zero latency is incurred. The velocity refers to the
  // time measurement_time - velocity_latency.
  // When velocity is computed using instantaneous Doppler frequency, there is
  // no latency. We should have velocity_latency = 0.
  optional float velocity_latency = 3 [default = 0.0];  // In seconds.

  // Position of the GNSS antenna phase center.
  optional deeproute.common.PointLLH position = 4;

  // East/north/up in meters.
  optional deeproute.common.Point3D position_std_dev = 5;

  // East/north/up in meters per second.
  optional deeproute.common.Point3D linear_velocity = 6;
  // East/north/up in meters per second.
  optional deeproute.common.Point3D linear_velocity_std_dev = 7;

  optional float time_diff_sec = 8;
  optional float time_solution_sec = 9;

  optional uint32 num_sats = 10;  // Number of satellites in position solution(tracked).
  optional uint32 num_sats_in_solution = 11;

  optional bool sln_calc = 12;

  // GNSS solution type.
  enum Type {
    // It is recommended not using the GNSS solution if solution type is INVALID
    // or PROPAGATED.
    INVALID = 0;     // Invalid solution due to insufficient observations,
                     // integrity warning, etc.
    PROPAGATED = 1;  // Propagated by a Kalman filter without new observations.

    // It is recommended using the following types of solution.
    SINGLE = 2;       // Standard GNSS solution without any corrections.
    PSRDIFF = 3;      // Pseudorange differential solution, including WAAS/SBAS
                      // solution.
    PPP = 4;          // Precise Point Positioning (PPP) solution.
    RTK_FLOAT = 5;    // Real Time Kinematic (RTK) float solution.
    RTK_INTEGER = 6;  // RTK integer solution.
  }
  optional Type type = 13;
  optional uint32 solution_status = 14;
  optional uint32 position_type = 15;
}

// Range information for one GNSS satellite.
message RangeInfo {
  enum Constellation {
    GPS = 0;
    GLONASS = 1;
    SBAS = 2;
    GALILEO = 3;
    BEIDOU = 4;
    QZSS = 5;
  }
  optional Constellation constellation = 1 [default = GPS];

  optional int32 prn = 2;  // Satellite PRN number.

  // Carrier frequency of the signal used in this range measurement. Note that
  // GLONASS uses FDMA, so different satellites transmit signals on different
  // frequencies.
  optional fixed32 carrier_frequency = 3;  // In Hz.

  optional double pseudorange = 4 [default = nan];  // In meters.

  optional float pseudorange_std_dev = 5 [default = nan];  // In meters.

  optional double carrier_phase = 6 [default = nan];  // In cycles.

  optional float carrier_phase_std_dev = 7 [default = nan];  // In cycles.

  optional float doppler = 8 [default = nan];  // In Hz.

  optional float doppler_std_dev = 9 [default = nan];  // In Hz.

  // Carrier-to-noise ratio (signal // strength) in dB-Hz.
  optional int32 cno = 10 [default = -1];

  // Carrier phase lock time in seconds.
  optional int32 lock_time = 11 [default = -1];
}

// All range measurements at a time.
message GnssRange {
  optional deeproute.common.Header header = 1;

  // The time of range measurement, seconds since the GPS epoch (Jan 6, 1980).
  optional double measurement_time = 2;  // In seconds.

  repeated RangeInfo ranges = 3;
}

// gnss raw data
message RawData {
  optional deeproute.common.Header header = 1;
  optional bytes data = 2;
}
