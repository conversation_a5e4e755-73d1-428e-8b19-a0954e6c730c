syntax = "proto2";

package deeproute.drivers.gnss;

import "common/header.proto";
import "common/geometry.proto";

// Measurements from an inertial measurement unit (IMU). The solution is with
// respect to the IMU by default.
message Imu {
  optional deeproute.common.Header header = 1;

  // The time of IMU measurement.
  optional sfixed64 measurement_time = 2;  // in us.

  // When measurement_span is non-zero, the gyroscope and accelerometer
  // measurements are averaged for the period from
  // (measurement_time - measurement_span) to measurement_time. Usually,
  //      measurement_span = 1 / sampling_frequency.
  //
  // When measurement_span is 0, angular_velocity and linear_acceleration are
  // instantaneous at measurement_time.
  optional float measurement_span = 3 [default = 0.0];  // In seconds.

  // gps time
  optional sfixed64 time_gps = 4;

  // Forward/left/up in meters per square second.
  optional deeproute.common.Point3D linear_acceleration = 5;

  // Around forward/left/up axes in radians per second.
  optional deeproute.common.Point3D angular_velocity = 6;
}
