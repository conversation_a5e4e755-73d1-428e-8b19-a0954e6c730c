package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "ultrasonic_proto",
    visibility = ["//visibility:public"],
    srcs = ["ultrasonic.proto"],
    deps = [
        "//proto/common:header_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "ultrasonic_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":ultrasonic_proto",
    ],
)

python_proto_library(
    name = "ultrasonic_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":ultrasonic_proto",
    ],
)

proto_library(
    name = "gm_ultrasonic_proto",
    visibility = ["//visibility:public"],
    srcs = ["gm_ultrasonic.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gm_ultrasonic_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gm_ultrasonic_proto",
    ],
)

python_proto_library(
    name = "gm_ultrasonic_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gm_ultrasonic_proto",
    ],
)

proto_library(
    name = "config_proto",
    visibility = ["//visibility:public"],
    srcs = ["config.proto"],
    deps = [
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "config_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":config_proto",
    ],
)

python_proto_library(
    name = "config_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":config_proto",
    ],
)

