syntax = "proto2";

package deeproute.drivers.ultrasonic;

import "common/header.proto";

enum EmitStatus {
  OFF = 0;
  RECV_ONLY = 1;
  EMIT_AND_RECV = 2;
}
enum MsgType {
  OBJECT = 0;
  OBSTACLE = 1;
  PARKING_SLOT = 2;
}
enum UltrasonicType {
  UNKNOWN = 0;
  M5_ULTRA = 1;
  M7_ULTRA = 2;
  LONG_HORN = 3;
  M7_BOSCH = 4;
  GWM_C01 = 5;
  SMART_HY11_BOSCH = 6;
}
message UltrasonicState {
  enum WorkStsType {
    DISABLE = 0;
    ENABLE = 1;
    ACTIVE = 2;
    FAILED = 3;
  }
  repeated uint32 object_detect_area_list = 1;
  optional bool fpas_dispcmd = 2;
  optional WorkStsType apasys_workstatus = 3;
}

message UltrasonicObject {
  optional deeproute.common.Header header = 1;
  optional double distance = 2 [default = -1.0];
  optional double cross_distance_left = 3 [default = -1.0];
  optional double cross_distance_right = 4 [default = -1.0];
  optional uint32 cross_distance_left_timestmap = 5;
  optional uint32 cross_distance_right_timestmap = 6;
  optional double confidence = 7;
  optional uint32 blind_pot = 8;
  optional uint32 echo_tof_1 = 9;
  optional uint32 echo_width_1 = 10;
  optional uint32 echo_peak_1 = 11;
  optional uint32 echo_tof_2 = 12;
  optional uint32 echo_width_2 = 13;
  optional uint32 echo_peak_2 = 14;
  optional uint32 ringing_time = 15;
  optional EmitStatus emit_status = 16;
  optional uint32 id = 17;
  optional uint32 echo_tof = 18;
  optional uint32 echo_width = 19;
  optional uint32 echo_peak = 20;
}

message UltrasonicObstacle {
  enum HeightType {
    HEIGHT_TYPE_LOW = 0;
    HEIGHT_TYPE_HIGHT = 1;
    HEIGHT_TYPE_TRAVERSIBLE = 2;
    HEIGHT_TYPE_UNKNOWN = 3;
    HEIGHT_TYPE_SPECIAL1 = 4;//同致的特殊属性
  }

  enum ObstacleType {
    OBSTACLE_TYPE_NONE = 0;
    OBSTACLE_TYPE_POINT = 1;
    OBSTACLE_TYPE_STRAIGHT0_CORNER = 2;
    OBSTACLE_TYPE_STRAIGHT1_CORNER = 3;
    OBSTACLE_TYPE_STRAIGHT2_CORNER = 4;
    OBSTACLE_TYPE_RESERVED = 5;
  }

  optional deeproute.common.Header header = 1;
  optional HeightType obstacle_height = 2;
  optional double obstacle_height_prob = 3;
  optional double point1_x = 4;
  optional double point1_y = 5;
  optional double point2_x = 6;
  optional double point2_y = 7;
  optional ObstacleType obstacle_type = 8;
  optional double confidence = 9;
  optional uint32 id = 10;
}

message UltrasonicParkingSlot {
  enum DepthReference {
    DEPTH_REFERENCE_NONE = 0;
    DEPTH_REFERENCE_CURB = 1;
    DEPTH_REFERENCE_WALL = 2;
    DEPTH_REFERENCE_VIRTUAL = 3;
    DEPTH_REFERENCE_LOW = 4;
    DEPTH_REFERENCE_HIGH = 5;
    DEPTH_REFERENCE_UNKNOWN = 6;
    DEPTH_REFERENCE_RESERVED = 7;
  }

  enum SlotType {
    SLOT_TYPE_LEFT_PARALLEL = 0;
    SLOT_TYPE_RIGHT_PARALLEL = 1;
    SLOT_TYPE_LEFT_CROSS = 2;
    SLOT_TYPE_RIGHT_CROSS = 3;
    SLOT_TYPE_RESERVED1 = 4;
    SLOT_TYPE_RESERVED2 = 5;
    SLOT_TYPE_RESERVED3 = 6;
    SLOT_TYPE_INVALID = 7;
  }

  enum SlotStatus {
    SLOT_STATUS_PS_NONE = 0;
    SLOT_STATUS_PS_OK = 1;
    SLOT_STATUS_RESERVED = 2;
  }

  enum ObjectType {
    OBJECT_TYPE_NONE = 0;
    OBJECT_TYPE_VIRTUAL = 1;
    OBJECT_TYPE_REAL = 2;
    OBJECT_TYPE_RESERVED = 3;
  }

  optional deeproute.common.Header header = 1;
  optional uint32 id = 2;
  optional DepthReference depth_reference = 3;
  optional SlotType slot_type = 4;
  optional SlotStatus slot_status = 5;
  optional double object1_x = 6;
  optional double object1_y = 7;
  optional double object1_alpha = 8;
  optional ObjectType object1_type = 9;
  optional double object2_x = 10;
  optional double object2_y = 11;
  optional double object2_alpha = 12;
  optional ObjectType object2_type = 13;
  optional double length = 14;
  optional double depth = 15;

  optional double obj1_start_pt_x = 16;
  optional double obj1_start_pt_y = 17;
  optional ObjectType obj1_start_pt_status = 18;
  optional double obj1_end_pt_x = 19;
  optional double obj1_end_pt_y = 20;
  optional ObjectType obj1_end_pt_status = 21;

  optional double obj2_start_pt_x = 22;
  optional double obj2_start_pt_y = 23;
  optional ObjectType obj2_start_pt_status = 24;
  optional double obj2_end_pt_x = 25;
  optional double obj2_end_pt_y = 26;
  optional ObjectType obj2_end_pt_status = 27;

  optional double slot_obj_start_pt_x = 28;
  optional double slot_obj_start_pt_y = 29;
  optional ObjectType slot_obj_start_pt_status = 30;
  optional double slot_obj_end_pt_x = 31;
  optional double slot_obj_end_pt_y = 32;
  optional ObjectType slot_obj_end_pt_status = 33;
}

message Ultrasonic {
  optional deeproute.common.Header header = 1;
  //@C_Mark@12@;
  repeated UltrasonicObject objects = 2;
  optional MsgType msg_type = 3;
  //@C_Mark@20@;
  repeated UltrasonicObstacle obstacles = 4;
  //@C_Mark@6@;
  repeated UltrasonicParkingSlot parking_slots = 5;
  optional UltrasonicType type = 6;

  optional UltrasonicState state = 7;
}