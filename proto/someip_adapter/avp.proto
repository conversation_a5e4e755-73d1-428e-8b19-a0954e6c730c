syntax = "proto3";
package deeproute.someip_adapter.gwm;

// topic: /gwm/havp_path_info
message HAVPPathInfo {
  bytes havp_learning_world_array = 1;
}

// topic: /gwm/havp_renderg_info
message HAVPRendergInfo {
  bytes havp_veh_data_array = 1;
}

// topic: /gwm/havp_path_map
message HAVPPathMap {
  bytes havp_map_file_array = 1;
}

// topic: /gwm/havp_map_upd_st
message PrkgLotMapUpdSt {
  enum MapUpdSt {
    MAPUPDST_MAPUDPNONE = 0;
    MAPUPDST_UNSUCCESS = 1;
    MAPUPDST_UPDATE_SUCCESS = 2;
    MAPUPDST_UPDATING = 3;
  }
  MapUpdSt map_upd_st = 1;
}

// topic: /gwm/havp_svp_swt_req
message HAVP_SVPSwtReq {
  enum BtnEnaReq {
    BTNENAREQ_NONE = 0;
    BTNENAREQ_CONTINUE_SEARCH_SLOT = 1;
    BTNENAREQ_ACTIVE_SIGNAL = 2;
    BTNENAREQ_START_HAVP_VR = 3;
    BTNENAREQ_CONFRIM_MAPBUILT = 4;
    BTNENAREQ_CANCEL = 5;
    BTNENAREQ_CONFRIM_START_PARKING = 6;
    BTNENAREQ_SEARCH_SLOT_ALONG_WAY = 7;
    BTNENAREQ_HAVP_COMPLETED = 8;
    BTNENAREQ_TRY = 9;
    BTNENAREQ_LEARNING_COMPLETED = 10;
    BTNENAREQ_CONTINUE_LEARN = 11;
    BTNENAREQ_START_SVP_VR = 12;
  }
  BtnEnaReq btn_ena_req = 1;
}

// topic: /gwm/havp_prkg_typ_req
message PrkgTypReq {
  enum SetPrkgSlotTyp {
    SETPRKGSLOTTYP_NO_SET = 0;
    SETPRKGSLOTTYP_SET_PRKING_SLOT = 1;
    SETPRKGSLOTTYP_SET_PRKING_SLOT_AREA = 2;
    SETPRKGSLOTTYP_RANDOM = 3;
  }
  SetPrkgSlotTyp set_prkg_slot_typ = 1;
}

// topic: /gwm/havp_prkg_flr_req
message PrkgFlrReq {
  int32 set_map_flr = 1;
}

// topic: /gwm/havp_prkg_slot_req
message PrkgSlotReq {
  uint32 set_prkg_slot = 1;
}

// topic: /gwm/havp_prkg_area_req
message PrkgAreaReq {
  uint32 set_prkg_area = 1;
}

// topic: /gwm/havp_lvng_poi_req
message LvngPOIReq {
  uint32 set_lvng_poi = 1;
}

// topic: /gwm/havp_tar_prkg_id_req
message TarPrkgIDReq {
  uint32 tar_map_id = 1;
}

// topic: /gwm/havp_near_slot_id_req
message NearSlotIDReq {
  uint32 sel_near_slot_id = 1;
}

// topic: /gwm/havp_function_req
message HAVPReq {
  enum HAVPfunctionReq {
    HAVPFUNCTIONREQ_NO_REQUEST = 0;
    HAVPFUNCTIONREQ_REQUEST = 1;
  }
  HAVPfunctionReq havp_function_req = 1;
}

// topic: /gwm/havp_auto_push_req
message AutoReq {
  enum AutoPushReq {
    AUTOPUSHREQ_NO_REQUEST = 0;
    AUTOPUSHREQ_REQUEST = 1;
  }
  AutoPushReq auto_push_req = 1;
}

// topic: /gwm/havp_svp_function_req
message SVPReq {
  enum SVPfunctionReq {
    SVPFUNCTIONREQ_NO_REQUEST = 0;
    SVPFUNCTIONREQ_REQUEST = 1;
  }
  SVPfunctionReq svp_function_req = 1;
}

// topic: /gwm/havp_avm_sts_req
message AVMDispReq {
  enum AVMStsReq {
    AVMSTSREQ_OFF = 0;
    AVMSTSREQ_ON = 1;
  }
  AVMStsReq avm_sts_req = 1;
}

// topic: /gwm/havp_avm_sts_resp
message AVMDispResp {
  enum AVMStsResp {
    AVMSTSRESP_OFF = 0;
    AVMSTSRESP_ON = 1;
  }
  AVMStsResp avm_sts_resp = 1;
}

// topic: /gwm/havp_svp_scrn_info
message HAVP_SVPScrnInfoStruct {
  enum MapUpdateReq {
    MAPUPDATEREQ_NO_REQUEST = 0;
    MAPUPDATEREQ_REQUEST = 1;
  }
  MapUpdateReq mapupdatereq = 1;
  uint32 currmapid = 2;
  enum FunctBtnDisp {
    FUNCTBTNDISP_FUNCTBTNNONE = 0;
    FUNCTBTNDISP_START_HAVP = 1;
    FUNCTBTNDISP_CONTINUE_HAVP = 2;
    FUNCTBTNDISP_START_SVP = 3;
    FUNCTBTNDISP_CONTINUE_SVP = 4;
    FUNCTBTNDISP_START_APA = 5;
    FUNCTBTNDISP_TRY = 6;
    FUNCTBTNDISP_SEARCH_SLOT_ALONG_WAY = 7;
    FUNCTBTNDISP_APA_CONTINUESEARCHSLOT = 8;
    FUNCTBTNDISP_STARTLEARNING = 9;
  }
  FunctBtnDisp functbtndisp = 3;
  enum FunctBtnSts {
    FUNCTBTNSTS_UNAVAILABLE = 0;
    FUNCTBTNSTS_AVAILABLE = 1;
    FUNCTBTNSTS_HIGHLIGHT = 2;
  }
  FunctBtnSts functbtnsts = 4;
  enum FunctWorkSts {
    FUNCTWORKSTS_STANDBY = 0;
    FUNCTWORKSTS_MAPBUILDING = 1;
    FUNCTWORKSTS_MAPBUILT_COMPLETE = 2;
    FUNCTWORKSTS_FUNCTWORKCRUISE = 3;
    FUNCTWORKSTS_FAILURE = 4;
    FUNCTWORKSTS_PAUSE = 5;
    FUNCTWORKSTS_PARKING = 6;
    FUNCTWORKSTS_COMPLETE = 7;
    FUNCTWORKSTS_FAULT = 8;
  }
  FunctWorkSts functworksts = 5;
  enum SignalIndnc {
    Signal_Indnc_CAN = 0;
    Signal_Indnc_SOME_IP = 1;
  }
  SignalIndnc signal_indnc = 6;
  enum InterfaceDisTyp {
    INTERFACEDISTYP_NONE = 0;
    INTERFACEDISTYP_PRE_MAPBUILT = 1;
    INTERFACEDISTYP_MAPBUILTING = 2;
    INTERFACEDISTYP_INTERFACEDISCRUISE = 3;
    INTERFACEDISTYP_MAPBUILT_COMPLETE = 4;
    INTERFACEDISTYP_HAVP_COMPLETED = 5;
    INTERFACEDISTYP_MAPSHOWTOSTART = 6;
    INTERFACEDISTYP_GUIDANCE = 7;
    INTERFACEDISTYP_SELECTSLOT_3D = 8;
    INTERFACEDISTYP_CRUISE_3D = 9;
    INTERFACEDISTYP_CHOOSE_EXIT = 10;
    INTERFACEDISTYP_CRUISE_EXIT = 11;
  }
  InterfaceDisTyp interfacedistyp = 7;
  enum PopupDisp {
    POPUPDISP_NONE = 0;
    POPUPDISP_START_LEARNING = 1;
    POPUPDISP_LEARN_ROUTE_CONFIRM = 2;
    POPUPDISP_TURN_ON_BACKGROUND_FUNCTIONS = 3;
    POPUPDISP_PLEASE_ADJUST_UNDER_P = 4;
    POPUPDISP_HAVP_RAMPWAY = 5;
    POPUPDISP_HAVP_ENVIRONMENT_EMPTY = 6;
    POPUPDISP_OUTSIDE_THE_UNDERGROUND_CAR_PARK = 7;
    POPUPDISP_HAVP_CAMERA_BLOCKED = 8;
    POPUPDISP_HAVP_LOOP_CAMERA_FAULTY = 9;
    POPUPDISP_HAVP_RADAR_FAULTY = 10;
    POPUPDISP_HAVP_ASSOCIATED_SYSTEM_FAULTY = 11;
    POPUPDISP_HAVP_SYSTEM_FAULTY = 12;
    POPUPDISP_HAVP_DOOR_OPEN = 13;
    POPUPDISP_HAVP_REAR_DOOR_OPEN = 14;
    POPUPDISP_HAVP_SAFETY_BELT_UNFASTENED = 15;
    POPUPDISP_HAVP_ENGINE_COVER_OPEN = 16;
    POPUPDISP_HAVP_RCTB_FCTB_ACTIVATION = 17;
    POPUPDISP_HAVP_AEB_ACTIVATION = 18;
    POPUPDISP_HAVP_TCS_ABS_ACTIVATION = 19;
    POPUPDISP_HAVP_ESP_ACTIVATION = 20;
    POPUPDISP_HAVP_HDC_ACTIVATION = 21;
    POPUPDISP_HAVP_TIRE_PRESSURE_IS_TOO_LOW = 22;
    POPUPDISP_HAVP_ILLUMINATION_CONDITIONS = 23;
    POPUPDISP_HAVP_RAINING_CONDITIONS = 24;
    POPUPDISP_CLICK_FINISH = 25;
    POPUPDISP_THE_LEARNING_ROUTE_IS_NOT_LOCATED = 26;
    POPUPDISP_PLEASE_SWITCH_TO_D_GEAR = 27;
    POPUPDISP_HAVP_PUSH_REQUEST_MAP_BUILDING = 28;
    POPUPDISP_HAVP_PUSH_REQUEST_ROUTE_REPLAY = 29;
    POPUPDISP_SVP_PUSH_REQUEST = 30;
    POPUPDISP_ACCELERATOR_PEDAL = 31;
    POPUPDISP_TAKE_OVER_IMMEDIATELY = 32;
    POPUPDISP_REQUEST_STOP = 33;
    POPUPDISP_SVP_REQUEST_CLOSE_ENGINE_HOOD = 34;
    POPUPDISP_SVP_REQUEST_CLOSE_TRUNK = 35;
    POPUPDISP_SVP_REQUEST_UNFOLD_REARVIEW_MIRROR = 36;
    POPUPDISP_SVP_REQUEST_CLOSE_DOOR = 37;
    POPUPDISP_SVP_REQUEST_FASTEN_SEATBELT = 38;
    POPUPDISP_SVP_RCTB_FCTB_ACTIVATION = 39;
    POPUPDISP_SVP_AEB_ACTIVATION = 40;
    POPUPDISP_SVP_TCS_ABS_ACTIVATION = 41;
    POPUPDISP_SVP_ESP_ACTIVATION = 42;
    POPUPDISP_SVP_HDC_ACTIVATION = 43;
    POPUPDISP_SVP_TIRE_PRESSURE_IS_TOO_LOW = 44;
    POPUPDISP_SVP_ILLUMINATION_CONDITIONS = 45;
    POPUPDISP_SVP_RAINING_CONDITIONS = 46;
    POPUPDISP_EXCESSIVE_SLOPE = 47;
    POPUPDISP_SVP_VEHICLE_RANGE_TOO_LOW = 48;
    POPUPDISP_SVP_CAMERA_BLOCKED = 49;
    POPUPDISP_SVP_THE_LOOP_CAMERA_FAULTY = 50;
    POPUPDISP_SVP_RADAR = 51;
    POPUPDISP_SVP_ASSOCIATED_SYSTEM_FAULTY = 52;
    POPUPDISP_SVP_SYSTEM_FAULTY = 53;
    POPUPDISP_NEED_UPDATE = 54;
    POPUPDISP_MAP_UPDATING = 55;
    POPUPDISP_MAP_UPDATE_SUCCESS = 56;
    POPUPDISP_MAP_UPDATE_FAIL = 57;
    POPUPDISP_SVP_TURN_ON_BACKGROUND_FUNCTIONS = 58;
    POPUPDISP_HAVP_DRIVING_MODE_NOT_SUPPORTED = 59;
    POPUPDISP_SVP_DRIVING_MODE_NOT_SUPPORTED = 60;
    POPUPDISP_HAVP_REARVIEW_MIRROR_FOLDED = 61;
    POPUPDISP_ADDTOFAVORITES = 62;
    POPUPDISP_HAVPPUSHREQUEST_MAPBACKSTAGEBUILDING = 63;
    POPUPDISP_HAVP_LIDAR_FAILURE = 64;
    POPUPDISP_HAVP_LIDAR_OBSTRUCTION = 65;
    POPUPDISP_OTHER_AUXILIARY_DRIVING_ACTIVATED = 66;
    POPUPDISP_SUSPENSION_MODE_NOT_SUPPORTED = 67;
    POPUPDISP_AVM_NOT_CALIBRATED = 68;
    POPUPDISP_VEHICLE_NOT_READY = 69;
  }
  PopupDisp popupdisp = 8;
  uint32 mapbuildprocbar = 9;
  enum StartPrkBtnDisp {
    STARTPRKBTNDISP_NO_DISPLAY = 0;
    STARTPRKBTNDISP_AVAILABLE = 1;
    STARTPRKBTNDISP_UNAVAILABLE = 2;
  }
  StartPrkBtnDisp startprkbtndisp = 10;
  enum UpdateRouteBtnDisp {
    UPDATEROUTEBTNDISP_NO_DISPLAY = 0;
    UPDATEROUTEBTNDISP_AVAILABLE = 1;
    UPDATEROUTEBTNDISP_UNAVAILABLE = 2;
  }
  UpdateRouteBtnDisp updateroutebtndisp = 11;
  enum GuidanceSts {
    GUIDANCESTS_NO_DISPLAY = 0;
    GUIDANCESTS_NO_COMPLETE = 1;
    GUIDANCESTS_COMPLETE1 = 2;
    GUIDANCESTS_COMPLETE2 = 3;
    GUIDANCESTS_COMPLETE3 = 4;
    GUIDANCESTS_COMPLETE4 = 5;
    GUIDANCESTS_COMPLETE5 = 6;
  }
  GuidanceSts guidancests = 12;
  uint32 actualmapflr = 13;
  int32 currleavingpoi = 14;
  enum ActualPrkgSlotTyp {
    ACTUALPRKGSLOTTYP_NO_SET = 0;
    ACTUALPRKGSLOTTYP_SET_PRKING_SLOT = 1;
    ACTUALPRKGSLOTTYP_SET_PRKING_SLOT_AREA = 2;
    ACTUALPRKGSLOTTYP_RANDOM = 3;
  }
  ActualPrkgSlotTyp actualprkgslottyp = 15;
  uint32 actualprkgslot = 16;
  uint32 actualprkgarea = 17;
  enum HAVPFunctTextDisp {
    HAVPFUNCTTEXTDISP_HAVPFUNCTNONE = 0;
    HAVPFUNCTTEXTDISP_START_PARKING_IN = 1;
    HAVPFUNCTTEXTDISP_ROUTE_LEARNING = 2;
    HAVPFUNCTTEXTDISP_PLEASE_SELECT_SLOT_USE_AUTOMATIC_PARKING_AFTER_BRAKING =
        3;
    HAVPFUNCTTEXTDISP_PLEASE_USE_AUTOMATIC_PARKING_AFTER_BRAKING = 4;
    HAVPFUNCTTEXTDISP_POUR_INTO_YOUR_PARKING_SLOT = 5;
    HAVPFUNCTTEXTDISP_SPEED_BUMP = 6;
    HAVPFUNCTTEXTDISP_EXCEEDS_DISTANCE = 7;
    HAVPFUNCTTEXTDISP_LEARNING_INTO_THE_RAMP = 8;
    HAVPFUNCTTEXTDISP_LEARNING_OFF_THE_RAMP = 9;
    HAVPFUNCTTEXTDISP_START_PARKING_OUT = 10;
    HAVPFUNCTTEXTDISP_ROUTE_LEARNING_PARKING_OUT = 11;
    HAVPFUNCTTEXTDISP_FOLLOW_INSTRUCTIONS = 12;
    HAVPFUNCTTEXTDISP_LEARNING_CAMERA_BLOCKED = 13;
    HAVPFUNCTTEXTDISP_LEARNING_THE_LOOP_CAMERA_FAULTY = 14;
    HAVPFUNCTTEXTDISP_LEARNING_RADAR_FAULTY = 15;
    HAVPFUNCTTEXTDISP_LEARNING_ASSOCIATED_SYSTEM_FAULTY = 16;
    HAVPFUNCTTEXTDISP_LEARNING_SYSTEM_FAULTY = 17;
    HAVPFUNCTTEXTDISP_LEARNING_ILLUMINATION_CONDITIONS = 18;
    HAVPFUNCTTEXTDISP_LEARNING_RAINING_CONDITIONS = 19;
    HAVPFUNCTTEXTDISP_LEARNING_ENVIRONMENT_TOO_EMPTY = 20;
    HAVPFUNCTTEXTDISP_ROUTE_REPEAT = 21;
    HAVPFUNCTTEXTDISP_EXCESSIVE_SLOPE = 22;
    HAVPFUNCTTEXTDISP_TIME_OUT = 23;
    HAVPFUNCTTEXTDISP_DISTANCE_TOO_LONG = 24;
    HAVPFUNCTTEXTDISP_SPEED_OVER_15_ = 25;
    HAVPFUNCTTEXTDISP_SPEED_TOO_HIGH = 26;
    HAVPFUNCTTEXTDISP_LEARNING_USER_EXIT = 27;
    HAVPFUNCTTEXTDISP_LEARNING_DOOR_OPEN = 28;
    HAVPFUNCTTEXTDISP_LEARNING_REAR_DOOR_OPEN = 29;
    HAVPFUNCTTEXTDISP_LEARNING_REARVIEW_MIRROR_FOLDED = 30;
    HAVPFUNCTTEXTDISP_LEARNING_LOOSEN_SEAT_BELT = 31;
    HAVPFUNCTTEXTDISP_BACKWARD = 32;
    HAVPFUNCTTEXTDISP_BACKWARD_EXIT = 33;
    HAVPFUNCTTEXTDISP_MAP_BUILDING = 34;
    HAVPFUNCTTEXTDISP_NOT_IN_SLOT = 35;
    HAVPFUNCTTEXTDISP_TAIL_IN_SLOT = 36;
    HAVPFUNCTTEXTDISP_MAP_BUILD_SUCCESS = 37;
    HAVPFUNCTTEXTDISP_MAP_BUILD_FAIL = 38;
    HAVPFUNCTTEXTDISP_INSUFFICIENT_STORAGE_SPACE = 39;
    HAVPFUNCTTEXTDISP_DISTANCE_TOO_SHORT = 40;
    HAVPFUNCTTEXTDISP_HAVP_READY_TO_START = 41;
    HAVPFUNCTTEXTDISP_KEEP_GOING = 42;
    HAVPFUNCTTEXTDISP_RELEASE_THE_BRAKES_AND_WE_SET_OFF = 43;
    HAVPFUNCTTEXTDISP_DRIVE_TO_YOUR_TERMINAL_SLOT = 44;
    HAVPFUNCTTEXTDISP_DRIVE_TO_YOUR_TERMINAL_EXIT = 45;
    HAVPFUNCTTEXTDISP_PARK_IN_CRUISING = 46;
    HAVPFUNCTTEXTDISP_TURN_LEFT = 47;
    HAVPFUNCTTEXTDISP_TURN_RIGHT = 48;
    HAVPFUNCTTEXTDISP_CROSSING = 49;
    HAVPFUNCTTEXTDISP_NARROW_ROAD = 50;
    HAVPFUNCTTEXTDISP_ROAD_DIFFICULT_HELP_ME_DRIVE = 51;
    HAVPFUNCTTEXTDISP_CRUSING_INTO_THE_RAMP = 52;
    HAVPFUNCTTEXTDISP_CRUSING_OUT_OF_THE_RAMP = 53;
    HAVPFUNCTTEXTDISP_WAIT_FOR_THE_CAR_AHEAD = 54;
    HAVPFUNCTTEXTDISP_GO_AROUND_THE_VEHICLE_AHEAD = 55;
    HAVPFUNCTTEXTDISP_THE_REAR_VEHICLE_FOLLOW = 56;
    HAVPFUNCTTEXTDISP_THE_REAR_VEHICLE = 57;
    HAVPFUNCTTEXTDISP_PEDESTRIAN = 58;
    HAVPFUNCTTEXTDISP_PEDESTRIAN_CROSSING = 59;
    HAVPFUNCTTEXTDISP_AVOID_NEARBY_OBSTACLES = 60;
    HAVPFUNCTTEXTDISP_ARRIVING_YOUR_SLOT = 61;
    HAVPFUNCTTEXTDISP_PARK_IN_PARKING = 62;
    HAVPFUNCTTEXTDISP_CRUSING_ENGINE_HOOD_OPEN = 63;
    HAVPFUNCTTEXTDISP_CRUSING_TRUNK_OPEN = 64;
    HAVPFUNCTTEXTDISP_CRUSING_DOOR_OPEN = 65;
    HAVPFUNCTTEXTDISP_CRUSING_REARVIEW_MIRROR_FOLD = 66;
    HAVPFUNCTTEXTDISP_CRUSING_SEAT_BELT_LOOSEN = 67;
    HAVPFUNCTTEXTDISP_DRIVE_NOT_INSIDE = 68;
    HAVPFUNCTTEXTDISP_TIMEOUT = 69;
    HAVPFUNCTTEXTDISP_STATISTIC_OBSTACLE_30S = 70;
    HAVPFUNCTTEXTDISP_TARGET_SLOT_OCCUPIED_PLOT_AVAILABLE_NEARBY = 71;
    HAVPFUNCTTEXTDISP_POSITIONING_UNSUCCESSFUL = 72;
    HAVPFUNCTTEXTDISP_CRUSING_ILLUMINATION_CONDITIONS = 73;
    HAVPFUNCTTEXTDISP_CRUSING_RAINING_CONDITIONS = 74;
    HAVPFUNCTTEXTDISP_CRUSING_LVP_FAILURE = 75;
    HAVPFUNCTTEXTDISP_CRUSING_ASSOCIATED_SYSTEM_FAILURE = 76;
    HAVPFUNCTTEXTDISP_CRUSING_TIME_OUT = 77;
    HAVPFUNCTTEXTDISP_PARKING_TIME_OUT = 78;
    HAVPFUNCTTEXTDISP_NUMBER_OF_PAUSES_EXCEEDED = 79;
    HAVPFUNCTTEXTDISP_VEHICLE_RANGE_TOO_LOW = 80;
    HAVPFUNCTTEXTDISP_CRUSING_RCTB_FCTB_ACTIVATION = 81;
    HAVPFUNCTTEXTDISP_CRUSING_AEB_ACTIVATION = 82;
    HAVPFUNCTTEXTDISP_CRUSING_ESP_ACTIVATION = 83;
    HAVPFUNCTTEXTDISP_CRUSING_TCS_ABS_ACTIVATION = 84;
    HAVPFUNCTTEXTDISP_CRUSING_HDC_ACTIVATION = 85;
    HAVPFUNCTTEXTDISP_TIRE_PRESSURE_IS_TOO_LOW = 86;
    HAVPFUNCTTEXTDISP_TARGET_SLOT_OCCUPIED_NO_SLOT_NEARBY = 87;
    HAVPFUNCTTEXTDISP_PARKINGIN_FAILURE = 88;
    HAVPFUNCTTEXTDISP_ROUTE_MATCHING_TIMEOUT = 89;
    HAVPFUNCTTEXTDISP_ACTIVATION_FAILED = 90;
    HAVPFUNCTTEXTDISP_CRUISING_CAMERA_BLOCKED = 91;
    HAVPFUNCTTEXTDISP_CRUISING_THE_LOOP_CAMERA_FAULTY = 92;
    HAVPFUNCTTEXTDISP_CRUISING_RADAR_FAULTY = 93;
    HAVPFUNCTTEXTDISP_SEARCH_SLOT_ALONG_WAY = 94;
    HAVPFUNCTTEXTDISP_BERTH_IN_COMPLETE = 95;
    HAVPFUNCTTEXTDISP_BERTH_OUT_COMPLETE = 96;
    HAVPFUNCTTEXTDISP_PULL_UP_EPB = 97;
    HAVPFUNCTTEXTDISP_GEAR_INTERVENE = 98;
    HAVPFUNCTTEXTDISP_STEERING_INTERVENE = 99;
    HAVPFUNCTTEXTDISP_BRAKE_INTERVENE = 100;
    HAVPFUNCTTEXTDISP_CRUSING_USER_EXIT = 101;
    HAVPFUNCTTEXTDISP_CRUSING_EXCESSIVE_SLOPE = 102;
    HAVPFUNCTTEXTDISP_CRUSING_SPEED_TOO_HIGH = 103;
    HAVPFUNCTTEXTDISP_VEHICLE_BACK = 104;
    HAVPFUNCTTEXTDISP_HANDS_OFF_THE_STEERING_WHEEL = 105;
    HAVPFUNCTTEXTDISP_DRIVING_MODE_NOT_SUPPORTED = 106;
    HAVPFUNCTTEXTDISP_PARKING_QUITPARKING_SLOT_BY_YOURSELF = 107;
    HAVPFUNCTTEXTDISP_GO_AROUND_NEARBY_OBSTACLES = 108;
    HAVPFUNCTTEXTDISP_GO_AROUND_THE_COMING_VEHICLE = 109;
    HAVPFUNCTTEXTDISP_RELEASE_THE_BRAKE_PARKING_START = 110;
    HAVPFUNCTTEXTDISP_AUTO_PARKING_BE_READY_TO_BRAKE = 111;
    HAVPFUNCTTEXTDISP_PAY_ATTENTION_TO_THE_RISK_OF_SCRATCHES = 112;
    HAVPFUNCTTEXTDISP_DETECTING_PARKING_SPACE = 113;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDEDCLOSE_TRUNK = 114;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_CLOSE_DOOR = 115;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_FASTEN_SEAT_BELT = 116;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_FOLD_THE_MIRROR = 117;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_CLOSE_CABIN_COVER = 118;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_OBSTACLES_DETECTED = 119;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDEDPEDESTRIAN_DETECTED = 120;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDEDVEHICLE_DETECTED_ = 121;
    HAVPFUNCTTEXTDISP_PARKING_CONTINUES = 122;
    HAVPFUNCTTEXTDISP_BRAKE_RECOVERED_CONFIRM_TO_CONTINUE = 123;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 124;
    HAVPFUNCTTEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_ACCELERATOR_PEDAL =
        125;
    HAVPFUNCTTEXTDISP_FINDING_SLOT_PLEASE_BRAKE = 126;
    HAVPFUNCTTEXTDISP_ENGINE_HOOD_OPEN = 127;
    HAVPFUNCTTEXTDISP_PLEASE_KEEP_BRAKING = 128;
    HAVPFUNCTTEXTDISP_MATCH_SUCCESSFUL_START_HAVP = 129;
    HAVPFUNCTTEXTDISP_GUIDANCE_START = 130;
    HAVPFUNCTTEXTDISP_GUIDANCE_SUCCESS = 131;
    HAVPFUNCTTEXTDISP_MAPFUSION = 132;
    HAVPFUNCTTEXTDISP_DELETESUCCESSFULLY = 133;
    HAVPFUNCTTEXTDISP_PLEASE_RELEASE_EPB = 134;
    HAVPFUNCTTEXTDISP_PLEASE_TURN_THE_WHEEL_BACK = 135;
    HAVPFUNCTTEXTDISP_OPERATION_FAILURE = 136;
    HAVPFUNCTTEXTDISP_TRYING_TO_PASS_BE_READY_TO_BRAKE = 137;
    HAVPFUNCTTEXTDISP_COLLECTION_SUCCESSFUL = 138;
    HAVPFUNCTTEXTDISP_MODIFY_SUCCESSFUL = 139;
    HAVPFUNCTTEXTDISP_LEARNING_VEHICLE_NOT_READY = 140;
    HAVPFUNCTTEXTDISP_CRUISING_VEHICLE_NOT_READY = 141;
    HAVPFUNCTTEXTDISP_LEARNING_LIDAR_FAILURE = 142;
    HAVPFUNCTTEXTDISP_CRUISING_LIDAR_FAILURE = 143;
    HAVPFUNCTTEXTDISP_LEARNING_LIDAR_OBSTRUCTION = 144;
    HAVPFUNCTTEXTDISP_CRUISING_LIDAR_OBSTRUCTION = 145;
    HAVPFUNCTTEXTDISP_DISTANCE_TOO_LONG_MAP_TO_BE_SAVED = 146;
    HAVPFUNCTTEXTDISP_PASS_GATE = 147;
    HAVPFUNCTTEXTDISP_PASS_GATE_BY_YOURSELF = 148;
    HAVPFUNCTTEXTDISP_PLEASE_EXIT_THE_PARKING_SPACE = 149;
    HAVPFUNCTTEXTDISP_TRAJECTORY_PLANNING_FAILED = 150;
    HAVPFUNCTTEXTDISP_PARKING_QUIT_AUTO_SAVE_MAP = 151;
    HAVPFUNCTTEXTDISP_DISTANCE_TOO_LONG_AUTO_SAVE_MAP = 152;
    HAVPFUNCTTEXTDISP_ROUTE_UPDATED = 153;
    HAVPFUNCTTEXTDISP_MAP_AUTO_EXTENDED_TO_PARKING_ENTRANCE = 154;
    HAVPFUNCTTEXTDISP_NAVIGATION_COMPLETE = 155;
    HAVPFUNCTTEXTDISP_OFF_COURSE_ROUTE_UPDATED = 156;
    HAVPFUNCTTEXTDISP_PLEASE_SELECT_SUITABLE_PARKING_SLOT_ICON = 157;
    HAVPFUNCTTEXTDISP_PLEASE_FOLLOW_NAVIGATION_TIPS_TURN_ON_FUNCTION = 158;
  }
  HAVPFunctTextDisp havpfuncttextdisp = 18;
  enum SVPFunctTextDisp {
    SVPFUNCTTEXTDISP_SVPFUNCTNONE = 0;
    SVPFUNCTTEXTDISP_PLEASE_CONFIRM_FINAL_POSITION_CLICK_START = 1;
    SVPFUNCTTEXTDISP_RELEASE_THE_BRAKES_AND_WE_SET_OFF = 2;
    SVPFUNCTTEXTDISP_DRIVE_TO_YOUR_TERMINAL_SLOT = 3;
    SVPFUNCTTEXTDISP_DRIVE_TO_YOUR_TERMINAL_EXIT = 4;
    SVPFUNCTTEXTDISP_PARK_IN_CRUISING = 5;
    SVPFUNCTTEXTDISP_TURN_LEFT = 6;
    SVPFUNCTTEXTDISP_TURN_RIGHT = 7;
    SVPFUNCTTEXTDISP_CROSSING = 8;
    SVPFUNCTTEXTDISP_NARROW_ROAD = 9;
    SVPFUNCTTEXTDISP_ROAD_DIFFICULT_HELP_ME_DRIVE = 10;
    SVPFUNCTTEXTDISP_INTO_THE_RAMP = 11;
    SVPFUNCTTEXTDISP_OUT_OF_THE_RAMP = 12;
    SVPFUNCTTEXTDISP_WAIT_FOR_THE_CAR_AHEAD = 13;
    SVPFUNCTTEXTDISP_GO_AROUND_THE_VEHICLE_AHEAD = 14;
    SVPFUNCTTEXTDISP_THE_REAR_VEHICLE_FOLLOW = 15;
    SVPFUNCTTEXTDISP_THE_REAR_VEHICLE = 16;
    SVPFUNCTTEXTDISP_PEDESTRIAN = 17;
    SVPFUNCTTEXTDISP_PEDESTRIAN_CROSSING = 18;
    SVPFUNCTTEXTDISP_AVOID_NEARBY_OBSTACLES = 19;
    SVPFUNCTTEXTDISP_ARRIVING_YOUR_SLOT = 20;
    SVPFUNCTTEXTDISP_PARK_IN_PARKING = 21;
    SVPFUNCTTEXTDISP_ENGINE_HOOD_OPEN = 22;
    SVPFUNCTTEXTDISP_TRUNK_OPEN = 23;
    SVPFUNCTTEXTDISP_DOOR_OPEN = 24;
    SVPFUNCTTEXTDISP_REARVIEW_MIRROR_FOLD = 25;
    SVPFUNCTTEXTDISP_SEAT_BELT_LOOSEN = 26;
    SVPFUNCTTEXTDISP_DRIVE_NOT_INSIDE = 27;
    SVPFUNCTTEXTDISP_TIMEOUT = 28;
    SVPFUNCTTEXTDISP_STATISTIC_OBSTACLE_30S = 29;
    SVPFUNCTTEXTDISP_TARGET_SLOT_OCCUPIED_PLOT_AVAILABLE_NEARBY = 30;
    SVPFUNCTTEXTDISP_POSITIONING_UNSUCCESSFUL = 31;
    SVPFUNCTTEXTDISP_ILLUMINATION_CONDITIONS = 32;
    SVPFUNCTTEXTDISP_RAINING_CONDITIONS = 33;
    SVPFUNCTTEXTDISP_SVP_FAILURE = 34;
    SVPFUNCTTEXTDISP_ASSOCIATED_SYSTEM_FAILURE = 35;
    SVPFUNCTTEXTDISP_CRUSING_TIME_OUT = 36;
    SVPFUNCTTEXTDISP_PARKING_TIME_OUT = 37;
    SVPFUNCTTEXTDISP_NUMBER_OF_PAUSES_EXCEEDED = 38;
    SVPFUNCTTEXTDISP_VEHICLE_RANGE_TOO_LOW = 39;
    SVPFUNCTTEXTDISP_RCTB_FCTB_ACTIVATION = 40;
    SVPFUNCTTEXTDISP_AEB_ACTIVATION = 41;
    SVPFUNCTTEXTDISP_ESP_ACTIVATION = 42;
    SVPFUNCTTEXTDISP_TCS_ABS_ACTIVATION = 43;
    SVPFUNCTTEXTDISP_HDC_ACTIVATION = 44;
    SVPFUNCTTEXTDISP_TIRE_PRESSURE_IS_TOO_LOW = 45;
    SVPFUNCTTEXTDISP_TARGET_SLOT_OCCUPIED_NO_SLOT_NEARBY = 46;
    SVPFUNCTTEXTDISP_PHONE_DISCONNECTED = 47;
    SVPFUNCTTEXTDISP_VIDEO_MONITOR_ABNORMAL = 48;
    SVPFUNCTTEXTDISP_REMOTE_DEVICE_ERROR = 49;
    SVPFUNCTTEXTDISP_OBSTACLE_TIMEOUT = 50;
    SVPFUNCTTEXTDISP_PARKINGIN_FAILURE = 51;
    SVPFUNCTTEXTDISP_ROUTE_MATCHING_TIMEOUT = 52;
    SVPFUNCTTEXTDISP_ACTIVATION_FAILED = 53;
    SVPFUNCTTEXTDISP_CAMERA_BLOCKED = 54;
    SVPFUNCTTEXTDISP_THE_LOOP_CAMERA_FAULTY = 55;
    SVPFUNCTTEXTDISP_RADAR_FAULTY = 56;
    SVPFUNCTTEXTDISP_BERTH_IN_COMPLETE = 57;
    SVPFUNCTTEXTDISP_BERTH_OUT_COMPLETE = 58;
    SVPFUNCTTEXTDISP_PULL_UP_EPB = 59;
    SVPFUNCTTEXTDISP_GEAR_INTERVENE = 60;
    SVPFUNCTTEXTDISP_STEERING_INTERVENE = 61;
    SVPFUNCTTEXTDISP_BRAKE_INTERVENE = 62;
    SVPFUNCTTEXTDISP_CRUSING_USER_EXIT = 63;
    SVPFUNCTTEXTDISP_CRUSING_EXCESSIVE_SLOPE = 64;
    SVPFUNCTTEXTDISP_VEHICLE_BACK = 65;
    SVPFUNCTTEXTDISP_HANDS_OFF_THE_STEERING_WHEEL = 66;
    SVPFUNCTTEXTDISP_DRIVING_MODE_NOT_SUPPORTED = 67;
    SVPFUNCTTEXTDISP_GO_AROUND_NEARBY_OBSTACLES = 68;
    SVPFUNCTTEXTDISP_GO_AROUND_THE_COMING_VEHICLE = 69;
    SVPFUNCTTEXTDISP_RELEASE_THE_BRAKE_PARKING_START = 70;
    SVPFUNCTTEXTDISP_AUTO_PARKING_BE_READY_TO_BRAKE = 71;
    SVPFUNCTTEXTDISP_PAY_ATTENTION_TO_THE_RISK_OF_SCRATCHES = 72;
    SVPFUNCTTEXTDISP_DETECTING_PARKING_SPACE = 73;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDEDCLOSE_TRUNK = 74;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_CLOSE_DOOR = 75;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_FASTEN_SEAT_BELT = 76;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_FOLD_THE_MIRROR = 77;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_CLOSE_CABIN_COVER = 78;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_OBSTACLES_DETECTED = 79;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDEDPEDESTRIAN_DETECTED = 80;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDEDVEHICLE_DETECTED_ = 81;
    SVPFUNCTTEXTDISP_PARKING_CONTINUES = 82;
    SVPFUNCTTEXTDISP_BRAKE_RECOVERED_CONFIRM_TO_CONTINUE = 83;
    SVPFUNCTTEXTDISP_CRUSING_SPEED_TOO_HIGH = 84;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL_CRUSING_SPEED_TOO_HIGH =
        85;
    SVPFUNCTTEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_ACCELERATOR_PEDAL =
        86;
    SVPFUNCTTEXTDISP_PLEASE_KEEP_BRAKING = 87;
  }
  SVPFunctTextDisp svpfuncttextdisp = 19;
  enum BtnEnaAck {
    BTNENAACK_NO_RESPONSE = 0;
    BTNENAACK_RESPONSE = 1;
  }
  BtnEnaAck btnenaack = 20;
  enum HAVPfunctionSts {
    HAVPFUNCTIONSTS_OFF = 0;
    HAVPFUNCTIONSTS_ON = 1;
  }
  HAVPfunctionSts havpfunctionsts = 21;
  enum AutoPushSts {
    AUTOPUSHSTS_OFF = 0;
    AUTOPUSHSTS_ON = 1;
  }
  AutoPushSts autopushsts = 22;
  enum SVPfunctionSts {
    SVPFUNCTIONSTS_OFF = 0;
    SVPFUNCTIONSTS_ON = 1;
  }
  SVPfunctionSts svpfunctionsts = 23;
}

// topic: /gwm/someip_adapter/slot_id_report
message SlotIDReport {
  string id = 1;
}