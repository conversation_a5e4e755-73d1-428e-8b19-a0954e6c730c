package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "service_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["service_state.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "service_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":service_state_proto",
    ],
)

python_proto_library(
    name = "service_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":service_state_proto",
    ],
)

proto_library(
    name = "side_bsd_disp_proto",
    visibility = ["//visibility:public"],
    srcs = ["side_bsd_disp.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "side_bsd_disp_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":side_bsd_disp_proto",
    ],
)

python_proto_library(
    name = "side_bsd_disp_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":side_bsd_disp_proto",
    ],
)

proto_library(
    name = "avp_proto",
    visibility = ["//visibility:public"],
    srcs = ["avp.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "avp_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":avp_proto",
    ],
)

python_proto_library(
    name = "avp_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":avp_proto",
    ],
)

proto_library(
    name = "power_manager_proto",
    visibility = ["//visibility:public"],
    srcs = ["power_manager.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "power_manager_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":power_manager_proto",
    ],
)

python_proto_library(
    name = "power_manager_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":power_manager_proto",
    ],
)

proto_library(
    name = "sdroute_resp_proto",
    visibility = ["//visibility:public"],
    srcs = ["sdroute_resp.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "sdroute_resp_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":sdroute_resp_proto",
    ],
)

python_proto_library(
    name = "sdroute_resp_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":sdroute_resp_proto",
    ],
)

proto_library(
    name = "hut_notify_proto",
    visibility = ["//visibility:public"],
    srcs = ["hut_notify.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "hut_notify_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":hut_notify_proto",
    ],
)

python_proto_library(
    name = "hut_notify_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":hut_notify_proto",
    ],
)
