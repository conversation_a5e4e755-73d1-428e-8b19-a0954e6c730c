package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "graph_match_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["graph_match_event.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "graph_match_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":graph_match_event_proto",
    ],
)

python_proto_library(
    name = "graph_match_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":graph_match_event_proto",
    ],
)

proto_library(
    name = "graph_matching_proto",
    visibility = ["//visibility:public"],
    srcs = ["graph_matching.proto"],
    deps = [
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "graph_matching_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":graph_matching_proto",
    ],
)

python_proto_library(
    name = "graph_matching_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":graph_matching_proto",
    ],
)

