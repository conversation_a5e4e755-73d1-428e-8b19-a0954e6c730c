package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "rviz_marker_proto",
    visibility = ["//visibility:public"],
    srcs = ["rviz_marker.proto"],
    deps = [
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "rviz_marker_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":rviz_marker_proto",
    ],
)

python_proto_library(
    name = "rviz_marker_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":rviz_marker_proto",
    ],
)

proto_library(
    name = "visualizer_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["visualizer_command.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "visualizer_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":visualizer_command_proto",
    ],
)

python_proto_library(
    name = "visualizer_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":visualizer_command_proto",
    ],
)

