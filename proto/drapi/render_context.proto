syntax = "proto3";

import "perception/deeproute_perception_ras_map.proto";
import "drapi/business_info.proto";
import "drapi/notification.proto";

package dr.hmi;

message CriticalObject {
  int32 id = 1;
  enum ObjectType {
    GREEN_ALARM_OBJ = 0;
    YELLOW_ALARM_OBJ = 1;
    RED_ALARM_OBJ = 2;
  }
  ObjectType object_type = 2;
  float distance = 3;
}

enum StatusDisplay {
  STATUS_NO_DISPLAY = 0;
  STATUS_YES = 1;
  STATUS_NO = 2;
}

message DrivingRenderContext {
  repeated CriticalObject critical_objects = 1;
  dr.business_info.FollowTimeGap time_gap = 2;  // 当前时距档位
  StatusDisplay sla_status = 3;                 // 融合限速状态
  dr.notification.NCAOutODD out_odd = 4;        // odd信息
  uint32 target_speed_limit = 5;                // 目标限速
}

message ParkingSpaceMapping {
  message MappingData {
    int32 number = 1;
    int32 parkingspace_id = 2;
  }
  repeated MappingData mappings = 1;
}

message SearchSuspendInfo {
  enum SearchSuspendType {
    TYPE_NONE = 0;
    TYPE_NORMAL = 1;  //车速超限、安全带：正常释放车位
    TYPE_SELECTABLE = 2;  //五门一盖、后视镜：不释放新车位，原可泊车位可选
    TYPE_UNSELECTABLE = 3;  //坡度超限：不释放新车位，原可泊车位不可选
  }
  bool search_suspend = 1;
  SearchSuspendType suspend_type = 2;
}

message ParkingRenderContext {
  enum ParkingActiveMode {
    UNKNOWN = 0;
    PARKING_IN = 1;
    PARKING_OUT = 2;
  };
  enum APAOutParkingDirection {
    DIRECTION_UNKNOWN = 0;
    PARELLEL_LEFT = 1;   // 水平车位左
    PARELLEL_RIGHT = 2;  // 水平车位右
    FRONT_LEFT = 3;      // 垂直、斜列左前
    FRONT_RIGHT = 4;     // 垂直、斜列右前
    BACK_LEFT = 5;       // 垂直、斜列后左
    BACK_RIGHT = 6;      // 垂直、斜列后右
    STRAIGHT_FRONT = 7;  // 垂直、斜列前
    STRAIGHT_BACK = 8;   // 垂直、斜列后
  }
  enum PrkModType {
    PRKMOD_NO_ACTION = 0;
    PRKMOD_HEAD_PARKING_IN = 1;
    PRKMOD_TAIL_PARKING_IN = 2;
    PRKMOD_RESERVED_3 = 3;
  }
  enum HAVPNaviTips {           //提示内容以UE为准，以下备注仅为提示大意
    HAVP_TIP_NONE               =0; //无文言 
    HAVP_TIP_PARKING_OUT        =1; //驶出车位  
    HAVP_TIP_ARRIVE_DEST        =2; //前方到达目标车位  
    HAVP_TIP_TURN_LEFT          =3; //前方左转 
    HAVP_TIP_TURN_RIGHT         =4; //前方右转 
    HAVP_TIP_TURN_AROUND        =5; //请掉头
    HAVP_TIP_UPHILL             =6; //即将上坡
    HAVP_TIP_DOWNHILL           =7; //即将下坡
    HAVP_TIP_ENTER_PARKING_LOT  =8; //即将驶入地库
    HAVP_TIP_EXIT_PARKING_LOT   =9; //即将驶离地库
    HAVP_TIP_RETURN_ROAD        =10; //请行驶至道路 
  }
  int64 timestamp = 1;
  ParkingSpaceMapping parkingspace_mapping = 2;
  bool background_search = 3;  // 是否后台搜车位，不是情况下仪表不释放车位
  int32 target_parkingspace_id = 4;  // 巡航目标车位
  ParkingActiveMode parking_active_mode = 5;
  string parkin_trajectory_id = 6;  // 全局轨迹线path匹配
  bool search_suspend = 7;  // 是否搜车位暂停，暂停情况下不释放车位 （后续弃用）
  bool is_dir_and_gear_not_consistent_ =
      8;  // 泊出方向是否与档位不一致，开启泊出时判断
  APAOutParkingDirection apa_out_parking_direction = 9;
  bool is_routing_end_target_slot_occupied_ =
      10;  //巡航终点目标车位是否被占用，用于巡航终点是否显示车位编号判断
  SearchSuspendInfo search_suspend_info = 11;  //只在搜车位暂停时发送
  bool is_map_saving_done =
      12;  // 是否完成地图保存，此时状态1s后才到 learning_completed
  bool is_apa_park_ready = 13;
  float mapping_save_progress = 14; // 地图保存进度（1~100）
  deeproute.perception.ParkingSpace parking_space = 15;  // 目标车位缓存信息

  int32 routing_target_parkingspace_id = 16; // vpa 导航、巡航到终点的目标车位ID 

  enum HUTVPAPage {
    PAGE_NONE = 0;
    MAP_MANAGE = 1;
  }
  HUTVPAPage hut_vpa_page = 17;
  PrkModType park_in_type = 18;
  HAVPNaviTips havp_navi_tips = 19;
}

message TSISign {
  enum SignType {
    UNKONW_SIGN = 0;
    SIGN_MAX_SPEED = 1;
    SIGN_MIN_SPEED = 2;
    SIGN_NON_SPEED = 3;
    SIGN_WARN_KID = 4;
    SIGN_BAN_ENTER = 5;
    SIGN_WARN_CONSTRUCTION = 6;
    SIGN_WARN_LEFT_SHARP_TURN = 7;
    SIGN_WARN_RIGHT_SHARP_TURN = 8;
    SIGN_WARN_MORE_TURN = 9;
    SIGN_UNBAN_OVERTAKE = 10;
  }
  SignType sign_type = 1;
  int32 limit_value = 2;
  int32 id = 3;
}

message ActiveSafetyWarningObstacle {
  uint32 id = 1;
  enum Type {
    UNKONW_FEATURE = 0;
    DW_ACTIVE = 1;
    FCW_ACTIVE = 2;
    AEB_ACTIVE = 3;
    FCTB_ACTIVE = 4;
    RCTB_ACTIVE = 5;
    ESA_ACTIVE = 6;
  }
  Type active_feature = 2;
}

message ActiveSafetyRenderContext {
  repeated ActiveSafetyWarningObstacle obstacles = 1;
  repeated TSISign tsi_sign = 2;
}

message GWMRenderContext {
  DrivingRenderContext driving_render_data = 1;
  ParkingRenderContext parking_render_data = 2;
}

message GLRenderContext {
  ActiveSafetyRenderContext active_safety_render_data = 1;
  DrivingRenderContext driving_render_data = 2;
  ParkingRenderContext parking_render_data = 3;
}

// topic: /blc/render_context
message RenderContext {
  oneof type {
    GWMRenderContext gwm_data = 1;
    GLRenderContext gl_data = 2;
  }
}

// topic: /dta/data_convert
message DataConvert {
  oneof type {
    GWMConvertTask gwm_task = 1;
  }
}
message GWMConvertTask {
  bytes havp_map_manage_map_data_convert = 1; 
}

