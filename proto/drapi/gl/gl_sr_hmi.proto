syntax = "proto3";
package gl.sr;
import "drapi/gl/gl_sr_common.proto";

// topic: /gl/sr_vehicle
message SelfVIement {
  SelfPosn self_posn = 1;  //  自车坐标
  SelfHeadingAg self_heading_ag = 2;  //  自车姿态坐标
  DisplayDynamicEffect display_dynamic_effect = 3;  //  显示动态效果
  SelfVisualAngle self_visual_angle = 4;  //  自车显示视角
  int32 floor = 5;  //  当前楼层高度（单位: 楼层数）
  repeated Point traceline_dot = 6;  //  轨迹线打点（动态数组）
  LaneElement lane_element = 7;  //  车道元素标识（动态数组）
  MeyPrkgSpe mey_prkg_spe = 8;  //  目标车位信息
  DestPosn dest_posn = 9;  //  目的地坐标信号
  EthMiniADASObj eth_mini_adas_obj = 10;  //  前一目标信息
  repeated EthHmiTrfcSign eth_hmi_trfc_sign = 11;  //  立体交通标志牌（动态数组）
  repeated EthHmiTrfcLight eth_hmi_trfc_light = 12;  //  立体交通灯（动态数组）
  ClassicalChinese classical_chinese = 13;  //  还原世界文言
  FunctionStsLight function_sts_light = 14;  //  状态灯图标状态
  EthForHumaMach eth_for_huma_mach = 15;  //  人车共驾
  EthHWALeadingPoint eth_hwa_leading_point = 16;  //  落位框坐标
  EthADCtrlMode eth_ad_ctrl_mode = 17;  //  表征当前是HWA还是NOA
  uint32 eth_arhud_vie_wsts = 18;  //  ARHUD规划线状态
  Other other = 19;  //  主题、时间戳
}

// topic: /gl/sr_obstacle
message PerceptualObstacleArr {
  repeated PerceptualObstacle obstacles = 1;
}

// topic: /gl/sr_perception_pk
message PerceptualSlotArr {
  repeated PerceptualSlot parkingspace = 1;
}

// topic: /gl/right_lanemaking
message PrkgLaneMarkingRight {
  repeated PrkgLaneMarking lane_marks = 1;
}

// topic: /gl/left_lanemaking
message PrkgLaneMarkingLeft {
  repeated PrkgLaneMarking lane_marks = 1;
}

// topic: /gl/right_tracepoint
message PrkgTraceDotRight {
  repeated PrkgTraceDot trace_dots = 1;
}

// topic: /gl/left_tracepoint
message PrkgTraceDotLeft {
  repeated PrkgTraceDot trace_dots = 1;
}