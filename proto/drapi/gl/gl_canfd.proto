syntax = "proto2";

package gl.canfd;

import "canbus/gl_p177_chassis_detail.proto";

// topic: /gl/dta_to_canbus
message DtaToCanbus {
  optional deeproute.canbus.gl.p177.ADCU_ZCUCANFD1_FR10_1B5 adcu_fr10_1b5 = 1; // acc跟车相关
  optional deeproute.canbus.gl.p177.ADCU_ZCUCANFD1_FR04_1A0 adcu_fr04_1a0 = 2; // 主题
  optional deeproute.canbus.gl.p177.ADCU_ZCUCANFD1_FR02_1F0 adcu_fr02_1f0 = 3; // 线属性(除右车道线类型)
  optional deeproute.canbus.gl.p177.ADCU_ZCUCANFD1_FR06_1FF adcu_fr06_1ff = 4; // 线属性(右车道线类型)
  optional deeproute.canbus.gl.p177.ADCU_ZCUCANFD1_FR11_1E0 adcu_fr11_1e0 = 5; // 底纹颜色
}