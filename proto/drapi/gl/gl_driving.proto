syntax = "proto3";
package gl.driving;
import "drapi/gl/gl_driving_common.proto";

message DrivingVehicleRender {
  repeated Obstacle obstacle = 1; // 障碍物
  PlanningLine planning_line = 2; // path规划线
  EthHWALeadingPoint hwa_leading_point = 3; // TODO: lcc变道落位点
  EthAsyLanChgColorDsp lane_change_color = 4; // 变道框颜色
  EthSlowDownSts slow_down_status = 5; // 减速状态
  EthStopPoint stop_point = 6; // 停止线
  EthNarrow narrow = 7; // 窄道通行
  EthCrossPlanningLine cross_planning_line = 8; // 路口规划线
}

message DrivingVehicleAdditionInfo {
  EthHmiCtl hmi_ctl = 1; // 时间戳
  EthADCtrlMode adc_ctrl_mode = 2; // 控车模式: lcc nca other
  EthAsyALgtIndcr lgt_indcr = 3; // 纵向指示灯
  EthAsyALgtSts lgt_status = 4; // 纵向状态机
  EthAsyALatIndcr lat_indcr = 5; // 横向指示灯
  EthDispTSIInfoForLgtCtrl tsi_info_for_lgt_ctrl = 6; // 限速融合图标
  EthDispSpdSetForLgtCtrl speed_set_for_lgt_ctrl = 7; // 车速图标
  EthAsyLatOffstSts lat_offset_status = 8; // lcc横向偏移
  EthOffsEndOfODD end_of_odd = 9; // ODD终点
}

message DrivingCSDMessage {
  EthAsyLanChgMesg lane_change_msg = 1; // 变道文言
  EthAsyInformForDrvr inform_for_drvr_msg = 2; // 驾驶员提示
  EthAsyNoaDistanMsg noa_distance_msg = 3; // noa终点提示
  EthLcmaIndcnLe lcma_left_warning = 4; // LCMA左侧预警
  EthLcmaIndcnRi lcma_right_warning = 5; // LCMA右侧预警
  EthFctaIndcnLe fcta_left_warning = 6; // FCTA左侧预警
  EthFctaIndcnRi fcta_right_warning = 7; // FCTA右侧预警
  EthCllsnWarnReIndcn rcw_warning = 8; // RCW预警
  EthDrvOffWarn drv_off_warning = 9; // 前方车辆已驶离
  EthAsyLgtCtrlTakeOverReq lgt_ctrl_takeover = 10; // 前车过近\n 请接管车辆
  EthOvrdWrnForFstActv ovrd_for_first_active = 11; // 驾驶员主动加速中\n 巡航无法提供减速
  EthCnclWarnLgtForAutDrv cncl_lgt_for_auto_drv = 12; // 自适应巡航已退出
  EthAvlStsForLongAutDrv avl_sts_for_long_aut_drv = 13; // 无法激活系列文言提示
  EthAsySteerApplyRqrd steer_apply_rqrd = 14; // 方向盘接管提示
  EthAsyWarnForSteerCncl steer_apply_rqrd_steer = 15; // 车道保持辅助已退出
  EthWarnForAsyEmgyLaneKeepAid emgy_lane_keep_aid = 16; // 紧急车道保持已激活
  EthCllsnAidPostEMA aid_post_ema = 17; // 紧急转向辅助已激活
  EthRcwmBrkReq rcwm_brk_req = 18; // 后方有碰撞风险\n已紧急制动
  EthCllsnFwdWarn cllsn_fwd_warn = 19; // 请保持安全车距
  EthCllsnAidPost rcwm_aid_post = 20; // 前方有碰撞风险\n已为你减缓碰撞
  EthDoorOpenwarnLeIndcn door_open_warn_le = 21; // 开门请注意侧后方
  EthDoorOpenwarnRiIndcn door_open_warn_ri = 22; // 开门请注意侧后方
  EthAsySafeStopSts safe_stop_sts = 23; // 请立即接管
  EthAsyEyesOffWarnRqrd eyes_off_warn = 24; // 请注视前方道路
  EthAsyNoaRemindMesg noa_remind_msg = 25; // noa提示
  EthAsyNoaTakeoverMesg noa_tk = 26; // // noa提示
  EthAsyNoaDeactvdReasnMesg noa_deactvd_reasn = 27; // noa无法激活原因
  EthAsyAutDrvgAvl auti_drv_avl = 28; // 重试
  EthAsyNoaActvSuggest noa_act_sug = 29; // 智能巡航辅助可以激活
  EthCnclWarnForAutDrv cncl_for_auto_drv = 30; // 智能巡航辅助已退出
  EthAsyLatDegSts lat_deg_sts = 31; // 车道居中辅助已暂停\n请控制方向
  EthAsyIccMaxWarnMsg icc_max_warn = 32; // 避让等
  EthAsyCnoaCrsActvMsg cnoa_act = 33; // cnoa激活提示
  EthAsyCnoaCrsActvFaildMsg cnoa_act_faild = 34; // cnoa激活失败提示
  EthAsyCnoaCrsDeactvdMsg cnoa_deatcd = 35; // cnoa激活异常提示
  EthAsyCNoaCrsLanChgMsg cnoa_lane_chg = 36; // cnoa行进过程中的提示
  EthAsyCnoaCrsOffsMsg cnoa_crs_off = 37; // cnoa行进过程中的避让提示
  EthAsyCnoaTakeoverWarnMsg cnoa_tk = 38; // cnoa接管提示
  EthAsyCnoaCrsCrossIntsecMsg cnoa_crs_cross = 39; 
  EthAsyCnoaCrsRmnMsg cnoa_crs_rmn = 40;
}

message DrivingMapRender {
  repeated StopLine stop_line = 1;           // 停止线
  repeated RoadArrow road_arrow = 2;         // 道路箭头
  repeated SpeedBump speed_bump = 3;         // 减速带
  repeated CrossWalk cross_walk = 4;         // 斑马线
  repeated LaneEdge lane_edge = 5;           // 路沿线
  repeated TrafficLight traffic_light = 6;   // 交通灯
  repeated TrafficSign traffic_sign = 7;     // 交通标识牌
  HmiBoundary hmi_boundary = 8;   // 路线
  repeated DrivingArea driving_area = 9;     // 行驶区域
}

message DrivingMapAdditionalInfo {
  SelfLane self_lane = 1;                     // 自车所在车道底纹
  ADTheme ad_theme = 2;                       // 显示主题
  MiniAdasObject mini_adas_obj = 3;           // 自车前方第一个物体的属性
  EthDoorOpenWarnRiIndcn dow_ri = 4;          // DOW（开门预警）右侧预警
  EthDoorOpenWarnLeIndcn dow_le = 5;          // DOW（开门预警）左侧预警
  EthTrfcLiChg traffic_light_change = 6;      // 交通灯变化提示
  EthTiGapSetForLgtCtrl time_gap = 7;         // 时距调节设置
}

// topic: /gl/driving_vehicle
message DrivingVehicle {
  uint64 timestamp = 1; // for debug
  DrivingVehicleRender render_data = 2;
  DrivingVehicleAdditionInfo additional_info = 3;
  DrivingCSDMessage csd_message = 4;
}

// topic: /gl/driving_map
message DrivingMap {
  uint64 timestamp = 1; // for debug
  DrivingMapRender render_data = 2;
  DrivingMapAdditionalInfo additional_info = 3;
}