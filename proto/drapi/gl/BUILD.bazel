package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "gl_canfd_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_canfd.proto"],
    deps = [
        "//proto/canbus:gl_p177_chassis_detail_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_canfd_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_canfd_proto",
    ],
)

python_proto_library(
    name = "gl_canfd_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_canfd_proto",
    ],
)

proto_library(
    name = "gl_driving_common_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_driving_common.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_driving_common_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_driving_common_proto",
    ],
)

python_proto_library(
    name = "gl_driving_common_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_driving_common_proto",
    ],
)

proto_library(
    name = "gl_driving_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_driving.proto"],
    deps = [
      "//proto/drapi/gl:gl_driving_common_proto"
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_driving_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_driving_proto",
    ],
)

python_proto_library(
    name = "gl_driving_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_driving_proto",
    ],
)

proto_library(
    name = "gl_sr_common_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_sr_common.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_sr_common_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_sr_common_proto",
    ],
)

python_proto_library(
    name = "gl_sr_common_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_sr_common_proto",
    ],
)

proto_library(
    name = "gl_sr_hmi_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_sr_hmi.proto"],
    deps = [
      "//proto/drapi/gl:gl_sr_common_proto"
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_sr_hmi_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_sr_hmi_proto",
    ],
)

python_proto_library(
    name = "gl_sr_hmi_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_sr_hmi_proto",
    ],
)

