syntax = "proto2";
package dr.blc.mcu;

import "canbus/gwm_p03_chassis_detail.proto";

message GwmP03DownstreamChassis {
  optional deeproute.canbus.gwm.p03.HAP_FD1_15B hap_fd1_15b = 1;
  optional deeproute.canbus.gwm.p03.HAP_FD2_274 hap_fd2_274 = 2;
  optional deeproute.canbus.gwm.p03.HAP_FD6_289 hap_fd6_289 = 3;
  optional deeproute.canbus.gwm.p03.HAP_FD3_298 hap_fd3_298 = 4;
  optional deeproute.canbus.gwm.p03.HAP_FD7_29B hap_fd7_29b = 5;

  optional deeproute.canbus.gwm.p03.ACC_FD1_143 acc_fd1_143 = 6;
  optional deeproute.canbus.gwm.p03.ACC_FD2_2AB acc_fd2_2ab = 7;
  optional deeproute.canbus.gwm.p03.ACC_FD3_2B4 acc_fd3_2b4 = 8;
  optional deeproute.canbus.gwm.p03.ACC_FD4_2B8 acc_fd4_2b8 = 9;

  optional deeproute.canbus.gwm.p03.IFC_FD1_12B ifc_fd1_12b = 10;
  optional deeproute.canbus.gwm.p03.IFC_FD5_19F ifc_fd5_19f = 11;
  optional deeproute.canbus.gwm.p03.IFC_FD6_222 ifc_fd6_222 = 12;
  //optional deeproute.canbus.gwm.p03.IFC_FD2_23D ifc_fd2_23d = 13;
  //optional deeproute.canbus.gwm.p03.IFC_FD4_240 ifc_fd4_240 = 14;
  optional deeproute.canbus.gwm.p03.IFC_FD7_2A2 ifc_fd7_2a2 = 15;
  optional deeproute.canbus.gwm.p03.IFC_FD3_2CF ifc_fd3_2cf = 16;

  optional deeproute.canbus.gwm.p03.AEB_FD1_18B aeb_fd1_18b = 17;
  optional deeproute.canbus.gwm.p03.AEB_FD2_227 aeb_fd2_227 = 18;

  optional deeproute.canbus.gwm.p03.MDC_FD1_312 mdc_fd1_312 = 19;
  optional deeproute.canbus.gwm.p03.CR_FD1_15E cr_fd1_15e = 20;
  optional deeproute.canbus.gwm.p03.ADAS_AD1_470 adas_ad1_470 = 21;
  optional deeproute.canbus.gwm.p03.RSDS_FD1_16F rsds_fd1_16f = 22;
  optional deeproute.canbus.gwm.p03.RSDS_FD2_30A rsds_fd2_30a = 23;
}

// topic: /blc/downstream_chassis_p03
message DownstreamChassisP03 {
  optional int64 timestamp = 1;  // us
  optional GwmP03DownstreamChassis chassis_detail = 2;  // chassis detail
}
