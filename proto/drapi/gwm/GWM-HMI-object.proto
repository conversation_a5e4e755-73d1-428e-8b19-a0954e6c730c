
syntax = "proto3";
package gwm;
import "drapi/gwm/GWM-HMI-common.proto";

message EgoVeh{
  uint64 time_stamp = 1;
  Vehicle_Pos vehicle_pos = 2;
  Guide_Line guide_line = 3;
  Obj_Disp objs = 4;
  repeated CriticalObject  critical_object = 5;
  Guide_Line decelerate_line = 6;
  ACCEgoStatusType acc_ego_status_type = 7;

  enum ACCEgoStatusType { 
    ACC_EGOSTATUS_NO_DISPLAY = 0; 
    ACC_EGOSTATUS_OVERRIDE = 1; 
    ACC_EGOSTATUS_DECELERATION = 2; 
    ACC_EGOSTATUS_RESERVED = 3; 
  };
  
  // debug
  uint64 init_time_stamp = 100;
  uint64 update_time_stamp = 101;
  uint64 processed_time_stamp = 102;
  bool is_framefilling = 103;
}

message Vehicle_Pos{
  float qx = 1; 
  float qy = 2; 
  float qz = 3; 
  float qw = 4; 
  float longitude = 5; 
  float latitude = 6; 
  float height = 7;
}

message Guide_Line{
 repeated Pnt3D pnt = 1;
}

message Obj_Disp{
  uint64 time_stamp = 1;
  repeated Obj_Info obj_info = 2; 
}

message Obj_Info{
  int32 id = 1; 
  Pnt3D pos = 2; 
  float speed = 3;
  float heading =4; 
  enum Obj_Type {
    OBJ_TYPE_UNKNOWN = 0;
    OBJ_TYPE_PASSENGER_CAR = 1; 
    OBJ_TYPE_TRUCK_BOX = 2; 
    OBJ_TYPE_BUS  = 3;
    OBJ_TYPE_BICYCLE = 4;
    OBJ_TYPE_MOTORCYCLE = 5; 
    OBJ_TYPE_TRICYCLE = 6; 
    OBJ_TYPE_PEDESTRIAN = 7; 
    OBJ_TYPE_CHILD = 8; 
    OBJ_TYPE_CONE = 9; 
    OBJ_TYPE_SUV = 10;
    OBJ_TYPE_MINIBUS = 11;
    OBJ_TYPE_ELETIRC_BICYCLE = 12; 
    OBJ_TYPE_POLICE = 13;
    OBJ_TYPE_BUCKET = 14;
    OBJ_TYPE_TRIPOD = 15;
    OBJ_TYPE_WATER_FILLED_BARRIER = 16;
    OBJ_TYPE_PICKUP = 17;
    OBJ_TYPE_TRUCK_FLAT = 18;
    OBJ_TYPE_SPECIAL_VEHICLES = 19;
    OBJ_TYPE_SCAFFOLD = 20;
    OBJ_TYPE_PILLAR = 21;
    OBJ_TYPE_SPEEDBUMP = 22;
    OBJ_TYPE_CHOCK = 23;
    OBJ_TYPE_LOCK = 24;
    OBJ_TYPE_MIXER = 25;
    OBJ_TYPE_OIL_CAR = 26;
    OBJ_TYPE_AMBULANCE = 27;
    OBJ_TYPE_FIRE_ENGINE = 28;
  };
  Obj_Type type = 5; 
  float length = 6; 
  float width = 7; 
  float height = 8; 
  repeated LightStatus light_status = 9;
  enum AttributeType {
    NORMAL_TYPE = 0;
    LEFT_DOOR_OPEN = 1;
    RIGHT_DOOR_OPEN = 2;
    TRUNK_OPEN = 3;
  };
  repeated AttributeType attribute_type =10;
}

message LightStatus {
  bool brake_switch_on = 1;
  bool left_turn_switch_on = 2;
  bool right_turn_switch_on = 3;
  bool left_right_turn_switch_on = 4;
}

message CriticalObject {
  int32 id = 1;
  enum ObjectType {
    GREEN_ALARM_OBJ = 0;
    YELLOW_ALARM_OBJ = 1;
    RED_ALARM_OBJ = 2;
  }
  ObjectType object_type = 2;
  float distance = 3;
}

