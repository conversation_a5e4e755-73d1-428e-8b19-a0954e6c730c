syntax = "proto3";
package gwm;
import "drapi/gwm/GWM-HMI-common.proto";

message CrossData {
  repeated BoundaryLine boundary_line = 1;
  repeated DrivingArea driving_area = 2;
  repeated Stopline stop_line = 3;
  repeated Area area = 4;
  repeated ArrowInfo arrow_info = 5;
  repeated Spline lane_spline = 6;
  uint64 time_stamp = 7;
  repeated Trafficlight traffic_light = 8;
  repeated Lane lanes = 9;
  LaneChangeRelated lane_change_related = 10;
  Lanechange_Sts lanechange_sts = 11;
  repeated ParkingSpace parking_space = 12;
  OperationStatus operation_status = 13;
  float turn_distance = 14;
  // for debug
  uint64 init_time_stamp = 100;
  uint64 update_time_stamp = 101;
  uint64 processed_time_stamp = 102;
  bool is_framefilling = 103;
}

message BoundaryLine {
  int32 id = 1;
  repeated Polyline polyline = 2;
}

message DrivingArea {
  int32 id = 1;
  repeated Polygon polygon = 2;
}

message Stopline {
  int32 id = 1;
  Pnt3D startPnt = 2;
  Pnt3D endPnt = 3;
}

message Area {
  int32 id = 1;
  Polygon polygon = 2;
  enum Type {
    UNKNOWN = 0;
    CROSSWALK = 1;
    WAITINGAREA = 2;
    DIVERSIONZONE = 3;
  };
  Type type = 3;
}

message ArrowInfo {
  int32 id = 1;
  enum ArrowType {
    ARROW_TYPE_INVALID = 0;
    ARROW_TYPE_S = 1;
    ARROW_TYPE_RT = 2;
    ARROW_TYPE_LT = 3;
    ARROR_TYPE_SRT = 4;
    ARROW_TYPE_SLT = 5;
    ARROW_TYPE_SLR = 6;
    ARROW_TYPE_LR = 7;
    ARROW_TYPE_UL = 8;
    ARROW_TYPE_UR = 9;
    ARROW_TYPE_ST = 10;
    ARROW_TYPE_CROSSWARN = 11;
    ARROW_TYPE_BUSONLY = 12;
  };
  ArrowType arrowtype = 2;
  Pnt3D pos = 3;
  Pnt2D dir = 4;
}

message Spline {
  int32 id = 1;
  enum LineType {
    LINE_TYPE_INVALID = 0;
    LINE_TYPE_SOLID = 1;
    LINE_TYPE_DASH = 2;
    LINE_TYPE_SOLID_DASH = 3;
    LINE_TYPE_DASH_SOLID = 4;
    LINE_TYPE_SOLID_SOLID = 5;
    LINE_TYPE_DASH_DASH = 6;
    LINE_TYPE_BOTTS = 7;
    LINE_TYPE_DECELERATION = 8;
    LINE_TYPE_HOV = 9;
    LINE_TYPE_ROAD_EDGE = 10;
    LINE_TYPE_ELEVATED_STRUCTURE = 11;
    LINE_TYPE_CURB = 12;
    LINE_TYPE_CONES_POLES = 13;
    LINE_TYPE_BARRIER = 14;
  };
  LineType linetype = 2;
  float a = 3;
  float b = 4;
  float c = 5;
  float d = 6;
  float x_start = 7;
  float x_end = 8;
  enum LineColor {
    LINE_COLOR_INVALID = 0;
    LINE_COLOR_GRAY = 1;
    LINE_COLOR_WHITE = 2;
    LINE_COLOR_YELLOW = 3;
  };
  LineColor linecolor = 9;
  Polyline lineboundary = 10;

  repeated LineColor point_boundary_colors = 11;
  repeated LineType point_boundary_shapes = 12;
}

message Trafficlight_Info {
  enum TFLType {
    TFL_TYPE_INVALID = 0;
    TFL_TYPE_S = 1;
    TFL_TYPE_L = 2;
    TFL_TYPE_R = 3;
    TFL_TYPE_U = 4;
    TFL_TYPE_Round = 5;
  };
  TFLType trafficlight_type = 1;
  enum TFLColor {
    TFL_COLOR_INVALID = 0;
    TFL_COLOR_GREY = 1;
    TFL_COLOR_RED = 2;
    TFL_COLOR_GREEN = 3;
    TFL_COLOR_YELLOW = 4;
    TFL_COLOR_RF = 5;
    TFL_COLOR_GF = 6;
    TFL_COLOR_YF = 7;
  };
  uint32 trafficlight_color = 2;
}

message Trafficlight {
  int32 id = 1;
  repeated Trafficlight_Info trafficlight_info = 2;
  uint32 trafficlight_time = 3;
  Pnt3D pos = 4;
  float length = 5;
  float width = 6;
  float height = 7;
}

message Lane {
  int32 id = 1;
  int32 left_boundary_id = 2;
  int32 right_boundary_id = 3;
  Polyline centerline = 4;
  repeated int32 predecessor_id = 5;
  repeated int32 successor_id = 6;
  int32 left_neighbor_id = 7;
  int32 right_neighbor_id = 8;
  bool is_ego_lane = 9;
}

message LaneChangeRelated {
  TargetLane target_lane = 1;
  enum TargetLane {
    MIDDLE = 0;
    LEFT = 1;
    RIGHT = 2;
  }
  repeated int32 target_lane_id = 2;
  Pnt3D target_pos = 3;   //落位区的位置
  Pnt2D target_dir = 4;   //落位区的朝向
}

message Lanechange_Sts {
  enum LanechangeSts {
    LANECHANGESTS_INVALID = 0;
    LANECHANGESTS_LEFT_SATISFIED = 1;
    LANECHANGESTS_RIGHT_SATISFIED = 2;
    LANECHANGESTS_LEFT_UNSATISFIED = 3;
    LANECHANGESTS_RIGHT_UNSATISFIED = 4;
    LANECHANGESTS_LEFT_SOLID = 5;
    LANECHANGESTS_RIGHT_SOLID = 6;
    LANECHANGESTS_FINISH = 7;
  }
  LanechangeSts lanechange_sts = 1;
}

message ParkingSpace {
  string id = 1;
  repeated ParkSpaceBoundingBox bounding_box = 2;
  ParkingSpaceType type = 3;
  ParkingSpaceStatus status = 4;
}

enum ParkingSpaceType {
  PARK_SPACE_TYPE_NONE = 0;
  PARK_SPACE_TYPE_VERTICAL = 1;
  PARK_SPACE_TYPE_LATERAL = 2;
  PARK_SPACE_TYPE_OBLIQUE = 3;
  PARK_SPACE_TYPE_INVALID = 4;
}
message ParkSpaceBoundingBox {
  Pnt3D point = 1;
  ParkSpacePosition position = 2;
  int32 quality = 3;
  enum ParkSpacePosition {
    POSITION_NONE = 0;
    POSITION_LEFT_FRONT = 1;
    POSITION_LEFT_REAR = 2;
    POSITION_RIGHT_REAR = 3;
    POSITION_RIGHT_FRONT = 4;
  }
}

enum ParkingSpaceStatus {
  PARK_SPACE_STATUS_UNKNOWN = 0;
  PARK_SPACE_STATUS_FREE = 1;
  PARK_SPACE_STATUS_OCCUPIED = 2;
  PARK_SPACE_STATUS_DESTINATION = 3;
  PARK_SPACE_STATUS_SELECTABLE = 4;
  PARK_SPACE_STATUS_FAVORITE = 5;
  PARK_SPACE_STATUS_INVALID = 6;
}

message OperationStatus {
  uint64 timestamp = 1;
  ICCStatusInfo ICC_status = 2;
  LDWStatusInfo ldw_status = 3;
  ELKStatusInfo elk_status = 4;
  LKAStatusInfo lka_status = 5;
}

enum ICCStatus {
  ICC_OFF = 0;
  ICC_FAILURE = 1;
  ICC_PASSIVE = 2;
  ICC_STANDBY = 3;
  ICC_NORMAL = 4;
  ICC_LATERAL_CONTROL_EXIT = 5;
  ICC_FORBIDDEN = 6;
  ICC_DRIVER_TAKEOFF = 7;
  ICC_LOW_RISK_FAILURE = 8;
  ICC_MRM = 9;
}

message ICCPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
  VehicleStatusCheckInfo vehicle_status_check_info = 3;
}

message ICCStatusInfo {
  ICCStatus status = 1;
  oneof reason { ICCPassiveReason passive_reason = 2; }
}

enum LDWStatus {
  LDW_OFF = 0;
  LDW_PASSIVE = 1;
  LDW_STANDBY = 2;
  LDW_LEFT_ACTIVE = 3;
  LDW_RIGHT_ACTIVE = 4;
  LDW_FAILURE = 5;
}
message LDWStatusInfo {
  LDWStatus status = 1;
  oneof reason { LDWPassiveReason passive_reason = 2; }
}

message LDWPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
}

enum ELKStatus {
  ELK_OFF = 0;
  ELK_FAILURE = 1;
  ELK_PASSIVE = 2;
  ELK_STANDBY = 3;
  ELK_ACTIVE = 4;
}

message ELKStatusInfo {
  ELKStatus status = 1;
  oneof reason { ELKActiveReason active_reason = 2; }
}

message ELKActiveReason {
  bool left_warning = 1;
  bool right_warning = 2;
  repeated int32 obj_id = 3;
}

enum LKAStatus {
  LKA_OFF = 0;
  LKA_PASSIVE = 1;
  LKA_STANDBY = 2;
  LKA_LEFT_ACTIVE = 3;
  LKA_RIGHT_ACTIVE = 4;
  LKA_FAILURE = 5;
}
message LKAStatusInfo {
  LKAStatus status = 1;
  oneof reason { LKAPassiveReason passive_reason = 2; }
}

message LKAPassiveReason {
  DeviceCheckInfo device_check_info = 1;
  BusinessCheckInfo business_check_info = 2;
}

// 以下枚举值必须和common保持一致
message DeviceCheckInfo {
  enum DeviceInfo {
    DEVICE_UNKNOW = 0;
    DRIVER_SEATBELT_NOT_BUCKLED = 1; //主驾安全带未系
    EPB_PARKED = 2;                  //电子手刹拉起
    GEAR_NOT_IN_D = 3;               //档位不在D挡
    DOOR_OPENED = 4;                 //四门两盖开启
    BRAKE_APPLIED = 5;               //非静止状态踩刹车
    WIPER_HIGH = 6;                  //雨刮高速
    CRUISE_CANCEL_PRESSED = 7;       //前拨拨杆
    THROTTLE_OVERRIDE = 8;           // 油门接管，使用: 泊车
    ACTUAL_ANGLE_OVER_THRESHOLD = 9; //方向盘转角超过门限
    LEFT_TURN_SIGNAL_ON = 10;        //左转向灯亮起
    RIGHT_TURN_SIGNAL_ON = 11;       //右转向灯亮起
    NO_TURN_SIGNAL = 12;             //没有转向灯信号
    GEER_IN_R = 13;                  //档位在R档
    GEER_IN_D = 14;                  //档位在D档位
    GEER_IN_N = 15;                  //档位在N档位
    GEER_IN_P = 16;                  //档位在P档位
    GEAR_OVERRIDE = 17;              // 档位接管, 使用：泊车
    STEERING_WHEEL_OVERRIDE = 18;    // 方向盘接管, 使用：泊车
    TRUNK_OPENED = 19;               // 后备箱打开, 使用：泊车
    REAR_VIEW_MIRROR_CLOSED = 20;    // 后视镜折叠,使用：泊车
    STEER_OVERRIDE = 21;             //方向盘接管

    STEER_WHEEL_SPEED_TO_HIGH = 22;   //方向盘转速过高
    DOUBLE_FLASH_ON = 23;             //双闪打开
    FOG_LIGHTS_ON = 24;               //雾灯打开
    HOOD_OPENED = 25;                 // 引擎盖开启
    EPS_TORSIONTORQUE_INVALID = 26;   // 扭矩invalid
    HOD_HANDOFF_MONITOR_INVALID = 27; // hod invalid
    DRIVER_DOOR_OPENED = 28;          // 主驾门打开
  }
  repeated DeviceInfo device_info = 1;
}
message BusinessCheckInfo {
  enum Status {
    STATUS_UNKNOW = 0;
    LCC_ACTIVATED = 1;     // LCC激活
    NOH_ACTIVATED = 2;     // NOH激活
    AEB_ACTIVATED = 3;     // AEB激活
    APA_ON = 4;            // APA开启
    ESC_OFF = 5;           // ESC OFF
    ABS_ACTIVED = 6;       // ABS激活
    HDC_ACTIVED = 7;       // HDC激活
    TCS_ACTIVED = 8;       // TCS激活
    VDC_ACTIVED = 9;       // VDC激活
    VCU_NOT_READY = 10;    // VCU未就绪
    CDP_ACTIVED = 11;      // CDP激活
    AVH_ACTIVED = 12;      // AVH激活
    ESC_ACTIVED = 13;      // ESC激活
    VCU_LIMPHOME_TCM = 14; // VCU在跛行模式
    EPS_NOT_READEY = 15;
    ESP_ACTIVED = 16;
    DTC_ACTIVED = 17;
    EBP_ACTIVED = 18; // EBP 激活
    EBD_ACTIVED = 19; // EBD 激活
    ACC_ACTIVED = 20;
    ODD_UNAVLIABLE = 21;
    ESC_SYSTEM_FAILED = 22; // ESC 系统故障
    EPS_SYSTEM_FAILED = 23; // EPS 系统故障
    VCU_SYSTEM_FAILED = 24; // VCU 系统故障
    EPB_SYSTEM_FAILED = 25; // EPB 系统故障
    LDW_ACTIVATED = 26;
    RDP_ACTIVATED = 27;

    TOO_SLOW_SPEED = 4000;          //速度太低
    TOO_FAST_SPEED = 4001;          //速度过高
    HIGH_YAW_RATE = 4002;           //横摆角速度高
    MISSING_LEFT_LANE_LINE = 4003;  //左边车道线丢失
    MISSING_RIGHT_LANE_LINE = 4004; //右边车道线丢失
    OVER_WIDE_LANE = 4005;
    OVER_NARROW_LANE = 4006;
    EXCESSIVE_CURVATURE_LANE = 4007;

    HEAVY_BRAKE_PRESSURE = 4008;
    EXCESSIVE_STEERING_LEFT_TORQUE = 4009;
    EXCESSIVE_STEERING_RIGHT_TORQUE = 4010;
    EXCESSIVE_STEERING_ANGLE = 4011;
    EXCESSIVE_STEERING_ANGLE_RATE = 4012;
    HIGH_ACC_PEDAL_RATE = 4013; // > 70%/s
    EXCESSIVE_WARNING_DURATION = 4014;
    TOO_SHORT_WARNING_INTERVAL = 4015;
    FRONT_WHEEL_EXCEED_WARNING_LINE = 4016;

    NO_NAVIGATION_INFO = 4500;  //没有导航信息
    NAVIGATION_ABNORMAL = 4501; //导航异常
    OUT_ODD_ROAD = 4502;
    NAVIGATION_ID_NOT_MATCH = 4503; //新的导航未开启成功，导致导航id不匹配
  }
  repeated Status status = 1;
}
message VehicleStatusCheckInfo {
  enum Status {
    STATUS_UNKNOW = 0;
    VEHICLE_SLIP = 1;                     //车辆溜车
    VEHICLE_SPEED_OVER_THRESHOLD = 2;     //车速超过门限>150km/h
    CURVE_RADIUS_LESS_THAN_THRESHOLD = 3; //<125m
    VEHICLE_SPEED_TO_LOW = 5;             //车速过低
    NOT_STANDSTILL = 6;                   //车辆非静止
  }
  repeated Status status = 1;
}