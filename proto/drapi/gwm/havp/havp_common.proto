syntax = "proto3";
package havp;

message Coor3{// 不用vector3
  double x = 1; // 二维平面横坐标
  double y = 2; // 二维平面纵坐标
  double z = 3; // 三维高度坐标
}

message Point {
  double x = 1; // 二维平面横坐标
  double y = 2; // 二维平面纵坐标
  double z = 3; // 三维高度坐标
  string floor = 7; // 楼层 (可以不传，自然语言标识, B1为负一层, M为过道, F1为一层)
}

message Location{
  double x = 1; // 二维平面横坐标
  double y = 2; // 二维平面纵坐标
  double z = 3; // 三维高度坐标
  double yaw = 4; // 车辆行驶方向，与X轴(前方)偏向Y轴的夹角(弧度单位, 范围: -π – π)
  double pitch = 5; // 可以不传 俯仰角
  double roll = 6; // 可以不传
  string floor = 7; // 楼层 (可以不传，自然语言标识, B1为负一层, M为过道, F1为一层)
}

message Pnt3D {
  float x = 1 ;//二维平面横坐标
  float y = 2 ; // 二维平面纵坐标
  float z = 3 ; // 三维高度坐标
}

message BoundingBox {// 障碍物包裹框
  Coor3 left_front = 1; // 左前角点
  Coor3 left_rear = 2; // 左后
  Coor3 right_rear = 3; // 右后
  Coor3 right_front = 4; // 右前
  Coor3 left_front_top = 5; // 上方左前角点
  Coor3 left_rear_top = 6; // 上方左后
  Coor3 right_rear_top = 7; // 上方右后
  Coor3 right_front_top = 8; // 上方右前
}

message ObstaclePose {  // 障碍物位置信息
  Coor3 point = 1;      // 障碍物中心点位置
  float direction = 2;  // 障碍物方向, 自车坐标系FLU中, 与X轴(前方)偏向Y轴的夹角(弧度单位, 范围: π 到 –π)
}

message ParkingSpace {// 车位信息
  string id = 1;                                    // 车位id（可以是高精地图标识，或者是唯一ID。例如第801293号停车位）
  uint32 number = 2;                                // 车位编号（1~8的滚动编号，用于选择车位。）
  repeated ParkSpaceBoundingBox bounding_box = 3;   // 车位轮廓描述(4个角+阻车器，车身坐标系)
  ParkingSpaceType type = 4;                        // 车位类型
  ParkingSpaceStatus status = 5;                    // 车位状态
  ParkingSpaceUsage use = 6;                        // 车位用途
  ParkingSpaceObstacleType obstacle_type = 7;       // 被占用情况
  string floor = 8;                                 // 楼层 (可以不传，自然语言标识, B1为负一层, M为过道, F1为一层)
  bool favorite = 9;                                // 收藏车位（用于记忆泊车的终点车位，巡航的时候发送）
  int32 favorite_time = 10;                         // 收藏车位时间（用于车位列表排序）(秒时间戳)
  string name = 11;                                 // 车位名称
  ParkingSpaceTag tag = 12;                         // 标签（充电桩、电梯、楼梯、出入口等）
  bool is_default = 13;                             // 是否为默认车位
  bool is_last_destination = 14;                    // 是否为上次目标车位
  ParkingSpaceSizeType size_type = 15;              // 车位空间大小类型
  ParkingDir dir = 16;                              // 泊入方向

  // ParkSpaceBoundingBox 是车位上的障碍物的物理大小, 可用于确定车位上如何放置模型
  message ParkSpaceBoundingBox {                    // 车位轮廓描述(全局坐标系)
    Coor3 point = 1;                                // 全局坐标系坐标点
    ParkSpacePosition position = 2;                 // 轮廓位置描述
    int32 quality = 3;                              // 角点识别质量(0质量低, 1质量高)

    enum ParkSpacePosition {                        // 轮廓角点位置描述
      POSITION_NONE = 0;                            // #0占位
      POSITION_LEFT_FRONT = 1;                      // 车位左前角点
      POSITION_LEFT_REAR = 2;                       // 车位左后角点
      POSITION_RIGHT_REAR = 3;                      // 车位右后角点
      POSITION_RIGHT_FRONT = 4;                     // 车位右前角点
      //      POSITION_STOP_LEFT = 5;               // 阻车器左角点
      //      POSITION_STOP_RIGHT = 6;              // 阻车器右角点
      POSITION_INVALID = 7;                         // 无效，错误位
    }
  }

  enum ParkingSpaceType {                           // 车位类型
    PARK_SPACE_TYPE_NONE = 0;                       // 无
    PARK_SPACE_TYPE_VERTICAL = 1;                   // 垂直车位
    PARK_SPACE_TYPE_LATERAL = 2;                    // 水平车位
    PARK_SPACE_TYPE_OBLIQUE = 3;                    // 倾斜车位
    PARK_SPACE_TYPE_INVALID = 4;                    // 错误位
  }
  enum ParkingSpaceStatus {                         // 车位状态
    PARK_SPACE_STATUS_UNKNOWN = 0;                  // 状态未知
    PARK_SPACE_STATUS_FREE = 1;                     // 车位状态为空且不可选
    PARK_SPACE_STATUS_OCCUPIED = 2;                 // 车位状态为被占用
    PARK_SPACE_STATUS_DESTINATION = 3;              // 目标车位, APA泊车选中状态
    PARK_SPACE_STATUS_SELECTABLE = 4;               // 车位状态为空且可选
    PARK_SPACE_STATUS_SELECTED = 5;                 // 车位选中，但非目标车位
    PARK_SPACE_STATUS_INVALID = 6;                  // 无效，错误位
    PARK_SPACE_STATUS_UPDATE_IGNORE = 7;            // 巡航map数据更新时车位状态不作为参考（map数据多次发送时采用）
  }
  enum ParkingSpaceUsage {                          // 车位用途
    PARK_SPACE_USE_INVALID = 0;                     // 无效, 错误位
    PARK_SPACE_USE_NORMAL = 1;                      // 普通车位
    //    PARK_SPACE_USE_STEREO = 2;                // 专用车位
    //    PARK_SPACE_USE_SPECIAL = 3;               // 特殊车位
    //    PARK_SPACE_USE_RESERVED = 4;              // 预留车位
    PARK_SPACE_USE_CHARGE = 5;                      // 充电车位
  }


  enum ParkingSpaceObstacleType {                   // 占用车位的障碍物类型
    PARK_SPACE_OBSTACLE_TYPE_UNKNOW = 0;            // 未知
    PARK_SPACE_OBSTACLE_TYPE_CAR = 1;               // 小轿车
    PARK_SPACE_OBSTACLE_TYPE_NODISPLAY = 2;         // 用于表示车位上有物体, 但是此物体不用显示
    PARK_SPACE_OBSTACLE_TYPE_PEDESTRIAN = 3;        // 行人
    PARK_SPACE_OBSTACLE_TYPE_CONE = 4;              // 锥桶
    PARK_SPACE_OBSTACLE_TYPE_LOCK = 5;              // 锁定
    PARK_SPACE_OBSTACLE_TYPE_PARKINGLOCK = 6;       // 地锁
    PARK_SPACE_OBSTACLE_TYPE_VAN = 7;               // 厢式货车
    PARK_SPACE_OBSTACLE_TYPE_SMALL = 8;             // 窄空间
    // PARK_SPACE_OBSTACLE_TYPE_ROADDELIMIRER = 8;  // 道路定界符
  }
  
  enum ParkingSpaceTag {                          // 车位标签
    PARK_SPACE_TAG_NONE = 0;                      // 无标签
    PARK_SPACE_TAG_CHARGE = 1;                    // 充电桩
    PARK_SPACE_TAG_ELEVATOR = 2;                  // 电梯
    PARK_SPACE_TAG_STAIRCASE = 3;                 // 楼梯
    PARK_SPACE_TAG_PASSAGEWAY = 4;                // 出入口
  }

  enum ParkingSpaceSizeType {                          // 车位空间大小类型
    PARK_SPACE_SIZE_TYPE_NONE = 0;                     // 无效值
    PARK_SPACE_SIZE_TYPE_STANDARD = 1;                 // 标准空间车位
    PARK_SPACE_SIZE_TYPE_NARROW = 2;                   // 狭小空间车位
    PARK_SPACE_SIZE_TYPE_WIDE = 3;                     // 宽敞空间车位
  }

  enum ParkingDir {                                 // 泊入方向类型
    PARK_DIR_NONE = 0;                              // 无效值
    PARK_DIR_HEAD_FIXED = 1;                        // 车头泊入，不可切换
    PARK_DIR_TAIL_FIXED = 2;                        // 车尾泊入，不可切换
    PARK_DIR_HEAD_SWITCHABLE = 3;                   // 车头泊入，可切换
    PARK_DIR_TAIL_SWITCHABLE = 4;                   // 车尾泊入，可切换  
  }
}

// 地图中存储的障碍物。如立柱和减速带
// 使用全局坐标系。
message MapObstacle {                           // 障碍物信息
  int32 id = 1;                                 // 障碍物id 障碍物的唯一ID
  MapObstacleType type = 2;                     // 识别物类型
  BoundingBox position_info = 3;                // 地图中存储在识别物使用特征坐标点表示。例如减速带是2个点，而立柱是4个点。
  string floor = 4;                             // 楼层 (可以不传，自然语言标识, B1为负一层, M为过道, F1为一层)

  enum MapObstacleType {                        // 障碍物类型
    OBSTACLE_TYPE_INVALID = 0;                  // 无效, 错误位
    OBSTACLE_TYPE_PILLAR_ROUND = 1;             // 圆型立柱
    OBSTACLE_TYPE_PILLAR_SQUARE = 2;            // 方型立柱
    OBSTACLE_TYPE_BUMPER = 3;                   // 减速带
  }
}

// 感知实时识别到的障碍物。如：行人、行车、锥桶等
message ObjectDetectionObstacle {               // 障碍物信息
  int32 id = 1;                                 // 障碍物id 障碍物的唯一ID
  ObjectDetectionObstacleType type = 2;         // 障碍物类型
  //  ObstaclePose pose = 3;                    // 障碍物朝向
  BoundingBox position_info = 3;                // 地图中存储在识别物使用特征坐标点表示。例如减速带是2个点，而立柱是4个点。
  bool is_moving = 4;                           // 目标是否在移动（目前主要用于行人）
  ObjAlarmLevel obj_alarm_level=5;              // 障碍物告警等级，HUT根据等级渲染颜色

  enum ObjectDetectionObstacleType {            // 障碍物类型
    OBSTACLE_TYPE_INVALID = 0;                  // 无效, 错误位
    OBSTACLE_TYPE_PILLAR_ROUND = 1;             // 圆型立柱
    OBSTACLE_TYPE_PILLAR_SQUARE = 2;            // 方型立柱
    OBSTACLE_TYPE_BUMPER = 3;                   // 减速带
    OBSTACLE_TYPE_CAR = 4;                      // 汽车、吉普车、SUV、面包车
    OBSTACLE_TYPE_PEDESTRIAN = 5;               // 行人
    OBSTACLE_TYPE_CONE = 6;                     // 锥桶等
    OBSTACLE_TYPE_FLOOR_ENTRANCE = 7;           // 楼层入口
    OBSTACLE_TYPE_FLOOR_EXIT = 8;               // 楼层出口
    OBSTACLE_TYPE_ARROW = 9;                    // 箭头
    OBSTACLE_TYPE_WARNINGSIGN = 10;             // 警示牌
    OBSTACLE_TYPE_TWOWHEEL = 11;                // 非机动车
    OBSTACLE_TYPE_PARKINGLOCK = 12;             // 地锁
    OBSTACLE_TYPE_MANHOLECOVER = 13;            // 井盖
    OBSTACLE_TYPE_BARRIERGATE = 14;             // 闸机
    OBSTACLE_TYPE_WARNINGPOST = 15;             // 警示柱
    OBSTACLE_TYPE_LAMP = 16;                    // 灯柱
    OBSTACLE_TYPE_RIDER = 17;                   // 骑行者
    OBSTACLE_TYPE_STOPPER_SHORT = 18;           // 短轮挡
    OBSTACLE_TYPE_STOPPER_LONG = 19;            // 长轮挡
    OBSTACLE_TYPE_WALL = 20;                    // 墙壁
    OBSTACLE_TYPE_WATER_FILLED_BARRIER = 21;    // 水马
    OBSTACLE_TYPE_TRASH_CAN = 22;               // 垃圾桶
    OBSTACLE_TYPE_GROUND_LOCK_OPEN = 23;        // 打开的地锁
    OBSTACLE_TYPE_GROUND_LOCK_CLOSE = 24;       // 关闭的地锁
    OBSTACLE_TYPE_PARKING_LIMITER = 25;         // 一体限位器
    OBSTACLE_TYPE_PARKING_LIMITER_SEPARATE = 26;// 分离限位器
    OBSTACLE_TYPE_VAN = 27;                     // 厢式货车
    OBSTACLE_TYPE_CHILD = 28;                   // 儿童 

    // TRAFFIC_SIGN 交通标志箭头
    OBSTACLE_TYPE_ARROW_TS_STRAIGHT = 100;    // 直行
    OBSTACLE_TYPE_ARROW_TS_LEFT = 101;        // 左转
    OBSTACLE_TYPE_ARROW_TS_RIGHT = 102;       // 右转
    OBSTACLE_TYPE_ARROW_TS_BACK = 103;        // 掉头
    OBSTACLE_TYPE_ARROW_TS_TOGETHER_LEFT = 104;   // 左转合流
    OBSTACLE_TYPE_ARROW_TS_TOGETHER_RIGHT = 105;  // 右转合流
    OBSTACLE_TYPE_ARROW_TS_LEFT_RIGHT = 120;      // 左转+右转
    OBSTACLE_TYPE_ARROW_TS_STRAIGHT_LEFT = 121;   // 直行+左转
    OBSTACLE_TYPE_ARROW_TS_STRAIGHT_RIGHT = 122;  // 直行+右转
    OBSTACLE_TYPE_ARROW_TS_LEFT_BACK = 123;       // 左转+掉头
    OBSTACLE_TYPE_ARROW_TS_RIGHT_BACK = 124;      // 右转+掉头
    OBSTACLE_TYPE_ARROW_TS_STRAIGHT_LEFT_RIGHT = 125; // 直行+左转+右转

    //    OBSTACLE_TYPE_TRUCK = 1; // 卡车，箱式卡车，皮卡车，大篷车，洒水车大型车辆，且后部/装载区域与驾驶室物理分离
    //    OBSTACLE_TYPE_BUS = 3; // 公交车、客车
    //    OBSTACLE_TYPE_TRAFFIC_SIGN_ARROW = 7; // 交通标志箭头
    //    OBSTACLE_TYPE_ULTRASONIC = 8; // 超声波识别障碍物
    //    OBSTACLE_TYPE_BAN = 9; // 禁停牌
    //    OBSTACLE_TYPE_GENERAL_OBJECT = 10; // freespace识别障碍物
    //    OBSTACLE_TYPE_UNKNOWN = 11; // 其他未知障碍物
    //    OBSTACLE_TYPE_CYCLIST = 5; // 摩托车、自行车、电动车等
  }

  enum ObjAlarmLevel { //定义中的颜色只代表等级，和HUT实际渲染颜色无关
     OBJ_ALARM_LEVEL_NONE = 0;    //无，正常物体
     OBJ_ALARM_LEVEL_GREEN = 1;   //跟随物体
     OBJ_ALARM_LEVEL_YELLOW = 2;  //告警物体
     OBJ_ALARM_LEVEL_RED = 3;     //碰撞风险物体
  }

}

message WallElement {                    // 墙壁
  repeated Polygon wall = 1;             // 多边形描绘墙壁, 用wall_bottom代替
  Polygon wall_bottom = 2;               // 多边形描绘墙壁的底面
}

message Polygon {
  repeated Pnt3D pnt = 1;               
}

message Polyline {
  repeated Pnt3D pnt = 1;                
}
  
message PAS {//雷达信息
  Distance FPAS_OBJFLCORNRAR1 = 1; // 前左角1
  Distance FPAS_OBJFLCORNRAR2 = 2; // 前左角2
  Distance FPAS_OBJFRCORNRAR1 = 3; // 前右角1
  Distance FPAS_OBJFRCORNRAR2 = 4; // 前右角2
  Distance FPAS_OBJFLMIDLAR1 = 5; // 前左中1
  Distance FPAS_OBJFLMIDLAR2 = 6; // 前左中2
  Distance FPAS_OBJFRMIDLAR1 = 7; // 前右中1
  Distance FPAS_OBJFRMIDLAR2 = 8; // 前右中2

  Distance RPAS_OBJRLCORNRAR1 = 9; // 后左角1
  Distance RPAS_OBJRLCORNRAR2 = 10; // 后左角2
  Distance RPAS_OBJRRCORNRAR1 = 11; // 后右角1
  Distance RPAS_OBJRRCORNRAR2 = 12; // 后右角2
  Distance RPAS_OBJRLMIDLAR1 = 13; // 后左中1
  Distance RPAS_OBJRLMIDLAR2 = 14; // 后左中2
  Distance RPAS_OBJRRMIDLAR1 = 15; // 后右中1
  Distance RPAS_OBJRRMIDLAR2 = 16; // 后右中2

  enum Distance {
    DISTANCE_NONE = 0; // 未定义
    F0T10CM = 1; // 0-10cm
    F11T20CM = 2; // 11-20cm
    F21T30CM = 3; // 21-30cm
    F31T40CM = 4; // 31-40cm
    F41T50CM = 5; // 41-50cm
    F51T60CM = 6; // 51-60cm
    F61T70CM = 7; // 61-70cm
    F71T80CM = 8; // 71-80cm
    F81T90CM = 9; // 81-90cm
    F91T100CM = 10; // 91-100cm
    F101T110CM = 11; // 101-110cm
    F111T120CM = 12; // 111-120cm
    F121T130CM = 13; // 121-130cm
    F131T140CM = 14; // 131-140cm
    F141T150CM = 15; // 141-150cm
  }
}

enum Color {      // 颜色
  COLOR_NONE = 0;     // 不需要渲染颜色
  COLOR_WHITE = 1;
  COLOR_YELLOW = 2;
  COLOR_BLUE = 3;
  COLOR_GREEN = 4;
  COLOR_RED = 5;
}

message SignlineVertical  {
  int32 id = 1;
  LineType type = 2;  //线类型
  LineShape shape = 3;  //形状
  Color color = 4;  
  LineMaterial material = 5; //材质
  repeated Polyline polyline = 6;// 线坐标点

  enum LineType{
    LINE_TYPE_BOUNDARY = 0; //道路边界
    LINE_TYPE_LANE = 1;//车道线
  }
  enum LineShape{  
    LINE_SHAPE_NONE = 0; 
    LINE_SHAPE_BOUNDARY = 1; //道路边界
    LINE_SHAPE_DOTTED = 2;//虚线
    LINE_SHAPE_SOLID = 3;//实线
    LINE_SHAPE_DOUBLE_DOTTED = 4;//双虚线
    LINE_SHAPE_DOUBLE_SOLID = 5;//双实线
  }
  enum LineMaterial{
    LINE_MATERIAL_NONE = 0;//未知
    LINE_MATERIAL_MARKING = 1;//标线
    LINE_MATERIAL_CURB = 2;//路沿
    LINE_MATERIAL_GUARD_BAR= 3 ;//护栏
  }
}

message SignPolygon  {
  int32 id = 1;
  PolygonType type = 2;
  Color color = 3;
  repeated Polygon polygon = 4;//面坐标点

  enum PolygonType {
    POLYON_TYPE_NONE = 0;
    POLYON_TYPE_DRIVINGAREA = 1;//可行驶区域
    POLYON_TYPE_CROSSWALK = 2;//人行横道
    POLYON_TYPE_NOPARKINGAREA = 3;//禁停区域
  }
}

message AVPMapPin {
  int32 id = 1;                                 // 唯一id
  string name = 2;                              // 名称
  double longitude = 3;                         // 地图经度，单位度 GCJ02坐标 
  double latitude = 4;                          // 地图纬度，单位度 GCJ02坐标
  double altitude = 5;                          // 地图高度，单位米
  string address = 6;                           // 地址
  AVPMapPinTag tag = 7;                         // 标签

  enum AVPMapPinTag {
    AVP_MAP_PIN_TAG_NONE = 0;                  // 无
    AVP_MAP_PIN_TAG_HOME = 1;                  // 家
    AVP_MAP_PIN_TAG_MALL = 2;                  // 商场
    AVP_MAP_PIN_TAG_HOSPITAL = 3;              // 医院
    AVP_MAP_PIN_TAG_PARK = 4;                  // 公园
  }
}

enum HAVPNaviTips {               //提示内容以UE为准，以下备注仅为提示大意
  HAVP_TIP_NONE               =0; //无文言 
  HAVP_TIP_PARKING_OUT        =1; //驶出车位  
  HAVP_TIP_ARRIVE_DEST        =2; //前方到达目标车位  
  HAVP_TIP_TURN_LEFT          =3; //前方左转 
  HAVP_TIP_TURN_RIGHT         =4; //前方右转 
  HAVP_TIP_TURN_AROUND        =5; //请掉头
  HAVP_TIP_UPHILL             =6; //即将上坡
  HAVP_TIP_DOWNHILL           =7; //即将下坡
  HAVP_TIP_ENTER_PARKING_LOT  =8; //即将驶入地库
  HAVP_TIP_EXIT_PARKING_LOT   =9; //即将驶离地库
  HAVP_TIP_RETURN_ROAD        =10; //请行驶至道路 
}

message Road {
  int32 id            = 1; //道路id
  repeated Point pts  = 2; //道路中心线点集
}

message AVPMap {
  repeated Road roads = 1; //道路集合
}
