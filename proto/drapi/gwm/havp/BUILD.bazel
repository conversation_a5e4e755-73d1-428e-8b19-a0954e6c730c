package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "havp_common_proto",
    visibility = ["//visibility:public"],
    srcs = ["havp_common.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "havp_common_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":havp_common_proto",
    ],
)

python_proto_library(
    name = "havp_common_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":havp_common_proto",
    ],
)

proto_library(
    name = "havp_map_proto",
    visibility = ["//visibility:public"],
    srcs = ["havp_map.proto"],
    deps = [
        "//proto/drapi/gwm/havp:havp_common_proto"
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "havp_map_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":havp_map_proto",
    ],
)

python_proto_library(
    name = "havp_map_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":havp_map_proto",
    ],
)

proto_library(
    name = "havp_vehicle_proto",
    visibility = ["//visibility:public"],
    srcs = ["havp_vehicle.proto"],
    deps = [
        "//proto/drapi/gwm/havp:havp_common_proto"
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "havp_vehicle_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":havp_vehicle_proto",
    ],
)

python_proto_library(
    name = "havp_vehicle_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":havp_vehicle_proto",
    ],
)