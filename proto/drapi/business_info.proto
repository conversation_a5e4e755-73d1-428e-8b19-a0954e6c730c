syntax = "proto3";

import "drapi/base.proto";
import "drapi/navigation_map.proto";

package dr.business_info;

/**********************************业务功能引用的结构定义**************************/

// common
message Travel {
  double total_distance = 1;
  double total_time = 2;
  uint32 start_time = 3;
  uint32 end_time = 4;
}

// ACC
enum SpeedLimitType {
  RELATIVE_SPEED_LIMIT = 0;
  ABSOLUTE_SPEED_LIMIT = 1;
}

message SpeedLimit {
  double speed = 1;  // unit m/s
  SpeedLimitType type = 2;
}

message FollowDistance {
  double distance = 1;  // unit m
}

enum FollowTimeGap {
  TAU_GAP_0 = 0;
  TAU_GAP_1 = 1;
  TAU_GAP_2 = 2;
  TAU_GAP_3 = 3;
  TAU_GAP_4 = 4;
}

// ICA

// NCA
message RoutingOption {
  enum RoutingStrategy {
    SYSTEM_RECOMMENDED = 0;
    HIGH_SPEED = 1;
    SHORTEST_PATH = 2;
    MOST_ECONOMICAL = 3;
  }

  enum RoutePlanningType {
    NAVI_PLANNING = 0;
    DR_PLANNING = 1;
  }

  enum UTurnMetric {
    NO_TURN_BACK = 0;
    NORMAL_TURN_BACK = 1;
  }

  enum HighwayMetric {
    NORMAL_HIGHWAY = 0;
    NO_HIGHWAY = 1;
    MORE_HIGHWAY = 2;
  }

  RoutingStrategy strategy = 1;
  bool is_elevated = 2;
  RoutePlanningType route_planning_type = 3;
  UTurnMetric u_turn_metric = 4;
  HighwayMetric high_way_metric = 5;
}

message SegmentInfo {
  navigation_map.RoadClass road_class = 1;
  repeated dr.base.PointLLH points = 2;
}

message MismatchSegmIndex {
  uint32 start_index = 1;
  uint32 end_index = 2;
}

message Path {
  int32 path_id = 1;
  repeated dr.base.PointLLH points = 2;
  double duration = 3;
  // Route distance, unit: meters.
  double distance = 4;
  int32 traffic_lights_num = 5;
  repeated SegmentInfo segment_infos = 6;
  repeated MismatchSegmIndex mismatch_indexes = 7;
  uint64 path_id_64 = 8;
  // Route distance that can be able to use NCA, unit: meters.
  int32 nca_distance = 9;
}

message RoutingResult {
  repeated Path paths = 1;
}

message CurNaviPointIndex {
  int32 route_point_index = 1;
  int32 passing_point_index = 2;
}

//单个object像素点坐标集合
message ImagePixelCoordinates {
  string id = 1;
  repeated dr.base.RotatedRect rotated_rects = 2;
  repeated dr.base.Point2D polygon_points = 3;
  repeated dr.base.Point2D polyline_points = 4;
}

message TransformedObject {
  string id = 1;
  // dtu根据 ObjectType 决定填standbyArea, polygons 还是polyline
  repeated dr.base.ParkingSpace parking_space = 2;
  dr.base.Polygon polygon = 3;
  dr.base.Polyline polyline = 4;
}
// avm

// 2D 模式AVM 设置
enum AVM2DViewSetting {
  TYPE_UNKNOWN = 0;  // 保留字段
  // 前后
  BACK_VIEW_NORMAL = 1;   // 切换后视图默认
  BACK_VIEW_BROAD = 2;    // 切换后视图广角
  FRONT_VIEW_NORMAL = 3;  // 切换前视图默认
  FRONT_VIEW_BROAD = 4;   // 切换前视图广角
  // 左右
  LEFT_VIEW = 5;   // 切换左视角
  RIGHT_VIEW = 6;  // 切换右视角
}

// 轮毂视图设置
enum AVMWheelView {
  WHELL_NEW_UNKNOWN = 0;
  FRONT_WHELL_VIEW = 1;  // 前轮毂
  MIDDLE_VIEW = 2;       // 中间
  BACK_WHEEL_VIEW = 3;   // 后轮毂
}
// 3d 旋转视图设置
message AVM3DView {
  float yaw_angle = 1;  // 用于设置绝对角度
  float pitch_angle = 2;
}

message AVMSettingInfo {
  enum AVM2DViewStatus {
    TYPE_UNKNOWN = 0;  // 保留字段
    // 前后
    BACK_VIEW_NORMAL = 1;   // 后视图默认
    BACK_VIEW_BROAD = 2;    // 后视图广角
    FRONT_VIEW_NORMAL = 3;  // 前视图默认
    FRONT_VIEW_BROAD = 4;   // 前视图广角
    // 左右
    LEFT_VIEW_FRONT = 5;   // 左轮视角向前
    LEFT_VIEW_BACK = 6;    // 左轮视角向后
    RIGHT_VIEW_FRONT = 7;  // 右轮视角向前
    RIGHT_VIEW_BACK = 8;   // 右轮视角向后
  }

  enum AVMStatus {
    STATUS_UNKNOWN = 0;
    STATUS_TURNING_MODE = 1;      // AVM 开启： 转向模式
    STATUS_NORMAL_MODE = 2;       // AVM 开启： 一般模式
    STATUS_GEAR_R_MODE = 3;       // AVM 开启：R 档模式
    STATUS_GEAR_R_TO_D_MODE = 4;  // AVM开启: R到D档模式
    STATUS_PDC_MODE = 5;
    STATUS_OFF = 6;
    STATUS_ERROR = 7;
    STATUS_STANDBY = 8;
    STATUS_SUSPEND = 9;
    STATUS_FAILED = 10;
    STATUS_ACTIVE = 11;
    STATUS_INACTIVE = 12;
    STATUS_ON = 13;
    STATUS_WORKING = 14;
    STATUS_GWM = 15;
    STATUS_CALIBRATION = 16;
    STATUS_CALIBRATION_FINISHED = 17;
    STATUS_CALIBRATION_FAILED = 18;
  }

  AVMStatus status = 1;
  bool bird_view_enabled = 2;
  bool round_view_enable = 3;
  oneof View {
    AVM2DViewStatus avm2dview = 4;
    AVM3DView avm3dview = 5;
    AVMWheelView avmwheelview = 6;
  }
}

enum AVMEnableMode {
  MODE_UNKNOWN = 0;
  MODE_NORMAL = 1;
  MODE_3D_CONQUEROR = 2;
}

// apa
enum APAOutDirection {
  UNKONWN = 0;
  PARELLEL_LEFT = 1;   // 水平车位左
  PARELLEL_RIGHT = 2;  // 水平车位右
  FRONT_LEFT = 3;      // 垂直、斜列左前
  FRONT_RIGHT = 4;     // 垂直、斜列右前
  BACK_LEFT = 5;       // 垂直、斜列后左
  BACK_RIGHT = 6;      // 垂直、斜列后右
  STRAIGHT_FRONT = 7;  // 垂直、斜列前
  STRAIGHT_BACK = 8;   // 垂直、斜列后
}

message ParkingOutSuggestPose {
  double x = 1;
  double y = 2;
  double heading = 3;
}

message SuggestOutParkingInfo {
  enum ParkingSpaceType {
    UNKNOWN = 0;
    VERTICAL = 1;  // rectangle
    PARALLEL = 2;  // rectangle
    SLANTED = 3;   // parallelogram, the angle is not 90 degree
  }
  enum FailedReason {
    UNKNOWN_REASON = 0;
    BLOCK_BY_OBSTACEL = 1;
    SPACE_LIMITED = 2;
  }
  ParkingSpaceType parking_space_type = 1;
  repeated APAOutDirection available_out_directions = 2;
  APAOutDirection recommend_out_direction = 3;
  repeated ParkingOutSuggestPose suggest_poses = 4;
  FailedReason failed_reason = 5;  // when no any suggest
}

// VPA
message VPASummary {
  uint32 vpa_total_time = 1;
  uint32 vpa_total_distance = 2;
  uint32 vpa_total_obstacles = 3;
}

enum CalibrationMode {
  CALIBRATION_MODE_ONLINE_UNKNOWN = 0x00;
  CALIBRATION_MODE_ONLINE_RASMAP = 0x01;
  CALIBRATION_MODE_ONLINE_CAMERA = 0x02;
  CALIBRATION_MODE_ONLINE_LIDAR = 0x03;
  CALIBRATION_MODE_ONLINE_RADAR = 0x04;
  CALIBRATION_MODE_ONLINE_AVM = 0x05;
  CALIBRATION_MODE_ONLINE_INERTIAL_NAVIGATION = 0x06;

  CALIBRATION_MODE_STATIC_CAMERA = 0x07;        //所有相机静态标定
  CALIBRATION_MODE_STATIC_LIDAR = 0x08;         //雷达静态标定
  CALIBRATION_MODE_STATIC_RADAR = 0x09;         //毫米波雷达静态标定
  CALIBRATION_MODE_STATIC_IMU = 0x0A;           // imu静态标定
  CALIBRATION_MODE_STATIC_AVM = 0x0B;           //环视静态标定
  CALIBRATION_MODE_STATIC_SVM = 0x0C;           //周视静态标定
  CALIBRATION_MODE_ONLINE_MULTI_LIDAR = 0x0D;   //动态多雷达外参标定
  CALIBRATION_MODE_DYNAMIC_CAMERA = 0x0E;       //所有相机动态标定
  CALIBRATION_MODE_DYNAMIC_SVM = 0x0F;          //周视相机动态标定
  CALIBRATION_MODE_DYNAMIC_AVM = 0x10;          //环视相机动态标定
  CALIBRATION_MODE_STATIC_AVM_4S_STORE = 0x11;  // 4S店环视静态标定
}

enum CalibrationGwmErrCode {
  CALIB_GWM_ERR_CODE_UNKNOWN = 0X00;
  CALIB_GWM_SUCCESS = 0X01;
  CALIB_GWM_ERR_CODE_CORNER_DETECTION_ERROR = 0X02;
  CALIB_GWM_ERR_CODE_INTRINSIC_ERROR = 0X03;
  CALIB_GWM_ERR_CODE_CONFIG_FILE_ERROR = 0X04;
  CALIB_GWM_ERR_CODE_IMAGE_SIZE_MATCH_ERROR = 0X05;
  CALIB_GWM_ERR_CODE_VEHICLE_TYPE_ERROR = 0X06;
  CALIB_GWM_ERR_CODE_VEHICLE_STATUS_ERROR = 0X07;
  CALIB_GWM_ERR_CODE_FILE_OPEN_ERROR = 0X08;
  CALIB_GWM_ERR_CODE_TIMEOUT = 0X09;
  CALIB_GWM_ERR_CODE_TARGET_NOT_FOUND = 0X0A;
  CALIB_GWM_ERR_CODE_TARGET_NUM_WRONG = 0X0B;
  CALIB_GWM_ERR_CODE_REPROJECTION_ERR_OUT_OF_RANGE = 0X0C;
  CALIB_GWM_ERR_CODE_IMU_DATA_ERROR = 0X0D;
  CALIB_GWM_ERR_CODE_LIDAR_DATA_ERROR = 0X0E;
}

enum CalibrationGwmSingleErrCode {
  CALIB_GWM_SINGLE_ERR_CODE_SUCCESS = 0X00;
  CALIB_GWM_SINGLE_ERR_CODE_CORNER_DETECTION_ERROR = 0X01;
  CALIB_GWM_SINGLE_ERR_CODE_INTRINSIC_ERROR = 0X02;
  CALIB_GWM_SINGLE_ERR_CODE_IMAGE_SIZE_MATCH_ERROR = 0X03;
  CALIB_GWM_SINGLE_ERR_CODE_TARGET_NOT_FOUND = 0X04;
  CALIB_GWM_SINGLE_ERR_CODE_TARGET_NUM_WRONG = 0X05;
  CALIB_GWM_SINGLE_ERR_CODE_REPROJECTION_ERR_OUT_OF_RANGE = 0X06;
  CALIB_GWM_SINGLE_ERR_CODE_X_OUT_OF_RANGE = 0X07;
  CALIB_GWM_SINGLE_ERR_CODE_Y_OUT_OF_RANGE = 0X08;
  CALIB_GWM_SINGLE_ERR_CODE_Z_OUT_OF_RANGE = 0X09;
  CALIB_GWM_SINGLE_ERR_CODE_ROLL_OUT_OF_RANGE = 0X0A;
  CALIB_GWM_SINGLE_ERR_CODE_YAW_OUT_OF_RANGE = 0X0B;
  CALIB_GWM_SINGLE_ERR_CODE_PITCH_OUT_OF_RANGE = 0X0C;
  CALIB_GWM_SINGLE_ERR_CODE_IMU_DATA_ERROR = 0X0D;
  CALIB_GWM_SINGLE_ERR_CODE_LIDAR_DATA_ERROR = 0X0E;
  CALIB_GWM_SINGLE_ERR_CODE_FOV_OUT_OF_RANGE = 0X0F;
  CALIB_GWM_SINGLE_ERR_CODE_GET_INTRINSIC_PARAM_ERROR = 0X10;
  CALIB_GWM_SINGLE_ERR_CODE_EXTRINSIC_OUT_OF_RANGE = 0X11;
  CALIB_GWM_SINGLE_ERR_CODE_IMAGE_LOAD_ERROR = 0X12;
}

message CalibrationBoardInfo {
  int32 board_id = 1;
  dr.base.Point3D position = 2;
  dr.base.Point3D roll_pitch_yaw = 3;
  double board_horizontal_size = 4;
  bool has_board_horizontal_size = 5;
  double board_vertical_size = 6;
  bool has_board_vertical_size = 7;
}

message CalibrationSingleSensorState {
  string frame_id = 1;
  repeated CalibrationGwmSingleErrCode error_code = 2;
}

message CalibrationGwmErrState {
  CalibrationGwmErrCode gwm_error_code = 1;
  repeated CalibrationSingleSensorState single_sensor_state = 2;
}

message CalibrationSeresErrState {
  CalibrationErrorCode error_code = 1;
}

enum CalibrationCommonErrorCode {
  CALIB_COMM_ERR_CODE_UNKNOWN = 0x00;           // 未定义
  CALIB_COMM_ERR_CODE_SUCCESS = 0x01;           // 成功
  CALIB_COMM_ERR_CODE_TIMEOUT = 0x02;           // 超时
  CALIB_COMM_ERR_CODE_MILEAGE_EXCEEDED = 0x03;  /// 已达最大里程未收敛
  CALIB_COMM_ERR_CODE_INSUFFICIENT_VALID_MILEAGE = 0x04;  // 有效里程不足

  // vehicle status
  CALIB_COMM_ERR_CODE_WIPERS_OPEN = 0x100;          // 雨刮器未关
  CALIB_COMM_ERR_CODE_NOT_IN_N_GEAR = 0x101;        // 车辆不在N档
  CALIB_COMM_ERR_CODE_NOT_SATISFACTORY = 0x102;     // 车辆未静止
  CALIB_COMM_ERR_CODE_TRAILER_CONNECTED = 0x103;    // 挂车已连接
  CALIB_COMM_ERR_CODE_LIGHT_OPEN = 0x104;           // 车灯未关闭
  CALIB_COMM_ERR_CODE_TIRE_PRESSURE_ERROR = 0x105;  // 胎压异常
  CALIB_COMM_ERR_CODE_WHEEL_SPEED_ERROR = 0x106;    // 轮速异常
  CALIB_COMM_ERR_CODE_DRIVER_DOOR_OPEN = 0x107;     // 驾驶车门打开
  CALIB_COMM_ERR_CODE_DRIVER_REAR_DOOR_OPEN = 0x108;  // 驾驶员后部/推拉车门未关
  CALIB_COMM_ERR_CODE_PASSENGER_DOOR_OPEN = 0x109;  // 乘客座车门开启
  CALIB_COMM_ERR_CODE_REAR_PASSENGER_DOOR_OPEN =
      0x10A;  // 乘客后部/推拉车门未关
  CALIB_COMM_ERR_CODE_FRONT_TRUNK_OPEN = 0x010B;           // 前备箱未关
  CALIB_COMM_ERR_CODE_REAR_TRUNK_OPEN = 0x010C;            // 后备箱未关
  CALIB_COMM_ERR_CODE_DRIVER_MIRROR_FOLDED_UP = 0x10D;     // 驾驶员镜折起
  CALIB_COMM_ERR_CODE_PASSENGER_MIRROR_FOLDED_UP = 0x10E;  // 乘客镜折起
  CALIB_COMM_ERR_CODE_AIR_SUSPEN_NOT_NORMAL_GEAR = 0x10F;  // 空气悬挂非标准档
  CALIB_COMM_ERR_CODE_AIR_SUSPEN_NOT_INIT_POS = 0x110;  // 空气悬挂不在初始位置
  CALIB_COMM_ERR_CODE_NUMBER_OF_PASSENGER_ABNORMAL = 0x111;  // 车内人数不合要求

  // calibration target
  CALIB_COMM_ERR_CODE_INIT_TARGET_FAIL = 0x200;  // 初始化靶标失败
  CALIB_COMM_ERR_CODE_TARGET_NOT_FOUND = 0x201;  // 未检测到靶标
  CALIB_COMM_ERR_CODE_TARGET_NOT_ENOUGH = 0x202;  // 检测到靶标但数量不足
  CALIB_COMM_ERR_CODE_POINT_CLOUD_ON_TAGET_NOT_ENOUGH =
      0x203;  // 靶标点云数量少

  // config file
  CALIB_COMM_ERR_CODE_READ_CONFIG_FILE_ERROR = 0x300;  // 读取cfg文件失败
  CALIB_COMM_ERR_CODE_INTRINSIC_ERROR = 0x301;         // 相机内参错误
  CALIB_COMM_ERR_CODE_EXTRINSIC_ERROR = 0x302;         // 外参错误

  // save result
  CALIB_COMM_ERR_CODE_SAVE_RESULT_ERROR = 0x400;

  // calibration result
  CALIB_COMM_ERR_CODE_INIT_REPROJECTION_ERR_TOO_LARGE = 0x500;  // 初始误差过大
  CALIB_COMM_ERR_CODE_REPROJECTION_ERR_TOO_LARGE = 0x501;  // 重投影误差过大
  CALIB_COMM_ERR_CODE_INIT_STITCHING_ERR_TOO_LARGE = 0x502;  // 初始拼接误差过大
  CALIB_COMM_ERR_CODE_STITCHING_ERR_TOO_LARGE = 0x503;  // 拼接误差过大

  // install error
  CALIB_COMM_ERR_CODE_INSTALL_ERR_OUT_OF_RANGE = 0x600;  // 安装误差过大
  CALIB_COMM_ERR_CODE_INSTALL_ROLL_ERR_OUT_OF_RANGE = 0x601;  // 安装误差过大
  CALIB_COMM_ERR_CODE_INSTALL_PITCH_ERR_OUT_OF_RANGE = 0x602;  // 安装误差过大
  CALIB_COMM_ERR_CODE_INSTALL_YAW_ERR_OUT_OF_RANGE = 0x603;  // 安装误差过大
  CALIB_COMM_ERR_CODE_INSTALL_X_ERR_OUT_OF_RANGE = 0x604;  // 安装角误差过大
  CALIB_COMM_ERR_CODE_INSTALL_Y_ERR_OUT_OF_RANGE = 0x605;  // 安装角误差过大
  CALIB_COMM_ERR_CODE_INSTALL_Z_ERR_OUT_OF_RANGE = 0x606;  // 安装角误差过大

  // sensor data
  // lidar 700~70F
  CALIB_COMM_ERR_CODE_LIDAR_DATA_FRAME_LOSS = 0x700;   // 雷达点云丢帧
  CALIB_COMM_ERR_CODE_CAMERA_DATA_FRAME_LOSS = 0x701;  // 相机出图丢帧

  // imu 710~71F
  CALIB_COMM_ERR_CODE_IMU_DATA_FRAME_LOSS = 0x710;    // IMU丢帧
  CALIB_COMM_ERR_CODE_ACC_NOISE_TOO_LARGE = 0x711;    // ACC噪声过大
  CALIB_COMM_ERR_CODE_ACC_OFFSET_TOO_LARGE = 0x712;   // ACC零偏过大
  CALIB_COMM_ERR_CODE_GYRO_NOISE_TOO_LARGE = 0x713;   // GYRO噪声过大
  CALIB_COMM_ERR_CODE_GYRO_OFFSET_TOO_LARGE = 0x714;  // GYRO零偏过大

  // wheel speed 720~72F
  CALIB_COMM_ERR_CODE_WHEEL_SPEED_DATA_ABNORMAL = 0x720;  // WHEEL_SPEED数值异常
  CALIB_COMM_ERR_CODE_WHEEL_SPEED_DATA_FRAME_LOSS = 0x721;  // WHEEL_SPEED丢帧

  // wheel  pulse, 730~73F
  CALIB_COMM_ERR_CODE_WHEEL_PULSE_DATA_ABNORMAL = 0x730;  // 轮脉冲数值异常
  CALIB_COMM_ERR_CODE_WHEEL_PULSE_DATA_FRAME_LOSS = 0x731;  //轮脉冲丢帧
}

message CalibrationCommonSingleSensorState {
  string frame_id = 1;
  repeated CalibrationCommonErrorCode error_code = 2;
}

message CalibrationCommonErrorState {
  CalibrationCommonErrorCode error_code = 1;
  repeated CalibrationCommonSingleSensorState single_sensor_state = 2;
}

enum CalibrationState {
  CALIBRATION_STATE_UNKNOWN = 0x00;
  CALIBRATION_STATE_SUCCESS = 0x01;
  CALIBRATION_STATE_FAIL = 0x02;
  CALIBRATION_STATE_RUNNING = 0x03;
}
enum CalibrationStep {
  CALIBRATION_STEP_ONLINE_UNKNOWN = 0x00;
  CALIBRATION_STEP_ONLINE_RASMAP_INIT = 0x01;
  CALIBRATION_STEP_ONLINE_RASMAP_COLLECTING_DATA = 0x02;
  CALIBRATION_STEP_ONLINE_RASMAP_CALCULATING = 0x03;

  CALIBRATION_STEP_ONLINE_CAMERA = 0x10;
  CALIBRATION_STEP_ONLINE_LIDAR = 0x20;
  CALIBRATION_STEP_ONLINE_RADAR = 0x30;

  CALIBRATION_STEP_ONLINE_AVM = 0x40;
  CALIBRATION_STEP_ONLINE_INERTIAL_NAVIGATION = 0x50;
}

enum APSText {
  NO_REQUEST = 0;
  WRONG_GEAR_POSITION_TO_START_PARKING = 1;
  TURN_D_TO_SEARCH_FOR_SLOT = 2;
  PLEASE_CLOSE_THE_TRUNK = 3;
  PLEASE_CLOSE_THE_DOOR = 4;
  PLEASE_FASTEN_THE_SEAT_BELT = 5;
  PLEASE_UNFOLD_THE_MIRROR = 6;
  SPEED_IS_TOO_HIGH_NEED_23KM_H = 7;
  SYSTEM_ERROR_PARKING_SYS_EXITS = 8;
  CONTROL_UNIT_ERROR_PARKING_SYS_EXITS = 9;
  PLEASE_BRAKE = 10;
  PLEASE_SELECT_PARKING_MODE = 11;
  PLEASE_SELECT_INTERNAL_PARKING = 12;
  PLEASE_SELECT_SLOT_AND_PARKING_MODE = 13;
  PLEASE_SELECT_SLOT_AND_INTERNAL_PARKING = 14;
  PLEASE_KEEP_BRAKING = 15;
  HANDS_OFF_THE_STEERING_WHEEL = 16;
  RELEASE_THE_BRAKE_PARKING_START = 17;
  AUTO_PARKING_BE_READY_TO_BRAKE = 18;
  PARKING_SUSPENDED_CLOSE_TRUNK = 19;
  PARKING_SUSPENDED_CLOSE_DOOR = 20;
  PARKING_SUSPENDED_FASTEN_SEAT_BELT = 21;
  OBSTACLES_DETECTED = 22;
  BRAKE_RECOVERED_CONFIRM_TO_CONTINUE = 23;
  PARKING_QUITED = 24;
  PARKING_FINISHED = 25;
  INTERFERED_BY_DRIVER_PARKING_QUITED = 26;
  TOO_MANY_PARKING_TIMES_PARKING_SYS_EXITS = 27;
  PARKING_TIME_TOO_LONG_PARKING_SYS_EXITS = 28;
  TOO_SMALL_SLOT_PARKING_SYS_EXITS = 29;
  PLEASE_CLEAN_THE_CAMERA = 30;
  SELECT_THE_PARKING_MODE_APA3 = 31;
  SEARCHING_THE_RIGHT_SLOT = 32;
  SEARCHING_THE_LEFT_SLOT = 33;
  PLEASE_TURN_D_TO_SEARCH_FOR_PARKING_SLOT = 34;
  SPEED_IS_TOO_HIGH_NEED_30KM_H = 35;
  SPEED_IS_TOO_HIGH_SYS_EXITS = 36;
  SELECT_PARKING_TYPE = 37;
  LONG_PRESS_SWITCH_2S_TO_ENTER_AUTO_PARKING = 38;
  PARKING_SUSPENDED_PLEASE_RELEASE_THE_GAS_PEDAL = 39;
  PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_CONTINUE_AUTO_PARKING = 40;
  PARKING_IS_NOT_AVAILABLE = 41;
  PLEASE_SELECT_THE_PARKING_DIRECTION_BY_TURNING_THE_TURN_SWITCH = 42;
  PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_ENTER_POC_LEFT = 43;
  PARKING_SYS_EXITS = 44;
  PARKING_SUSPENDED_FOLD_THE_MIRROR = 45;
  TOO_SMALL_SLOT_PLEASE_SELECT_ANOTHER_DIRECTION = 46;
  DRIVING_MODE_NOT_SUPPORTED = 47;
  PATH_IS_SMOOTH = 48;
  PLEASE_SELECT_PARK_OUT = 49;
  PLEASE_SELECT_PARK_OUT_DIRECTION = 50;
  PLEASE_GET_OFF_THE_CAR_WITH_KEYS_TO_START_REMOTE_PARKING = 51;
  PLEASE_CONFIRM_TO_START_PARKING = 52;
  REMOTE_PARKING_IN_EXIT = 53;
  PARKING_SUSPENDED_REMOTE_CONNECTION_ERROR = 54;
  SYSTEM_ERROR_PARKING_SYS_CAN_NOT_START = 55;
  CONTROL_UNIT_ERROR_PARKING_SYS_CAN_NOT_START = 56;
  PLEASE_TURN_P_AND_APPLY_EPB_REMOTE_PARKING_SYS_NOT_RUNNING = 57;
  SELECT_REMOTE_PARKING_OUT_TYPE = 58;
  PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_ENTER_POC_RIGHT = 59;
  PLEASE_RELEASE_SWITCH_TO_EN_TER_GUIDANCE = 60;
  SLOPE_OVER_SYSTEM_EXITS = 61;
  PLEASE_SELECT_SUMMON_PATH = 62;
  RESERVED_63 = 63;
  PATH_STUDY_BEGIN_PLEASE_DRIVE_THE_CAR = 64;
  PATH_STUDYING = 65;
  CONFIRM_STUDY_FINISHED = 66;
  PATH_STUDYING_FAILED_CAUSED_BY_PATH_TOO_LONG = 67;
  PATH_STUDYING_FAILED_CAUSED_BY_SPEED_TOO_HIGH = 68;
  PATH_STUDYING_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 69;
  PATH_STUDYING_FAILED_CAUSED_BY_PATH_ERROR = 70;
  PATH_STUDYING_FAILED_CAUSED_BY_LEARN_TIME_TOO_LONG = 71;
  PATH_STUDYING_FAILED_CAUSED_BY_INTERNAL_ERROR = 72;
  PATH_STUDYING_FAILED_CAUSED_BY_PATH_TOO_SHORT = 73;
  PATH_STUDYING_FAILED_CAUSED_BY_PATH_BACKWARD_TOO_LONG = 74;
  PATH_STUDYING_FAILED_CAUSED_BY_SHIFT_GEAR_TOO_MANY_TIMES = 75;
  SPEED_IS_TOO_HIGH_NEED_5KM_H = 76;
  SPEED_IS_TOO_HIGH_NEED_10KM_H = 77;
  SPEED_IS_TOO_HIGH_NEED_15KM_H = 78;
  RESERVED_79 = 79;
  PATH_SAVING = 80;
  PATH_SAVING_SUCCEED = 81;
  PATH_SAVING_FAILED_CAUSED_BY_PATH_TOO_SHORT = 82;
  PATH_SAVING_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 83;
  PATH_SAVING_FAILED_CAUSED_BY_PATH_ERROR = 84;
  PATH_SAVING_FAILED_CAUSED_BY_PATH_ERROR_LIMIT_STORAGE_ROOM = 85;
  PATH_SAVING_FAILED_CAUSED_BY_INTERNAL_ERROR = 86;
  RESERVED_87 = 87;
  PLEASE_DRIVE_TO_PATH_START_POINT = 96;
  PATH_MATCHE_PLEASE_STOP = 97;
  PLEASE_SELECT_PLAY_BACK_MODE = 98;
  PATH_MATCH_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 99;
  PATH_MATCH_FAILED_CAUSED_BY_PATH_ERROR = 100;
  PATH_MATCH_FAILED_CAUSED_BY_LONG_TIME_NO_ACTION = 101;
  PATH_MATCH_FAILED_CAUSED_BY_INTERNAL_ERROR = 102;
  THE_MAXIMUN_PATH_LENGTH_IS_ABOUT_TO_BE_EXCEEDED = 103;
  AUTO_PARKING_BE_READY_TO_BREAK = 104;
  MAP_DATA_ABNORMAL_SYSTEM_EXIT = 105;
  RESERVED_106 = 106;
  NO_SAVE_DISTANCE_RADS_SYS_ESITS = 112;
  RADS_RUNNING_BE_READY_TO_BRAKE = 113;
  NEAR_THE_BOURN = 114;
  ARRIVE_AT_THE_BOURN = 115;
  RELEASE_THE_BRAKE = 116;
  TRUNK_OPEN_RADS_SYS_EXITS = 117;
  DOOR_OPEN_RADS_SYS_EXITS = 118;
  SEAT_BELT_DROP_RADS_SYS_EXITS = 119;
  MIRROR_FOLD_RADS_SYS_EXITS = 120;
  OBSTACLES_DETECTED_RADS_SYS_EXITS = 121;
  NO_CONTINUE_DRIVING_PARKING_QUITED = 122;
  PLEASE_TURN_TO_R = 123;
  RELEASE_THE_BRAKE_DRIVING_ASSISTANCE_START = 124;
  PLEASE_TURN_TO_D = 125;
  RADS_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 126;
  RADS_RECOVERED_CONFIRM_TO_CONTINUE = 127;
  FADS_RUNNING_BE_READY_TO_BRAKE = 128;
  TRUNK_OPEN_FADS_SYS_EXITS = 129;
  DOOR_OPEN_FADS_SYS_EXITS = 130;
  SEAT_BELT_DROP_FADS_SYS_EXITS = 131;
  MIRROR_FOLD_FADS_SYS_EXITS = 132;
  OBSTACLES_DETECTED_FADS_SYS_EXITS = 133;
  VEHICLE_CAN_NOT_MOVE_FADS_SYS_EXITS = 134;
  REMOTE_CONTROL_STRAIGHT_SEARCH = 135;
  OVER_LIMIT_SEARCH_DISTANCE = 136;
  PLEASE_SELECT_SUITABLE_PARKING_SLOT_ICON = 137;
  PLEASE_DRAG_THE_PARKING_SLOT_TO_SUITABLE_PLACE_AND_ADJUST_THE_ANGLE = 138;
  MOVE_OVER_25M_SYSTEM_EXITS = 139;
  MOVE_OVER_5M_SYSTEM_EXITS = 140;
  PARKING_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 141;
  RADS_SUSPENDED_DETECTE_OBSTACLES = 142;
  FADS_SUSPENDED_DETECTE_OBSTACLES = 143;
  FADS_RECOVERED_CONFIRM_TO_CONTINUE = 144;
  FRONT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 145;
  REAR_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 146;
  LEFT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 147;
  RIGHT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 148;
  DVR_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 149;
  FADS_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 150;
  PLEASE_CLOSE_CABIN_COVER = 151;
  PLEASE_PULL_OUT_THE_CHARGING_GUN = 152;
  WOULD_YOU_LIKE_TO_PARK_IN = 153;
  YOU_CAN_SELECT_SLOT_AND_PARK_IT = 154;
  PAY_ATTENTION_TO_THE_RISK_OF_SCRATCHES = 155;
  DETECTING_PARKING_SPACE = 156;
  PLEASE_READY = 157;
  GUIDE_SUCCESSFUL_PLEASE_START_CHARGING = 158;
  WIRELESS_CHARGING_GUIDANCE_POSITIONING = 159;
  WIRELESS_CHARGING_ALIGNED_PARKING_COMPLETE = 160;
  PARKING_SUSPENDED_PEDESTRIAN_DETECTED = 161;
  PARKING_SUSPENDED_VEHICLE_DETECTED = 162;
  PARKING_CONTINUES = 163;
  PARKING_OUT_COMPLETED_PLEASE_TAKE_OVER_VEHICLE = 164;
  ENVIRONMENT_CANNOT_BE_PARKED = 165;
  PUT_ON_THE__P__PULL_UP_EPB_CLOSE_DOOR_FOR_REMOTE_PARKING = 166;
  CONNECTION_ABNORMAL_PARKING_CANNOT_BE_STARTED = 167;
  REMOTE_PARKING_BE_READY_TO_STOP = 168;
  SELECT_PARKING_OUT_MODE = 169;
  PARKING_SUSPENDED_CLOSE_CABIN_COVER = 170;
  RADS_SUSPENDED_LOOSEN_ACCELERATOR_PEDAL = 171;
  RADS_SUSPENDED_CLOSE_CABIN_COVER = 172;
  STEERING_WHEEL_INTERVENTION_RADS_EXITS = 173;
  EPB_INTERVENTION_RADS_EXITS = 174;
  GEAR_INTERVENTION_RADS_EXITS = 175;
  SPEED_TOO_HIGH_RADS_EXITS = 176;
  TIME_OUT_RADS_EXITS = 177;
  TOO_MANY_TIMES_RADS_EXITS = 178;
  TRUNK_OPEN_RADS_SYS_SUSPEND = 179;
  DOOR_OPEN_RADS_SYS_SUSPEND = 180;
  SEAT_BELT_DROP_RADS_SYS_SUSPEND = 181;
  MIRROR_FOLD_RADS_SYS_SUSPEND = 182;
  // OBSTACLES_DETECTED = 183;
  DETECTED_OBSTACLES = 184;
  AVOID_NEARBY_OBSTACLES = 185;
  RADS_EXIT = 186;
  SLOT_SMALL_USE_RPA = 187;
  CAMERA_FAULT_SYS_EXITS = 188;
  CAMERA_BLOCK_SYS_EXITS = 189;
  SLOPE_OVER_SYS_UNAVAILABLE = 190;
  OTHER_ASSISIT_ACTIVATE_SYS_EXITS = 191;
  RADAR_SYSTEM_FAILURE_SYS_UNAVAILABLE = 192;
  PARKING_SYSTEM_FAILURE_SYS_UNAVAILABLE = 193;
  SPEED_IS_TOO_HIGH_SYS_UNAVAILABLE = 194;
  CONTROL_UNIT_ERROR_SYS_UNAVAILABLE = 195;
  AVM_NOT_CALIBRATEDE_SYS_UNAVAILABLE = 196;
  CAMERA_FAILURE_SYS_UNAVAILABLE = 197;
  OTHER_AUXILIARY_DRIVING_FUNCTIONS_ARE_ACTIVATED_SYS_UNAVAILABLE = 198;
  ACTIVE_SECURITY_FEATURE_ACTIVATION_SYS_UNAVAILABLE = 199;
  CAMERA_DIRTY_SYS_UNAVAILABLE = 200;
  CAMERA_IS_BLOCKED_SYS_UNAVAILABLE = 201;
  DRIVING_MODE_NOT_SUPPORTED_SYS_UNAVAILABLE = 202;
  PATH_IS_UNOBSTRUCTED_SYS_UNAVAILABLE = 203;
  SINGLE_CALL_EXCEEDS_THE_LIMIT_DISTANCE = 204;
  ENCOUNTER_OBSTACLES_LATERALLY_PARKING_SUSPENSION = 205;
  PARKING_SYSTEM_FAILURE_PLEASE_TAKE_OVER_THE_VEHICLE_IN_TIME = 206;
  TRUNK_OPEN_FADS_SYS_SUSPEND = 207;
  DOOR_OPEN_FADS_SYS_SUSPEND = 209;
  SEAT_BELT_DROP_FADS_SYS_SUSPEND = 210;
  MIRROR_FOLD_FADS_SYS_SUSPEND = 211;
  FADS_EXIT = 212;
  FADS_SUSPENDED_LOOSEN_ACCELERATOR_PEDAL = 213;
  SPEED_TOO_HIGH_FADS_EXITS = 214;
  SLOPE_EXCEEDING_LIMIT_PLEASE_LEAVE_THE_RAMP = 215;
  VEHICLE_NOT_READY_PARKING_SYSTEM_EXIT = 216;
  RESERVED_217 = 217;

  // DR
  PLEASE_STANDSTILL = 0x1110;                  //请踩停车辆，激活功能
  PLEASE_CLOSE_THE_HOOD = 0x1111;              // search --> suspend
  PLEASE_CLOSE_THE_FUELCAP = 0x1112;           // search --> suspend
  PLEASE_CLOSE_THE_CHARGINGCAP = 0x1113;       // search --> suspend
  HOOD_OPEN_RADS_SYS_SUSPEND = 0x1114;         // active --> suspend
  FUELCAP_OPEN_RADS_SYS_SUSPEND = 0x1115;      // active --> suspend
  CHARGINGCAP_OPEN_RADS_SYS_SUSPEND = 0x1116;  // active --> suspend
  THROTTLE_OVERRIDE_RADS_EXITS = 0x1117;       // active --> standby
  SUSPEND_TIMEOUT_RADS_EXIT =
      0x1118;  // active_suspend --> standby，在active_suspend状态下发
  RESUME_CONFIRM_TIMEOUT_RADS_EXIT =
      0x1119;  // resume_confirm --> standby，在resume_confirm状态下发
}

enum APSSysSoundIndcn {
  NO_WARNING = 0;
  APS_FAILED_TONE = 1;
  APS_SUCCESSFUL_TONE = 2;
  APS_WARNING_TONE = 3;
  APS_REQUEST_TONE = 4;
}

enum CalibrationErrorCode {
  CALIB_ERR_CODE_UNKNOWN = 0x00;
  CALIB_ERR_CODE_STATIC_CAMERA_ROLL_CALIB_RUNNING = 0x06;
  CALIB_ERR_CODE_INS_CALIB_NOT_COMPLETED = 0xA7;
  CALIB_ERR_CODE_INSUFFICIENT_VALID_MILEAGE = 0xA8;
  CALIB_ERR_CODE_STEERING_WHEEL_NOT_RETURN_CENTER = 0xA9;
  CALIB_ERR_CODE_DOOR_OR_TRUNK_NOT_CLOSED = 0xAA;
  CALIB_ERR_CODE_REARVIEW_MIRROR_NOT_UNFOLDED = 0xAB;
  CALIB_ERR_CODE_WIPERS_NOT_TURNED_OFF = 0xAC;
  CALIB_ERR_CODE_NUMBER_OF_PEOPLE_IN_VEHICLE_ABNORMAL = 0xAD;
  CALIB_ERR_CODE_AIR_SUSPENSION_NOT_IN_STANDARD_GEAR = 0xAE;
  CALIB_ERR_CODE_NOT_IN_N_GEAR = 0xAF;
  CALIB_ERR_CODE_NOT_SATISFACTORY = 0xB8;
  CALIB_ERR_CODE_MMWAVE_NO_CALIB_TARGET = 0xB9;
  CALIB_ERR_CODE_MMWAVE_NOT_DETECT_TARGET = 0xBA;
  CALIB_ERR_CODE_MMWAVE_SELF_VEHICLE_MOTION = 0xBB;
  CALIB_ERR_CODE_MMWAVE_PITCH_ANGLE_FLUCTUATES_GREATLY = 0xBC;
  CALIB_ERR_CODE_MMWAVE_YAW_ANGLE_FLUCTUATES_GREATLY = 0xBD;
  CALIB_ERR_CODE_MMWAVE_TARGET_RCS_ABNORMAL = 0xBE;
  CALIB_ERR_CODE_MMWAVE_TARGET_DISTANCE_ABNORMAL = 0xBF;
  CALIB_ERR_CODE_VEHICLE_NOT_STATIONARY = 0xC8;
  CALIB_ERR_CODE_TWO_TARGETS_TOO_CLOSE = 0xC9;
  CALIB_ERR_CODE_TARGET_IN_LOWER_PART_OF_IMAGE = 0xCA;
  CALIB_ERR_CODE_TARGET_IN_UPPER_PART_OF_IMAGE = 0xCB;
  CALIB_ERR_CODE_TARGET_IN_RIGHT_PART_OF_IMAGE = 0xCC;
  CALIB_ERR_CODE_TARGET_IN_LEFT_PART_OF_IMAGE = 0xCD;
  CALIB_ERR_CODE_ONLY_ONE_QR_CODE_IN_TARGET = 0xCE;
  CALIB_ERR_CODE_NO_TARGET = 0xCF;
  CALIB_ERR_CODE_RECEIVED_SYSTEM_FAULT = 0xD8;
  CALIB_ERR_CODE_SIDE_VIEW_CAMERA_INSTALLED_BACKWARDS = 0xD9;
  CALIB_ERR_CODE_CAMERA_TRANSLATION_ABNORMAL = 0xDA;
  CALIB_ERR_CODE_CAMERA_ID_ABNORMAL = 0xDB;
  CALIB_ERR_CODE_CAMERA_NUMBER_ABNORMAL = 0xDC;
  CALIB_ERR_CODE_FISHEYE_CAMERA_VEHICLE_MOVE = 0xDD;
  CALIB_ERR_CODE_NUMBER_OF_DETECTED_TARGETS_TOO_SMALL = 0xDE;
  CALIB_ERR_CODE_CAMERA_LENS_OR_TARGET_BLOCKED = 0xDF;
  CALIB_ERR_CODE_LIDAR_CALIB_NO_TARGET_DETECTED = 0xE7;
  CALIB_ERR_CODE_NO_STATIC_CALIB_RESULT = 0xE9;
  CALIB_ERR_CODE_INS_CALIB_RUNNING = 0xF4;
  CALIB_ERR_CODE_LIDAR_DATA_ABNORMAL = 0xF7;
  CALIB_ERR_CODE_NO_SENSOR_DATA = 0xF8;
  CALIB_ERR_CODE_TIMEOUT = 0xF9;
  CALIB_ERR_CODE_MILEAGE_EXCEEDED = 0xFA;
  CALIB_ERR_CODE_TARGET_DETECTION_OFFSET_ABNORMAL = 0xFC;
  CALIB_ERR_CODE_NO_INITIAL_CONFIG_FILE = 0xFD;
  CALIB_ERR_CODE_NO_INITIAL_PARAMETER = 0xFE;

  // self defined error code, 0x1000~0x10FF
  CALIB_ERR_CODE_DATA_NOT_ENOUGH = 0x1000;
  CALIB_ERR_CODE_LIDAR_CALIB_NOT_CONVERGENT = 0x1001;
  CALIB_ERR_CODE_CAMERA_CALIB_NOT_CONVERGENT = 0x1002;
  CALIB_ERR_CODE_RADAR_CALIB_NOT_CONVERGENT = 0x1003;
  CALIB_ERR_CODE_INS_CALIB_NOT_CONVERGENT = 0x1004;
}

// RPA
message RPAStateRPA {
  // 参考　RPA SDK 接口定义, RPAState::Rpa
  int32 power_request = 1;  // 遥控泊车上下电请求状态 ADS_PwrOnOffReq
  int32 park_auth = 2;      // RPA泊车授权: ADS_RpaParkAuthn
  int32 park_state = 3;     // 泊车状态 ADS_ParkStMac
  int32 progress = 4;       //泊车进度百分比 ADS_ParkProgs
  int32 remaining_distance = 5;  // 泊车剩余距离,单位分米。ADS_ParkRmngDst
  int32 obstacle_direction = 6;  // 障碍物方向
  int32 obstacle_distance = 7;   // 障碍物距离: ADS_ObstclDst
  int32 event = 8;               //  自动驾驶事件 ADS_AutEve
  int32 mode = 9;  // 自车泊入模式（车头、车尾泊入）ADS_VehParkMod
  int32 remote_park_sel = 10;  // 远程泊车模式选择确认
  int32 park_no_sel = 11;      // 车位ID ADS_SeldParkID
  int32 park_out_dirs = 12;    // 可用的泊出方向
  // 下方数据参考<<X1 23 RPA信号交互_V1.2_20230419.xlsx>>
  uint32 sig_apa_resu_aval = 13;  // RPA功能是否可以恢复泊车 ADS_APA_ResuAvl
  uint32 sig_paused_time_left = 14;  // RPA暂停倒计时 ADS_parkInterruptDuration
  uint32 sig_rpa_park_auth = 15;    // RPA泊车授权 ADS_RpaParkAuthn
  uint32 sig_park_st_machine = 16;  // 泊车状态机 ADS_ParkStMac
  uint32 sig_park_dir_cfm = 17;     // RPA泊出方向确认 ADS_ParkDirCfm
  uint32 sig_park_state = 18;       // 泊车状态信号　ADS_ParkStMac
}

message RPAStateVehicle {
  // 参考　RPA SDK 接口定义, RPAState::Vehicle
  int32 speed = 1;                          // 车辆行驶速度,单位km/h
  int32 gear = 2;                           //   车辆档位
  int32 front_left_locl_status = 3;         // 主驾车锁状态
  int32 steering_angle = 4;                 // 方向盘转角度数
  int32 bcm_power_state = 5;                // BCM电源状态
  int32 bms_auth_state = 6;                 // 防盗认证状态:
  uint32 fronte_left_door_lock_status = 7;  // Frontleftdoorlockstatus
}

message RPAParkingSpot {
  // 参考　RPA SDK 接口定义, RPAState::ParkingSpot
  uint32 type = 1;  // 目标车位类型
  uint32 x1 =
      2;  // 车位左上顶点相对车辆水平方向距离,单位为分米,车辆位于顶点右侧为正值,否则为负值。
  uint32 y1 =
      3;  // 车位左上顶点相对车辆垂直方向距离,单位为分米,车辆位于顶点北向为正值,否则为负值。
  uint32 x2 =
      4;  // 车位右上顶点相对车辆水平方向距离,单位为分米,车辆位于顶点右侧为正值,否则为负值。
  uint32 y2 =
      5;  // 车位右上顶点相对车辆垂直方向距离,单位为分米,车辆位于顶点北向为正值,否则为负值。
}

message RPABusinessState {
  // 参考　RPA SDK 接口定义
  RPAStateRPA rpa_state = 1;
  RPAStateVehicle vehicle_state = 2;
  RPAParkingSpot parking_spot = 3;
}

enum ActiveResult {
  ACTIVE_RESULT_UNKNOW = 0;
  ACTIVE_FAILED = 1;       //激活失败
  ACTIVE_TIMEOUT = 2;      //激活超时
  ACTIVE_INTERRUPTED = 3;  //激活被打断
  ACTIVE_SUCCESS = 4;      //激活成功
}

enum LaneChangeRqResult {
  LANE_CHANGE_RQ_RESULT_UNKNOWN = 0;
  LANE_CHANGE_RQ_FAILED = 1;
  LANE_CHANGE_RQ_TIMEOUT = 2;
  LANE_CHANGE_RQ_INTERRUPTED = 3;
  LANE_CHANGE_RQ_SUCCESS = 4;
}

message GWMLSSWarningDebugInfo {
  enum LDWLKAWarnType {
    LDW_LKAWARN_NO_LDW_LKA_WARNING = 0;
    LDW_LKAWARN_LDW_LKA_LEFT_WARNING = 1;
    LDW_LKAWARN_LDW_LKA_RIGHT_WARNING = 2;
    LDW_LKAWARN_RESERVED_3 = 3;
  }
  enum LSSInterventionType {
    LSSINTERVENTION_NO_WARNING_AND_INTERVENTION = 0;
    LSSINTERVENTION_LEFT_WARNING = 1;
    LSSINTERVENTION_RIGHT_WARNING = 2;
    LSSINTERVENTION_LEFT_INTERVENTION = 3;
    LSSINTERVENTION_RIGHT_INTERVENTION = 4;
    LSSINTERVENTION_BOTH_INTERVENTION = 5;
    LSSINTERVENTION_LEFT_WARN_ONLY_REMINDER = 6;
    LSSINTERVENTION_RIGHT_WARN_ONLY_REMINDER = 7;
  }
  int32 cycle_time_left_seconds = 1;     // 当前计时周期剩余时间
  double warning_time_left_seconds = 2;  // 报警声音剩余时间
  int32 warning_count = 3;               // 纠偏发生次数(计数次数)
  int32 statemachine_status = 4;  // 内部告警状态机状态(blc 调试使用)
  bool is_muted = 5;              // 是否处于静音状态
  bool is_planning_ignore_control = 6;  // planning 是否处于忽略控车
  LDWLKAWarnType ldw_lka_warn = 7;
  LSSInterventionType lss_intervention = 8;
}

message SentryModeStatistic {
  int32 sentinel_alarm_count = 1;
}