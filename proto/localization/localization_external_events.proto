syntax = "proto3";

package deeproute.localization.event;

import "semantic_map/map_standby_area.proto";
import "common/geometry.proto";
import "localization/havp_map.proto";

/***  Begin of VPA Testing ****/
enum MatchingStatus {
  SEARCHING = 0;
  MATCHING = 1;
  MATCHED = 2;
  NO_MAP = 3;
  ROUTING_NOT_READY = 4;  // GWM SOP+3 deprecated
  NEAR_DESTINATION = 5;   // GWM SOP+3 deprecated
}

// report map localization module status event
message MapMatchingStatusEvent {
  string map_id = 1;
  MatchingStatus status = 2;

  deeproute.hdmap.StandbyArea parking_space = 3; // in odometry frame

  deeproute.common.Transformation3 odometry_to_map_transformation = 4;
  int32 current_floor_id = 5;
  repeated string nearby_map_id = 6;
  bool has_default_parking_space = 7;  // 匹配到的地图中有默认车位则为true，如果没有默认车位则是false
  bool is_expand_map_available = 8; // 匹配到的是否是可扩建
}

message NavigationStatusEvent {
  enum NavigationStatus {
    NAVIGATION_READY = 0;                     // deprecated
    NAVIGATION_ONGOING = 1;
    NAVIGATION_FINISHED = 2;
    NAVIGATION_PASSIVE = 3;
    NAVIGATION_YAWED_U_TURN = 4;
    NAVIGATION_YAWED_AWAY_FROM_ROAD = 5;
    NAVIGATION_UNEXPECTED = 6;
  }

  enum RoutingStatus {
    ROUTING_READY = 0;
    ROUTING_ONGOING = 1;
    ROUTING_PASSIVE_OUT_OF_NAVIGATION = 2; // not in navigation
    ROUTING_PASSIVE_U_TURN = 3; // vehicle heading and path heading has large diff
    ROUTING_PASSIVE_AWAY_FROM_ROAD = 4; // vehicle is away from centerline
  }

  NavigationStatus navigation_status = 1;
  RoutingStatus routing_status = 2;
  int32 target_parking_space_id = 3;
  repeated havp_map.HAVPTrajectory global_path = 4; // 导航界面和巡航过程中的全局路径规划 // TODO define lane-like structure
  float route_length = 5;
  float route_remaining_length = 6;
  float current_speed_limit = 7;
  float next_speed_limit = 8;
  double lost_distance_with_target_parking_space = 9;
  bool navigation_ready = 10;
  float next_speed_limit_remaining_s = 11;
}

enum MappingStatus {
  // Mapping Ongoing Status
  ONGOING = 0;                 // Mapping is ongoing without any issues
  FAIL = 1;                    // Mapping in abnormal state, should be turned off

  // Standby Status
  READY = 10;                 // Currently not in mapping or routing mode
  PRE_MAPPING = 11;           // pre mapping is ongoing

  // Not Ready Status
  IN_ROUTING = 20;             // Currently in routing mode, mapping is not supported in this mode
  GNSS_NOT_READY = 21;         // Currently gnss pose is not ready
  ROAD_LEVEL_OUT_OF_ODD = 22;  // Current road level is out of odd, e.g freeway
  GNSS_SIGNAL_LOST = 23;       // Currently no gnss signal
  MAP_NUMBER_REACH_MAX = 24;     // Map number over the threshold for SMART
  REACH_MAX_EXPANSION_DISTANCE = 25; // Matched map is over max distance, can't be expanded
}

enum MappingErrorCode {
  LOOP_DETECTED = 0;
  REVERSING_BEFORE_PARKING = 1;
  MULTI_LEVEL = 2;
  BAD_QUALITY_TRAJECTORY = 3;
}

// report mapping module status event
message MappingStatusEvent {
  MappingStatus status = 1;
  double mapping_distance = 2;
  MappingErrorCode error_code = 3;                   // deprecated
  int32 passed_speedbump_count = 4;
  repeated common.Point3D vehicle_trajectory = 5;    // deprecated
  double reverse_distance = 6;

  repeated int32 matched_map_ids = 7;
}

// report current nearby environment status event
message EnvironmentStatusEvent {
  enum EnvironmentInfo {
    
    // Current Environment Status
    VEHILCE_IN_FLAT = 0;
    VEHICLE_IN_UPHILL = 1;
    VEHILCE_IN_DOWNHILL = 2;

    VEHICLE_PASSING_SPEEDBUMP = 3;
    VEHICLE_REVERSING = 4; // deprecated
    VEHICLE_IN_PARKING_SPACE = 5;

    VEHICLE_INDOOR = 10;
    VEHICLE_OUTDOOR = 11;

    // Environment Status Ahead
    FLAT_AHEAD = 50;
    UPHILL_AHEAD = 51;
    DOWNHILL_AHEAD = 52;

    LEFT_TURN_AHEAD = 53;
    RIGHT_TURN_AHEAD = 54;
    STRAIGHT_AHEAD = 55;
  }
  repeated EnvironmentInfo environment_info = 1;
}

/***  End of VPA Testing ****/

message LocalizationEvent {  // localization modules send 'LocalizationEvent'
                             // message to external users use
                             // '/localization/event' topic
  string event_id = 1;
  double timestamp_ms = 2;

  MapMatchingStatusEvent map_matching_status_event = 3;
  MappingStatusEvent mapping_status_event = 4;
  EnvironmentStatusEvent environment_status_event = 5;
  NavigationStatusEvent navigation_status_event = 6;
}
