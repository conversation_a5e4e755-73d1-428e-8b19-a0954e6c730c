package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "havp_map_proto",
    visibility = ["//visibility:public"],
    srcs = ["havp_map.proto"],
    deps = [
        "//proto/perception:deeproute_perception_ras_map_proto",
        "//proto/perception:deeproute_perception_obstacle_proto",
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "havp_map_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":havp_map_proto",
    ],
)

python_proto_library(
    name = "havp_map_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":havp_map_proto",
    ],
)

proto_library(
    name = "ground_truth_measurements_proto",
    visibility = ["//visibility:public"],
    srcs = ["ground_truth_measurements.proto"],
    deps = [
        "//proto/drivers/gnss:ins_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "ground_truth_measurements_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":ground_truth_measurements_proto",
    ],
)

python_proto_library(
    name = "ground_truth_measurements_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":ground_truth_measurements_proto",
    ],
)

proto_library(
    name = "localization_internal_messages_proto",
    visibility = ["//visibility:public"],
    srcs = ["localization_internal_messages.proto"],
    deps = [
        "//proto/common:geometry_proto",
        "//proto/drivers:pointcloud2_proto",
        "//proto/perception:deeproute_perception_ras_map_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "localization_internal_messages_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":localization_internal_messages_proto",
    ],
)

python_proto_library(
    name = "localization_internal_messages_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":localization_internal_messages_proto",
    ],
)

proto_library(
    name = "localization_external_events_proto",
    visibility = ["//visibility:public"],
    srcs = ["localization_external_events.proto"],
    deps = [
        "//proto/semantic_map:map_standby_area_proto",
        "//proto/common:geometry_proto",
        "//proto/localization:havp_map_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "localization_external_events_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":localization_external_events_proto",
    ],
)

python_proto_library(
    name = "localization_external_events_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":localization_external_events_proto",
    ],
)

proto_library(
    name = "localization_external_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["localization_external_command.proto"],
    deps = [
        "//proto/common:geometry_proto",
        "//proto/semantic_map:map_standby_area_proto",
        "//proto/localization:localization_external_events_proto",
        "//proto/perception:deeproute_perception_ras_map_proto",
        "//proto/map:deeproute_map_ras_map_plus_proto",
        "//proto/localization:havp_map_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "localization_external_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":localization_external_command_proto",
    ],
)

python_proto_library(
    name = "localization_external_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":localization_external_command_proto",
    ],
)

