syntax = "proto3";

package deeproute.localization.command;

import "common/geometry.proto";
import "semantic_map/map_standby_area.proto";
import "localization/localization_external_events.proto";
import "perception/deeproute_perception_ras_map.proto";
import "map/deeproute_map_ras_map_plus.proto";
import "localization/havp_map.proto";

/*** Begin of VPA Test ***/

enum LocalizationMode {
  ODOMETRY = 0;
  LIO = 1;
  MAP_REGISTRATION = 2;
}

enum ErrorCode {
  // General status (0 ~ 99)
  UNSET = 0;                                          // deprecated             
  SUCCESS = 1;                                        // SUCCESS with no error
  FAIL = 2;                                           // deprecated
  VPA_CMD_STATUS_NOT_MATCH = 3;                       // blc cmd doesn't match current status 

  // VPA END_MAPPING related error code (100 ~ 199)
  VPA_MAPPING_LOOP_DETECTED = 100;                     // deprecated
  VPA_MAPPING_REVERSING_BEFORE_PARKING = 101;          // deprecated
  VPA_MAPPING_MULTI_LEVEL = 102;                       // deprecated
  VPA_MAPPING_BAD_QUALITY_TRAJECTORY = 103;            // deprecated
  VPA_MAPPING_NOT_IN_MAP = 104;                        // deprecated

  VPA_END_MAPPING_ADC_NOT_IN_PARKING_SPACE = 105;
  VPA_END_MAPPING_LEARNED_TRAJECORY_TOO_SHORT = 106;
  VPA_END_MAPPING_NO_PARKING_SPACE_NEARBY = 107;
  VPA_END_MAPPING_LEARNED_TRAJECORY_TOO_LONG = 108;
  VPA_END_MAPPING_LEARNED_TRAJECTORY_REACH_MAX_LIMITS = 109;
  VPA_END_MAPPING_TRAJECTORY_TOO_STEEP = 110;
  VPA_END_MAPPING_TRAJECTORY_ENVIRONMENT_TOO_SPARSE = 111;
  VPA_END_MAPPING_TRAJECTORY_REVERSE_TOO_FAR = 112;
  VPA_END_MAPPING_TRAJECTORY_NOT_REACH_PARKING_SPACE = 113;
  VPA_END_MAPPING_TRAJECTORY_CROSS_TARGET_PARKING_SPACE = 114;
  VPA_END_MAPPING_TRAJECTORY_LOOP_DETECTED = 115;
  VPA_END_MAPPING_MAPPING_TIME_TOO_LONG = 116;
  VPA_END_MAPPING_MAPPING_SAVE_FAILURE = 117;
  VPA_END_MAPPING_NOT_ENOUGH_DISK_FREE_SPACE = 118;
  VPA_END_MAPPING_INPUT_DATA_INVALID = 119;
  VPA_END_MAPPING_INDOOR_TO_OUTDOOR_MAPPING = 120;                      // mapping from indoor to outdoor
  VPA_END_MAPPING_GNSS_POSE_JUMP = 121;                                  // gnss yaw jump check failed
  VPA_END_MAPPING_MOVE_BACK_TO_START = 122;                             // move to start again
  VPA_END_MAPPING_NO_DESTINATION_CANDIDATES = 123;                      // neither have given parking space, nor have destination candidates like reverse_time_pose or apa_start_time_pose
  VPA_END_MAPPING_NO_VALID_DESTINATION_IN_CANDIDATES = 124;             // all destination candidates are invalid after check
  VPA_END_MAPPING_OUT_OF_PARKING_ODD = 125;

  VPA_END_MAPPING_UNKNOWN_REASON_FAILURE = 199;

  // VPA start mapping related error code (200 ~ 299)
  VPA_START_MAPPING_GNSS_NOT_READY = 200;
  VPA_START_MAPPING_GNSS_TOO_OLD = 201;
  VPA_START_MAPPING_LOCAL_POSE_NOT_READY = 202;

  // VPA start routing related error code (300 ~ 349)
  VPA_START_ROUTING_NOT_MATCHED = 301;
  VPA_START_ROUTING_NEAR_DESTINATION = 302;
  VPA_START_ROUTING_ABNORMAL = 303;

  // VPA end routing related error code (350 ~ 399)
}

message LocalizationModeChangeCMD {
  LocalizationMode localization_mode = 1;
}
message LocalizationModeChangeCMDRP {
  bool success = 1;
  string semantic_map_path = 2;                  // deprecated
  deeproute.hdmap.StandbyArea parking_space = 3; // deprecated
}

// It's actually for start routing signal notification
message StartMapLocalizationCMD {               // GWM SOP+3 deprecated
}
message StartMapLocalizationCMDRP {             // GWM SOP+3 deprecated
  string semantic_map_path = 1;
  deeproute.hdmap.StandbyArea parking_space = 2;  // in odometry frame
}

message EndMapLocalizationCMD {                 // GWM SOP+3 deprecated
}
message EndMapLocalizationCMDRP {               // GWM SOP+3 deprecated
}

message StartMappingCMD {
  bool expand_map = 1;
}
message StartMappingCMDRP {
}

enum CancelMappingType {
  USER_REQUEST = 0;
  MAPPING_FAILURE = 1;
  OTHERS = 2;
}

message CancelMappingCMD {
  CancelMappingType cancel_mapping_type = 1;
}
message CancelMappingCMDRP {
  ErrorCode error_code = 1; // if cancel mapping due to mapping failure, here put the failure reason
}

enum MappingErrorCode {
  LOOP_DETECTED = 0;
  REVERSING_BEFORE_PARKING = 1;
  MULTI_LEVEL = 2;
  BAD_QUALITY_TRAJECTORY = 3;
}

message ChangedMapStatus {
  int32 map_id = 1;
  OperatingType operation_type = 2;
}

message ChangedMapStatusList {
  repeated ChangedMapStatus map_status = 1;
}

message EndMappingCMD {
  perception.ParkingSpace parking_space = 1; // it's optional
  string map_name = 2;
  string target_parking_space_name = 3;
}
message EndMappingCMDRP {
  bool success = 1;
  deeproute.localization.event.MappingErrorCode error_code = 2;
  perception.RASMap semantic_map = 3;                       // in odometry frame  // GWM SOP+3 deprecated
  deeproute.map.MapObstacles semantic_map_obstacles = 4;    // in odometry frame  // GWM SOP+3 deprecated

  double mapped_trajectory_length = 5; // could we get this from map?             // GWM SOP+3 deprecated
  uint32 mapped_speedbump_count = 6;   // could we get this from map?             // GWM SOP+3 deprecated
  int32 target_parking_space_id = 7;   // could we get this from map?             // GWM SOP+3 deprecated
  
  havp_map.HAVPMap havp_map = 8; // do we still need information above?
  int32 ego_floor_id = 9;
  int32 recommended_favorite_parking_space_id = 10;
  ChangedMapStatusList changed_map_status_list = 11;
}

enum OperatingType {
  DELETE = 0;
  MODIFY = 1;
  ADD = 2; // TODO: do we need this?
  MERGE = 3;
}

message ParkingSpaceInfoUpdateCMD {
  int32 map_id = 1;
  OperatingType operation_type = 2; //修改方式
  havp_map.ParkingSpaceUsrInfo parking_spaces_info = 3;
  bool is_default_parking_space = 4;
}
message ParkingSpaceInfoUpdateCMDRP {
  havp_map.ParkingSpaceUsrInfo parking_spaces_info = 1;
  bool is_default_parking_space = 2;
  OperatingType real_operation_type = 3;
}

message MapInfoUpdateCMD {
  OperatingType operation_type = 1; //修改方式, Add is not supported
  havp_map.HAVPMapMetaData map_info = 2;
}
message MapInfoUpdateCMDRP {
  havp_map.HAVPMapMetaData map_info = 1;
}

message GetTargetHavpMapMetaDataCMD {
  int32 target_map_id = 1;
}
message GetTargetHavpMapMetaDataCMDRP {
  havp_map.HAVPMapMetaData havp_map_meta_data = 1; // only get light-weighted usr data, not include map structure information
}


message GetAllHavpMapMetaDataCMD {
}
message GetAllHavpMapMetaDataCMDRP {
  repeated havp_map.HAVPMapMetaData havp_map_meta_data = 1; // only get light-weighted usr data, not include map structure information
}

message SaveMapCMD {                            // deprecated
  bool save_map = 1;
}
message SaveMapCMDRP {                          // deprecated
}

message GetMapTargetParkingSpaceCMD {
}
message GetMapTargetParkingSpaceCMDRP {
  deeproute.hdmap.StandbyArea parking_space = 1; // in odometry frame, if there is no nearest parking space or vehicle isn't in parking space, return null
}

message GetTargetParkingSpaceInMapCMD {
  deeproute.common.Point3D position = 1;
}
message GetTargetParkingSpaceInMapCMDRP {
  havp_map.ParkingSpaceUsrInfo target_parking_spaces_user_info = 1;
}

message SetNavigationTargetParkingSpaceCMD {
  int32 target_navigation_parking_space_id = 1;
}

message SetNavigationTargetParkingSpaceCMDRP {}

message GetMapCMD {
}
message GetMapCMDRP {
  perception.RASMap semantic_map = 1;  // in map frame                      // GWM SOP+3 deprecated
  deeproute.map.MapObstacles semantic_map_obstacles = 2; // in map frame    // GWM SOP+3 deprecated
  double map_trajectory_length = 3;                                         // GWM SOP+3 deprecated
  deeproute.common.Transformation3 odometry_to_map_transformation = 4;      // GWM SOP+3 deprecated

  havp_map.HAVPMap havp_map = 5; // do we still need information above?
}

enum StartNavigationRequestReason {
  REASON_PREPLAN = 0;  // 预规划导航路线
  REASON_VPA_NAVIGATION = 1; // 进导航
  REASON_VPA_ROUTING = 2;  // 进巡航
}

message StartNavigationCMD {
  int32 target_parking_space_id = 1; // 导航目标车位
  StartNavigationRequestReason reason = 2;  // 请求原因
}

message StartNavigationCMDRP {
  double route_total_distance = 1;  // 导航总距离
  ErrorCode error_code = 2;
  int32 target_parking_space_id = 3;
}

message EndNavigationCMD {
}
message EndNavigationCMDRP {
}

message CancelPreMappingCMD {
}
message CancelPreMappingCMDRP {
}

message GetNearbyMapCMD {
  deeproute.common.PointLLH search_origin = 1;
  double search_radius = 2;
}

message GetNearbyMapCMDRP {
  message NearbyMapInfo {
    int32 map_id = 1;
    double distance = 2;
  }
  repeated NearbyMapInfo nearby_map_info = 1;
}

/*** End of VPA Test ***/

enum CurrentMode {
  DRIVING = 0;  // auto-driving
  APA = 1;
  RADS = 2;
  VPA = 3;
  MANUAL = 4;
}
message DrivingModeChangeCMD {
  CurrentMode driving_mode = 1;
}
message DrivingModeChangeCMDRP {
}

message ParkingSpaceNameDuplicationCheckCMD {
    int32 map_id = 1;
    int32 target_parking_space_id = 2;
    string name = 3;
}
message ParkingSpaceNameDuplicationCheckCMDRP {
    bool name_repeated = 1;     // return true if repeated
}

message MapNameDuplicationCheckCMD {
    int32 map_id = 1;
    string name = 2;
}
message MapNameNameDuplicationCheckCMDRP {
    bool name_repeated = 1;     // return true if repeated
}

message LocalizationCommand {  // external users send commands in
                               // '/localization/command' topic to
                               // localization modules
  string cmd_id = 1;
  double timestamp_ms = 2;
  oneof command {
    // testing command
    StartMapLocalizationCMD start_map_localization_cmd = 3;                           // GWM SOP+3 deprecated
    StartMappingCMD start_mapping_cmd = 4;
    EndMappingCMD end_mapping_cmd = 5;
    EndMapLocalizationCMD end_map_localization_cmd = 6;                               // GWM SOP+3 deprecated
    CancelMappingCMD cancel_mapping_cmd = 7;
    LocalizationModeChangeCMD localization_mode_change_cmd = 8;
    GetMapTargetParkingSpaceCMD get_map_target_parking_space_cmd = 9;
    GetMapCMD get_map_cmd = 10;
    CancelPreMappingCMD cancel_pre_mapping_cmd = 11;
    DrivingModeChangeCMD driving_mode_change_cmd = 12;
    ParkingSpaceInfoUpdateCMD parking_space_info_update_cmd = 13;
    MapInfoUpdateCMD map_info_update_cmd = 14;
    GetTargetHavpMapMetaDataCMD get_target_havp_map_meta_data_cmd = 15;
    GetAllHavpMapMetaDataCMD get_all_havp_map_meta_data_cmd = 16;
    StartNavigationCMD start_navigation_cmd = 17;
    EndNavigationCMD end_navigation_cmd = 18;
    GetTargetParkingSpaceInMapCMD get_target_parking_space_in_map_cmd = 19;
    SetNavigationTargetParkingSpaceCMD set_navigation_target_parking_space_cmd = 20;  // GWM SOP+3 deprecated
    GetNearbyMapCMD get_nearby_map_cmd = 21;
    ParkingSpaceNameDuplicationCheckCMD parking_space_name_duplication_check_cmd = 22;
    MapNameDuplicationCheckCMD map_name_duplication_check_cmd = 23;
  }
}

message LocalizationResponse {  // external users receive 'LocalizationResponse'
                                // in '/localization/command_response' topic
                                // from localization modules
  string cmd_id = 1;
  double timestamp_ms = 2;
  ErrorCode error_code = 3;
  oneof command_response {
    StartMapLocalizationCMDRP start_map_localization_cmd = 4;                         // GWM SOP+3 deprecated
    StartMappingCMDRP start_mapping = 5;
    EndMappingCMDRP end_mapping = 6;
    EndMapLocalizationCMDRP end_map_localization = 7;                                 // GWM SOP+3 deprecated
    CancelMappingCMDRP cancel_mapping = 8;
    LocalizationModeChangeCMDRP localization_mode_change = 9;
    GetMapTargetParkingSpaceCMDRP get_map_target_parking_space = 10;
    GetMapCMDRP get_map = 11;
    CancelPreMappingCMDRP cancel_pre_mapping = 12;
    DrivingModeChangeCMDRP driving_mode_change = 13;
    ParkingSpaceInfoUpdateCMDRP parking_space_info_update = 14;
    MapInfoUpdateCMDRP map_info_update = 15;
    GetTargetHavpMapMetaDataCMDRP get_target_havp_map_meta_data = 16;
    GetAllHavpMapMetaDataCMDRP get_all_havp_map_meta_data = 17;
    StartNavigationCMDRP start_navigation = 18;
    EndNavigationCMDRP end_navigation = 19;
    GetTargetParkingSpaceInMapCMDRP get_target_parking_space_in_map = 20;
    SetNavigationTargetParkingSpaceCMDRP set_navigation_target_parking_space = 21;    // deprecated
    GetNearbyMapCMDRP get_nearby_map = 22;
    ParkingSpaceNameDuplicationCheckCMDRP parking_space_name_duplication_check = 23;
    MapNameNameDuplicationCheckCMDRP map_name_duplication_check = 24;
  }
}