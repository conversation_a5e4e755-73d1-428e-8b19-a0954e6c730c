package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "aeb_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["aeb_state.proto"],
    deps = [
        "//proto/aeb:visualizer_aeb_command_proto",
        "//proto/esa:esa_state_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "aeb_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":aeb_state_proto",
    ],
)

python_proto_library(
    name = "aeb_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":aeb_state_proto",
    ],
)

proto_library(
    name = "visualizer_aeb_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["visualizer_aeb_command.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "visualizer_aeb_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":visualizer_aeb_command_proto",
    ],
)

python_proto_library(
    name = "visualizer_aeb_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":visualizer_aeb_command_proto",
    ],
)

proto_library(
    name = "trigger_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["trigger_event.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "trigger_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":trigger_event_proto",
    ],
)

python_proto_library(
    name = "trigger_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":trigger_event_proto",
    ],
)

