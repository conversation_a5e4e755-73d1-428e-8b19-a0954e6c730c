syntax = "proto2";

package deeproute.aeb;

// topic: /visualizer/aeb_command
message VisualizerCommand {
  enum AebEnableType {
    UNKNOWN = 0;
    CLOSE = 1;
    OPEN_CARE_BRAKE = 2;
    OPEN_NOT_CARE_BRAKE = 3;
  }
  enum AebCrossObjectSwitch {
    SWITCH_UNKNOWN = 0;
    ENABLE = 1;
    DISABLE = 2;
  }
  enum CruiseSpeedType{
    TYPE_UNKNOWN = 0;
    TURN_ON = 1;
    TURN_OFF = 2;
  }
  enum LevelType {
    LEVEL_UNKNOWN = 0;
    EARLY = 1;
    MIDDLE = 2;
    LATE = 3;
    CUSTOM = 4;
    DEFAULT = 5;
  }
  message PoseCheckerCmd {
    optional double dist_step_thres = 1 [default = 0.5];
    optional int32 buffer_thres = 2 [default = 5];
    optional double duration = 3 [default = 20.0];
  }
  optional bool aeb_enable = 1;
  optional double speed = 2;
  optional bool enable_set_speed = 3;
  optional AebEnableType aeb_enable_type = 4;
  optional AebCrossObjectSwitch aeb_cross_object_switch = 5;
  optional CruiseSpeedType cruise_speed_type = 6;
  optional LevelType aeb_level = 7;
  optional LevelType fcw_level = 8;
  optional AebEnableType meb_enable_type = 9;
  optional AebEnableType fctb_enable_type = 10;
  optional AebEnableType rctb_enable_type = 11;
  optional PoseCheckerCmd pose_checker_cmd = 87; 
  optional double lat_accel_max_nudge_fcw_cmd = 88;
  optional double lat_accel_max_nudge_point_cmd = 89;
  optional double lat_accel_max_nudge_cmd = 90;
  optional double lat_ignore_dist_cmd = 91;
  optional double raeb_buffer_duration_cmd = 92;
  optional double rctb_buffer_duration_cmd = 93;
  optional double fcta_buffer_duration_cmd = 94;
  optional double fcw_buffer_duration_cmd = 95;
  optional double aeb_buffer_duration_cmd = 96;
  repeated double dists_stop_vru_cmd = 97;
  repeated double dists_stop_cmd = 98;
  repeated double eba_decel_table_cmd = 99;
  repeated double aeb_decel_table_cmd = 100;
}
