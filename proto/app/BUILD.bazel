package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "deeproute_app_base_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_app_base.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_app_base_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_app_base_proto",
    ],
)

python_proto_library(
    name = "deeproute_app_base_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_app_base_proto",
    ],
)

proto_library(
    name = "deeproute_app_access_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_app_access.proto"],
    deps = [
        "//proto/app:deeproute_app_base_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_app_access_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_app_access_proto",
    ],
)

python_proto_library(
    name = "deeproute_app_access_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_app_access_proto",
    ],
)

proto_library(
    name = "deeproute_app_data_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_app_data.proto"],
    deps = [
        "//proto/semantic_map:map_standby_area_proto",
        "//proto/app:deeproute_app_base_proto",
        "//proto/devices:devices_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_app_data_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_app_data_proto",
    ],
)

python_proto_library(
    name = "deeproute_app_data_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_app_data_proto",
    ],
)

proto_library(
    name = "deeproute_app_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_app_event.proto"],
    deps = [
        "//proto/app:deeproute_app_base_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_app_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_app_event_proto",
    ],
)

python_proto_library(
    name = "deeproute_app_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_app_event_proto",
    ],
)

proto_library(
    name = "deeproute_app_command_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_app_command.proto"],
    deps = [
        "//proto/semantic_map:map_standby_area_proto",
        "//proto/app:deeproute_app_base_proto",
        "//proto/app:deeproute_app_data_proto",
        "//proto/common:geometry_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_app_command_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_app_command_proto",
    ],
)

python_proto_library(
    name = "deeproute_app_command_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_app_command_proto",
    ],
)

