syntax = "proto3";

package dr.dta.onboard;

/********************************单次智驾导航信息统计*******************************/
// topic : /dta/onboard
message DtaOnboard {
  SingleTripDriving single_trip_driving = 1;   // 单次智驾导航信息统计
}

message SingleTripDriving {
  string nav_id = 1;                      // 导航ID: car_id + timestamp                                
  double total_distance = 2;              // 行程里程: km
  double noa_distance = 3;                // noa里程
  double lcc_distance = 4;                // lcc里程
  double acc_distance = 5;                // acc里程
  uint64 total_time = 6;                  // 行程时长: second
  uint64 noa_time = 7;                    // noa时长
  uint64 lcc_time = 8;                    // lcc时长
  uint64 acc_time = 9;                    // acc时长
  PerformanceMetrics performance_metrics = 10; // 性能指标统计
  DrivingTrajectory driving_trajectory = 11;   // 行程轨迹
}

message PerformanceMetrics {
  repeated GradingInfo grading_info = 1;   // grading性能指标统计
}

message GradingInfo {
  enum GradingEvent {
    UNKNOWN = 0;
    PASS_JUNCTION_SUCCESS = 1;                // 经过路口成功
    PASS_JUNCTION_FAIL = 2;                   // 经过路口失败
    LANE_CHANGE_SUCCESS = 3;                  // 自主变道成功
    LANE_CHANGE_FAIL = 4;                     // 自主变道失败
    INBOUND_OR_OUTBOUND_RAMP_SUCCESS = 5;     // 汇入汇出成功
    INBOUND_OR_OUTBOUND_RAMP_FAIL = 6;        // 汇入汇出失败
    DETOUR_SUCCESS = 7;                       // 绕行成功
    DETOUR_FAIL = 8;                          // 绕行失败
    YIELD_VRU_SUCCESS = 9;                    // 避让行人成功
    YIELD_VRU_FAIL = 10;                      // 避让行人失败
    CUT_IN_SUCCESS = 11;                      // 应对加塞成功
    CUT_IN_FAIL = 12;                         // 应对加塞失败
  };
  GradingEvent grading_event = 1;    // Grading性能指标事件
  uint32 times = 2;                  // 次数
}

message DrivingTrajectory {
  repeated Point point = 1;          // 轨迹点
}

message Point {
  double x = 1; // 经度
  double y = 2; // 纬度
  double z = 3; // *高度
}
