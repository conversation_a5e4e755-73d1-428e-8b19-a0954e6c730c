package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "dta_onboard_proto",
    visibility = ["//visibility:public"],
    srcs = ["dta_onboard.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dta_onboard_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dta_onboard_proto",
    ],
)

python_proto_library(
    name = "dta_onboard_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dta_onboard_proto",
    ],
)

