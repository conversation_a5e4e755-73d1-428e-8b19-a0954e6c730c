syntax = "proto2";

package deeproute.perception;
import "common/geometry.proto";
import "perception/deeproute_perception_obstacle.proto";

message AdbOutput {
  optional int64 time_measurement = 1;
  repeated CarObject adb_object = 2;
  optional AdbTrafficCond adb_traffic_cond = 3;
  repeated AdbTrafficSign adb_traffic_sign = 4;
  repeated AdbLight2D adb_object_2d = 5;
}

message CarObject {
  enum Direction {
    OTHER = 0;
    SAME = 1;
    OPPOSITE = 2;
  };
  optional PerceptionObstacle.Type type = 1;
  optional bool trk_info = 2;
  optional Direction obj_dir = 3;
  optional float abs_dist = 4;
  optional float hozl_ag_le = 5;
  optional float hozl_ag_re = 6;
  optional float obj_hozl_ag_spd_le = 7;
  optional float obj_hozl_ag_spd_ri = 8;
  optional float vert_ag = 9;
  optional int32 percep_obj_id = 10;
}

message AdbTrafficCond {
  optional int32 too_many_cars = 1;
  optional CarNumDebug debug_car_num = 2; 
} 

message CarNumDebug {
  optional int32 inrange_100_income = 1;
  optional int32 inrange_100_all = 2;
  optional int32 inrange_150_income = 3;
  optional int32 inrange_150_all = 4;
  optional int32 inrange_200_income = 5;
  optional int32 inrange_200_all = 6;
}

message AdbTrafficSign {
  enum TrackState{
    TRACKED = 0;
    MISS = 1;
    LOST = 2;
  };
  optional TrackState adb_trk_info = 1;
  optional float adb_abs_dist = 2;
  optional float adb_hozl_ag_le = 3; // horizontal angle leqstant
  optional float adb_hozl_ag_re = 4; // horizontal angle geqstant
  optional float adb_vert_ag_top = 5; // vertival angle top
  optional float adb_vert_ag_bot = 6; // vertival angle bottom

  enum Conf{
    LOW = 0;
    MEDIUM = 1;
    HIGH = 2;
  };
  optional Conf adb_conf = 7 [default = HIGH];
} 

message AdbLight2D {
  optional BBox2D bbox = 1;
  optional float approx_dist = 2; // m
  optional double confidence = 3;
  optional CarObject.Direction direction = 4;
}