syntax = "proto2";

package deeproute.prediction;

message Intention {
  optional LaneChange lane_change = 1;
  optional float abnormal_prob = 2; // For Vehicle Only
  optional float crossing_prob = 3; // For Non-Vehicle Only
  optional float attention_score = 4;   // For Ego Caution
  optional float object_decision_prob = 5 [deprecated = true];
  optional float yield_prob = 6 [default = 0.0];
  optional float overtake_prob = 7 [default = 0.0];
}

message LaneChange {  // as probability
    optional float keep = 1;
    optional float left = 2;
    optional float right = 3;
}
