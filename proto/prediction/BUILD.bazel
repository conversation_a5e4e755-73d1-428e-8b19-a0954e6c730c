package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "trajectory_proto",
    visibility = ["//visibility:public"],
    srcs = ["trajectory.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "trajectory_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":trajectory_proto",
    ],
)

python_proto_library(
    name = "trajectory_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":trajectory_proto",
    ],
)

proto_library(
    name = "intention_proto",
    visibility = ["//visibility:public"],
    srcs = ["intention.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "intention_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":intention_proto",
    ],
)

python_proto_library(
    name = "intention_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":intention_proto",
    ],
)

proto_library(
    name = "deeproute_predobstacle_proto",
    visibility = ["//visibility:public"],
    srcs = ["deeproute_predobstacle.proto"],
    deps = [
        "//proto/prediction:trajectory_proto",
        "//proto/prediction:intention_proto",
        "//proto/perception:deeproute_perception_obstacle_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "deeproute_predobstacle_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":deeproute_predobstacle_proto",
    ],
)

python_proto_library(
    name = "deeproute_predobstacle_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":deeproute_predobstacle_proto",
    ],
)


proto_library(
    name = "offline_feature_proto",
    visibility = ["//visibility:public"],
    srcs = ["offline_feature.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "offline_feature_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":offline_feature_proto",
    ],
)

python_proto_library(
    name = "offline_feature_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":offline_feature_proto",
    ],
)
