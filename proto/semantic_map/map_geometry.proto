syntax = "proto2";

import "common/geometry.proto";

package deeproute.hdmap;

// Polygon, not necessary convex Deprecated
// Deprecated, use common.Polygon instead
message Polygon {
  repeated deeproute.common.PointENU points = 1;
}

// Deprecated
message Polyline {
  repeated deeproute.common.PointENU points = 1;
}


// Straight line segment.
// A segment is represented by two endpoints.
message LineSegment {
  repeated deeproute.common.PointENU points = 1;
}

// Generalization of a line.
message CurveSegment {
  oneof curve_type {
    LineSegment line_segment = 1;
  }
  optional double s = 6;  // start position (s-coordinate)
  optional deeproute.common.PointENU start_position = 7;
  optional double heading = 8;  // start orientation
  optional double length = 9;
}

// An object similar to a line but that need not be straight.
message Curve {
  repeated CurveSegment segment = 1;
}

// Geometry of dotted line markings painted on the road.
message DottedLineGeometry {
  repeated LineSegment dotted_line_segments = 1;
}