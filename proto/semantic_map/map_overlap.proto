syntax = "proto2";

package deeproute.hdmap;

import "common/geometry.proto";

message LaneOverlapInfo {
  optional double start_s = 1;  // position (s-coordinate)
  optional double end_s = 2;    // position (s-coordinate)
  optional bool is_merge = 3;

  optional int32 region_overlap_id = 4;
}

message SignalOverlapInfo {}

message StopSignOverlapInfo {}

message CrosswalkOverlapInfo { 
  optional int32 region_overlap_id = 1; 
  repeated common.Point3D intersect_laneboundary_points = 2;
}

message JunctionOverlapInfo {}

message YieldOverlapInfo {}

message ClearAreaOverlapInfo {}

message SpeedBumpOverlapInfo {}

message ParkingSpaceOverlapInfo {}

message PNCJunctionOverlapInfo {}

message RegionOverlapInfo {
  optional int32 id = 1;
  repeated common.Polygon polygon = 2;
}

// Information about one object in the overlap.
message ObjectOverlapInfo {
  optional int32 id = 1;

  oneof overlap_info {
    LaneOverlapInfo lane_overlap_info = 3;
    SignalOverlapInfo signal_overlap_info = 4;
    StopSignOverlapInfo stop_sign_overlap_info = 5;
    CrosswalkOverlapInfo crosswalk_overlap_info = 6;
    JunctionOverlapInfo junction_overlap_info = 7;
    YieldOverlapInfo yield_sign_overlap_info = 8;
    ClearAreaOverlapInfo clear_area_overlap_info = 9;
    SpeedBumpOverlapInfo speed_bump_overlap_info = 10;
    ParkingSpaceOverlapInfo parking_space_overlap_info = 11;
    PNCJunctionOverlapInfo pnc_junction_overlap_info = 12;
  }
}

// Here, the "overlap" includes any pair of objects on the map
// (e.g. lanes, junctions, and crosswalks).
message Overlap {
  optional int32 id = 1;

  // Information about one overlap, include all overlapped objects.
  repeated ObjectOverlapInfo object = 2;

  repeated RegionOverlapInfo region_overlap = 3;

  enum OverlapType {
    UNKNOWN = 0;
    CLEAR_ZONE = 1;
    CROSS_WALK = 2;
    JUNCTION = 3;
    LANE = 4;
    STOP_LINE = 5;
    ENTRANCE = 6;
    BUS_STATION = 7;
    PARKING = 8;
    PNC_JUNCTION = 9;
    JUNCTION_END_LINE = 10;
    SPEED_BUMP = 11;
    ROAD_MASK = 12;
    ROAD_MASK_PLUS = 13;
    DECELERATION_AND_YIELD_LINE = 14;
    TRANSVERSE_DECELERATION_MARKING = 15;
    VEHICLE_GAP_CONFIRMATION_LINE = 16;
    WIDE_LANE = 17;
    INVALID_AREA = 18;
    DIVERSION_AREA = 19;
    STANDBY_AREA = 20;
    TOLL_STATION = 21;
    LANE_MARKER = 22;
    BICYCLE_LANE_MARKER = 23;    
    CONNECTION_LINE = 24;   
  }
  optional OverlapType type = 4;
  // The current overlap message is mainly used in the lane.
  // s_begin and s_begin indicate that the overlap is at the beginning and end of the lane.
  optional float s_begin = 5;
  optional float s_end = 6;
}
