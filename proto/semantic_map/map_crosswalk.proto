syntax = "proto2";

package deeproute.hdmap;

import "common/geometry.proto";
import "semantic_map/map_overlap.proto";
// Crosswalk is a place designated for pedestrians to cross a road.
message Crosswalk {
  optional int32 id = 1;

  // A clockwise polygon. The first two points in the polygon indicates the 
  // direction along which people would walk on the crosswalk.
  optional common.Polygon polygon = 2;

  repeated int32 overlap_id = 3;

  optional double cost = 4;

  repeated int32 layers = 5;

  repeated Overlap overlaps = 6;
}
