syntax = "proto2";

package deeproute.hdmap;

import "common/geometry.proto";
import "semantic_map/map_overlap.proto";
//RoadMaskPlus means a roadmask with the direction.
//The direction is from the first point to the second point of the polygon.
message RoadMaskPlus {
  optional int32 id = 1;
  optional common.Polygon polygon = 2;
  optional int32 layer = 3;
  repeated Overlap overlaps = 4;
  //是否为lane_close场景
  optional bool is_lane_close = 5;
}
