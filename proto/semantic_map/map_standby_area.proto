syntax = "proto2";

package deeproute.hdmap;

import "common/geometry.proto";
import "semantic_map/map_overlap.proto";
message StandbyArea {
  optional int32 id = 1;

  optional common.Polygon polygon = 2;

  optional deeproute.common.Point3D switch_point = 3;

  optional deeproute.common.Point3D center_point = 4;

  optional double heading = 5;

  repeated int32 layers = 6;

  // If this filed exist, use the switch_point_out for leaving the standby area, otherwise use the switch_point in field 3 
  optional deeproute.common.Point3D switch_point_out = 7;

  repeated Overlap overlaps = 8;
}



