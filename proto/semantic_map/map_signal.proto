syntax = "proto2";

package deeproute.hdmap;

import "common/geometry.proto";
import "semantic_map/map_geometry.proto";

message Subsignal {
  enum Type {
    UNKNOWN = 1;
    CIRCLE = 2;
    ARROW_LEFT = 3;
    ARROW_FORWARD = 4;
    ARROW_RIGHT = 5;
    ARROW_LEFT_AND_FORWARD = 6;
    ARROW_RIGHT_AND_FORWARD = 7;
    ARROW_U_TURN = 8;
  };

  optional int32 id = 1;
  optional Type type = 2;

  // Location of the center of the bulb. now no data support.
  optional deeproute.common.PointENU location = 3;
}

message SignInfo {
  enum Type {
    None = 0;
    NO_RIGHT_TURN_ON_RED = 1;
  };

  optional Type type = 1;
}

message Signal {
  enum Type {
    UNKNOWN = 1;
    MIX_2_HORIZONTAL = 2;
    MIX_2_VERTICAL = 3;
    MIX_3_HORIZONTAL = 4;
    MIX_3_VERTICAL = 5;
    SINGLE = 6;
  };

  optional int32 id = 1;
  optional common.Polygon boundary = 2;
  repeated Subsignal subsignal = 3;
  // TODO: add orientation. now no data support.
  repeated int32 overlap_id = 4;
  optional Type type = 5;
  // stop line
  repeated Curve stop_line = 6;

  repeated SignInfo sign_info = 7;
}
