package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@bazel_skylib//lib:selects.bzl", "selects")
load("@bazel_skylib//rules:expand_template.bzl", "expand_template")
load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "chassis_control_interface_type_proto",
    visibility = ["//visibility:public"],
    srcs = ["chassis_control_interface_type.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "chassis_control_interface_type_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":chassis_control_interface_type_proto",
    ],
)

python_proto_library(
    name = "chassis_control_interface_type_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":chassis_control_interface_type_proto",
    ],
)

proto_library(
    name = "mar_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["mar_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "mar_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":mar_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "mar_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":mar_chassis_detail_proto",
    ],
)

proto_library(
    name = "m7_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["m7_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "m7_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":m7_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "m7_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":m7_chassis_detail_proto",
    ],
)

proto_library(
    name = "m5_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["m5_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "m5_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":m5_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "m5_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":m5_chassis_detail_proto",
    ],
)

proto_library(
    name = "byd_tang_adas_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["byd_tang_adas_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "byd_tang_adas_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":byd_tang_adas_detail_proto",
    ],
)

python_proto_library(
    name = "byd_tang_adas_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":byd_tang_adas_detail_proto",
    ],
)

proto_library(
    name = "canbus_blc_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["canbus_blc_state.proto"],
    deps = [
        "//proto/common/configs:vehicle_config_proto",
        "//proto/canbus:chassis_proto",
        "//proto/common:vehicle_signal_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "canbus_blc_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":canbus_blc_state_proto",
    ],
)

python_proto_library(
    name = "canbus_blc_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":canbus_blc_state_proto",
    ],
)

proto_library(
    name = "canraw_proto",
    visibility = ["//visibility:public"],
    srcs = ["canraw.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "canraw_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":canraw_proto",
    ],
)

python_proto_library(
    name = "canraw_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":canraw_proto",
    ],
)

proto_library(
    name = "gwm_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["gwm_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gwm_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gwm_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "gwm_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gwm_chassis_detail_proto",
    ],
)

proto_library(
    name = "gwm_d03_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["gwm_d03_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gwm_d03_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gwm_d03_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "gwm_d03_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gwm_d03_chassis_detail_proto",
    ],
)

proto_library(
    name = "gwm_p03_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["gwm_p03_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gwm_p03_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gwm_p03_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "gwm_p03_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gwm_p03_chassis_detail_proto",
    ],
)

proto_library(
    name = "gl_p177_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["gl_p177_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "gl_p177_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":gl_p177_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "gl_p177_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":gl_p177_chassis_detail_proto",
    ],
)

proto_library(
    name = "byd_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["byd_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "byd_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":byd_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "byd_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":byd_chassis_detail_proto",
    ],
)

proto_library(
    name = "model_config_proto",
    visibility = ["//visibility:public"],
    srcs = ["model_config.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "model_config_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":model_config_proto",
    ],
)

python_proto_library(
    name = "model_config_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":model_config_proto",
    ],
)

proto_library(
    name = "vehicle_custom_config_proto",
    visibility = ["//visibility:public"],
    srcs = ["vehicle_custom_config.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "vehicle_custom_config_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":vehicle_custom_config_proto",
    ],
)

python_proto_library(
    name = "vehicle_custom_config_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":vehicle_custom_config_proto",
    ],
)

expand_template(
    name = "car_info",
    out = "car_info.proto",
    template = "car_info.proto.in",
    substitutions = selects.with_or({
        ("@deeproute_build_tools//:AT7-ET_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";  // keep
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";
import "canbus/hy11_chassis_detail.proto";
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
    M5ChassisDetail m5 = 2;
    MarChassisDetail mar = 3;
    M7ChassisDetail m7 = 4;  // keep
    deeproute.canbus.byd.BydChassisDetail byd = 5;
    GwmChassisDetail gwm = 6;
    SmartHy11ChassisDetail smart_hy11 = 7;
""",
        },
        ("@deeproute_build_tools//:C01-PT_setting", "@deeproute_build_tools//:M8-ALL_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";  // keep
import "canbus/hy11_chassis_detail.proto";
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
    M5ChassisDetail m5 = 2;
    MarChassisDetail mar = 3;
    M7ChassisDetail m7 = 4;
    deeproute.canbus.byd.BydChassisDetail byd = 5;
    GwmChassisDetail gwm = 6;  // keep
    SmartHy11ChassisDetail smart_hy11 = 7;
""",
        },
        ("@deeproute_build_tools//:D03-ET_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
import "canbus/gwm_d03_chassis_detail.proto";
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";
import "canbus/hy11_chassis_detail.proto";
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
        M5ChassisDetail m5 = 2;
        MarChassisDetail mar = 3;
        M7ChassisDetail m7 = 4;
        deeproute.canbus.byd.BydChassisDetail byd = 5;
        GwmChassisDetail gwm = 6;
        SmartHy11ChassisDetail smart_hy11 = 7;
        deeproute.canbus.gwm.d03.GwmD03ChassisDetail gwm_d03 = 9;
""",
        },
        ("@deeproute_build_tools//:P03-ET_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
import "canbus/gwm_p03_chassis_detail.proto";
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";
import "canbus/hy11_chassis_detail.proto";
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
        M5ChassisDetail m5 = 2;
        MarChassisDetail mar = 3;
        M7ChassisDetail m7 = 4;
        deeproute.canbus.byd.BydChassisDetail byd = 5;
        GwmChassisDetail gwm = 6;
        SmartHy11ChassisDetail smart_hy11 = 7;
        deeproute.canbus.gwm.p03.GwmP03ChassisDetail gwm_p03 = 10;
""",
        },
        ("@deeproute_build_tools//:P177-ET_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
import "canbus/gl_p177_chassis_detail.proto";
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";
import "canbus/hy11_chassis_detail.proto";
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
        M5ChassisDetail m5 = 2;
        MarChassisDetail mar = 3;
        M7ChassisDetail m7 = 4;
        deeproute.canbus.byd.BydChassisDetail byd = 5;
        GwmChassisDetail gwm = 6;
        SmartHy11ChassisDetail smart_hy11 = 7;
        deeproute.canbus.gl.p177.GlP177ChassisDetail gl_p177 = 11;
""",
        },
        ("@deeproute_build_tools//:HY11-ET_setting") : {
            "@_IMPORT_CHASSIS_DETAIL_PROTO_@" : """
// strip later
import "canbus/m5_chassis_detail.proto";
import "canbus/mar_chassis_detail.proto";
import "canbus/m7_chassis_detail.proto";
import "canbus/byd_chassis_detail.proto";
import "canbus/gwm_chassis_detail.proto";
import "canbus/hy11_chassis_detail.proto";  // keep
""",
            "@_CHASSIS_DETAIL_MESAGE_TYPE_@" : """
        M5ChassisDetail m5 = 2;
        MarChassisDetail mar = 3;
        M7ChassisDetail m7 = 4;
        deeproute.canbus.byd.BydChassisDetail byd = 5;
        GwmChassisDetail gwm = 6;
        SmartHy11ChassisDetail smart_hy11 = 7;  // keep
""",
        },
    })
)

proto_library(
    name = "car_info_proto",
    visibility = ["//visibility:public"],
    srcs = [":car_info.proto"],
    deps = [
        "//proto/canbus:chassis_proto",
        "//proto/common/configs:vehicle_config_proto",
        "//proto/common:vehicle_signal_proto",
    ] + selects.with_or({
        ("@deeproute_build_tools//:AT7-ET_setting") : [
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto", # keep
            "//proto/canbus:gwm_chassis_detail_proto",
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto",
        ],
        ("@deeproute_build_tools//:C01-PT_setting", "@deeproute_build_tools//:M8-ALL_setting") : [
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto",
            "//proto/canbus:gwm_chassis_detail_proto", # keep
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto",
        ],
        ("@deeproute_build_tools//:D03-ET_setting") : [
            "//proto/canbus:gwm_d03_chassis_detail_proto",
            # TODO: remove following
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto",
            "//proto/canbus:gwm_chassis_detail_proto",
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto",
        ],
        ("@deeproute_build_tools//:P03-ET_setting") : [
            "//proto/canbus:gwm_p03_chassis_detail_proto",
            # TODO: remove following
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto",
            "//proto/canbus:gwm_chassis_detail_proto",
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto",
        ],
        ("@deeproute_build_tools//:P177-ET_setting") : [
            "//proto/canbus:gl_p177_chassis_detail_proto",
            # TODO: remove following
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto",
            "//proto/canbus:gwm_chassis_detail_proto",
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto",
        ],
        ("@deeproute_build_tools//:HY11-ET_setting") : [
            "//proto/canbus:m5_chassis_detail_proto",
            "//proto/canbus:mar_chassis_detail_proto",
            "//proto/canbus:m7_chassis_detail_proto",
            "//proto/canbus:gwm_chassis_detail_proto",
            "//proto/canbus:byd_chassis_detail_proto",
            "//proto/canbus:hy11_chassis_detail_proto", # keep
        ],
    }),
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "car_info_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":car_info_proto",
    ],
)

python_proto_library(
    name = "car_info_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":car_info_proto",
    ],
)

proto_library(
    name = "dtu_hmi_canbus_interface_proto",
    visibility = ["//visibility:public"],
    srcs = ["dtu_hmi_canbus_interface.proto"],
    deps = [
        "//proto/common/configs:vehicle_config_proto",
        "//proto/canbus:byd_chassis_detail_proto",
        "//proto/canbus:gwm_chassis_detail_proto",
        "//proto/canbus:gwm_d03_chassis_detail_proto",
        "//proto/canbus:gwm_p03_chassis_detail_proto",
        "//proto/canbus:gl_p177_chassis_detail_proto",
        "//proto/canbus:byd_tang_adas_detail_proto",
        "//proto/canbus:byd_tang_chassis_detail_proto",
        "//proto/canbus:hy11_chassis_detail_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dtu_hmi_canbus_interface_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dtu_hmi_canbus_interface_proto",
    ],
)

python_proto_library(
    name = "dtu_hmi_canbus_interface_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dtu_hmi_canbus_interface_proto",
    ],
)

proto_library(
    name = "canbus_state_report_proto",
    visibility = ["//visibility:public"],
    srcs = ["canbus_state_report.proto"],
    deps = [
        "//proto/common:header_proto",
        "//proto/canbus:car_info_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "canbus_state_report_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":canbus_state_report_proto",
    ],
)

python_proto_library(
    name = "canbus_state_report_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":canbus_state_report_proto",
    ],
)

proto_library(
    name = "can_card_parameter_proto",
    visibility = ["//visibility:public"],
    srcs = ["can_card_parameter.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "can_card_parameter_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":can_card_parameter_proto",
    ],
)

python_proto_library(
    name = "can_card_parameter_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":can_card_parameter_proto",
    ],
)

proto_library(
    name = "hy11_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["hy11_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "hy11_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":hy11_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "hy11_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":hy11_chassis_detail_proto",
    ],
)

proto_library(
    name = "dtu_canbus_interface_proto",
    visibility = ["//visibility:public"],
    srcs = ["dtu_canbus_interface.proto"],
    deps = [
        "//proto/common:vehicle_signal_proto",
        "//proto/common/configs:vehicle_config_proto",
        "//proto/canbus:byd_chassis_detail_proto",
        "//proto/canbus:chassis_control_interface_type_proto",
        "//proto/canbus:gwm_chassis_detail_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "dtu_canbus_interface_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":dtu_canbus_interface_proto",
    ],
)

python_proto_library(
    name = "dtu_canbus_interface_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":dtu_canbus_interface_proto",
    ],
)

proto_library(
    name = "byd_tang_chassis_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["byd_tang_chassis_detail.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "byd_tang_chassis_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":byd_tang_chassis_detail_proto",
    ],
)

python_proto_library(
    name = "byd_tang_chassis_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":byd_tang_chassis_detail_proto",
    ],
)

proto_library(
    name = "torque_speed_table_proto",
    visibility = ["//visibility:public"],
    srcs = ["torque_speed_table.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "torque_speed_table_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":torque_speed_table_proto",
    ],
)

python_proto_library(
    name = "torque_speed_table_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":torque_speed_table_proto",
    ],
)

proto_library(
    name = "byd_tang_detail_proto",
    visibility = ["//visibility:public"],
    srcs = ["byd_tang_detail.proto"],
    deps = [
        "//proto/canbus:byd_tang_adas_detail_proto",
        "//proto/canbus:byd_tang_chassis_detail_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "byd_tang_detail_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":byd_tang_detail_proto",
    ],
)

python_proto_library(
    name = "byd_tang_detail_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":byd_tang_detail_proto",
    ],
)

proto_library(
    name = "chassis_proto",
    visibility = ["//visibility:public"],
    srcs = ["chassis.proto"],
    deps = [
        "//proto/common:header_proto",
        "//proto/common:vehicle_signal_proto",
        "//proto/common:drive_state_proto",
        "//proto/common:geometry_proto",
        "//proto/common/configs:vehicle_config_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "chassis_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":chassis_proto",
    ],
)

python_proto_library(
    name = "chassis_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":chassis_proto",
    ],
)

