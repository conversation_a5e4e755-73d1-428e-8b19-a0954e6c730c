syntax = "proto2";

package deeproute.canbus.gwm.p03;

message ECM_FD2_103 {
// Report Message
  enum EngStatePHEVType {
    ENGSTATE_PHEV_STAND_BY = 0;
    ENGSTATE_PHEV_READY = 1;
    ENGSTATE_PHEV_CRANKING = 2;
    ENGSTATE_PHEV_RUNNING = 3;
    ENGSTATE_PHEV_STOPPING = 4;
    ENGSTATE_PHEV_FINISH = 5;
    ENGSTATE_PHEV_AUTOSTOPPING = 6;
    ENGSTATE_PHEV_RESERVED = 7;
  }
  // [] [0|7] [initial_value:0]
  optional EngStatePHEVType eng_state_phev = 1;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_ecm_fd2 = 2;
}

message ECM1_111 {
// Report Message
  enum EngSpdVldtyType {
    ENGSPDVLDTY_INVALID_VALUES = 0;
    ENGSPDVLDTY_VALID_VALUES = 1;
    ENGSPDVLDTY_INIT_VALUE = 2;
    ENGSPDVLDTY_RESERVED = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ecm1 = 1;
  // [rpm] [0|8191.875] [initial_value:0]
  optional double eng_spd = 2;
  // [] [0|3] [initial_value:2]
  optional EngSpdVldtyType eng_spd_vldty = 3;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ecm1 = 4;
}

message VCU_FD2_11B {
// Report Message
  enum DrvModType {
    DRVMOD_NORMAL_MODE_STARTED = 0;
    DRVMOD_EV_MODE_STARTED = 1;
    DRVMOD_SAVE_MODE_STARTED = 2;
    DRVMOD_SPORT_MODE_STARTED = 3;
    DRVMOD_AWD_NORMAL_MODE_STARTED = 4;
    DRVMOD_SNOW_MODE_STARTED = 5;
    DRVMOD_MUDDY_MODE_STARTED = 6;
    DRVMOD_SAND_MODE_STARTED = 7;
    DRVMOD_ECO_MODE = 8;
    DRVMOD_SPORT_PLUS_MODE_STARTED = 9;
    DRVMOD_OPD_MODE = 10;
    DRVMOD_AUTO_MODE = 11;
    DRVMOD_ECOPLUS_MODE = 12;
    DRVMOD_INDIVIDUATION_MODE = 13;
    DRVMOD_LADY_MODE = 14;
    DRVMOD_WET_MODE = 15;
    DRVMOD_TRACK_MODE = 16;
    DRVMOD_RESERVED = 17;
  }
  enum PTDrvModType {
    PTDRVMOD_DEFAULT_VALUE = 0;
    PTDRVMOD_EV_FWD = 1;
    PTDRVMOD_EV_AWD = 2;
    PTDRVMOD_EV_RWD = 3;
    PTDRVMOD_HEV_FWD = 4;
    PTDRVMOD_HEV_AWD = 5;
    PTDRVMOD_POWER_SPLIT = 6;
    PTDRVMOD_POWER_SPLIT_AWD = 7;
  }
  enum DrvTrqReqOvldType {
    DRVTRQREQOVLD_NO_DRIVER_OVERRIDE = 0;
    DRVTRQREQOVLD_DRIVER_OVERRIDE = 1;
    DRVTRQREQOVLD_RESERVED = 2;
    DRVTRQREQOVLD_INVALID_VALUE = 3;
  }
  enum DrvgPrkgTrqAvailStsType {
    DRVGPRKGTRQAVAILSTS_INITIAL_VALUE = 0;
    DRVGPRKGTRQAVAILSTS_NOT_AVAILABLE = 1;
    DRVGPRKGTRQAVAILSTS_AVAILABLE = 2;
    DRVGPRKGTRQAVAILSTS_AVAILABLE_DEGRADED = 3;
  }
  enum DrvgPrkgTrqRespFlgType {
    DRVGPRKGTRQRESPFLG_INITIAL_VALUE = 0;
    DRVGPRKGTRQRESPFLG_NOT_RESPONSE = 1;
    DRVGPRKGTRQRESPFLG_RESPONSED = 2;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_vcu12 = 1;
  // description / [No Unit] [0|1] [initial_value:0]
  optional bool vcu_acc_com_if = 2;
  // description / [] [0|31] [initial_value:0]
  optional DrvModType drv_mod = 3;
  // description / [No Unit] [0|7] [initial_value:0]
  optional PTDrvModType pt_drv_mod = 4;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_vcu12 = 5;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_vcu26 = 6;
  // description / [Nm] [0|20470] [initial_value:0]
  optional double max_psbl_pos_wheel_trq = 7;
  // description / [Nm] [-20475|20475] [initial_value:4094]
  optional double act_tot_wheel_trq = 8;
  // description / [Nm] [-20470|0] [initial_value:4094]
  optional double min_psbl_neg_wheel_trq = 9;
  // [NoUnit] [0|3] [initial_value:0]
  optional DrvTrqReqOvldType drv_trq_req_ovld = 10;
  // [Nm] [-6000|14470] [initial_value:1200]
  optional double vcu_drv_req_tq_whl = 11;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_vcu26 = 12;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_vcu21 = 13;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool drv_trq_req_ovrdvld = 14;
  // [NoUnit] [0|3] [initial_value:0]
  optional DrvgPrkgTrqAvailStsType drvg_prkg_trq_avail_sts = 15;
  // [NoUnit] [0|3] [initial_value:0]
  optional DrvgPrkgTrqRespFlgType drvg_prkg_trq_resp_flg = 16;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_vcu21 = 17;
}

message HUT44_127 {
// Report Message
  // [NoUnit] [0|1] [initial_value:0]
  optional bool auto_lmp_swt_sts_hut = 1;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool low_beam_swt_sts_hut = 2;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool rear_fog_lmp_swt_sts_hut = 3;
}

message IFC_FD1_12B {
// Control Message
  enum IFCFuncTrqCmdReqFlagType {
    IFCFUNCTRQCMDREQFLAG_NO_REQUEST = 0;
    IFCFUNCTRQCMDREQFLAG_LKA_REQUEST = 1;
    IFCFUNCTRQCMDREQFLAG_ESS_REQUEST = 2;
    IFCFUNCTRQCMDREQFLAG_RESERVED = 3;
  }
  enum IFCVibrationWarningType {
    IFCVIBRATIONWARNING_NO_REQUEST = 0;
    IFCVIBRATIONWARNING_LEVEL_1 = 1;
    IFCVIBRATIONWARNING_RESERVED = 2;
  }
  enum IFCFuncAngCmdReqFlagType {
    IFCFUNCANGCMDREQFLAG_NO_REQUEST = 0;
    IFCFUNCANGCMDREQFLAG_LKA_REQ = 1;
    IFCFUNCANGCMDREQFLAG_ESS_REQ = 2;
    IFCFUNCANGCMDREQFLAG_RESERVED = 3;
  }
  enum ESSStateType {
    ESS_STATE_INITIAL_VALUE = 0;
    ESS_STATE_OFF = 1;
    ESS_STATE_STANDBY = 2;
    ESS_STATE_ACTIVE = 3;
    ESS_STATE_PASSIVE = 4;
    ESS_STATE_FAULT = 5;
    ESS_STATE_RESERVED = 6;
  }
  enum AESStateType {
    AES_STATE_INITIAL_VALUE = 0;
    AES_STATE_OFF = 1;
    AES_STATE_STANDBY = 2;
    AES_STATE_ACTIVE = 3;
    AES_STATE_PASSIVE = 4;
    AES_STATE_FAULT = 5;
    AES_STATE_RESERVED = 6;
  }
  enum LDWStateType {
    LDW_STATE_INITIAL_VALUE = 0;
    LDW_STATE_OFF = 1;
    LDW_STATE_STANDBY = 2;
    LDW_STATE_ACTIVE = 3;
    LDW_STATE_PASSIVE = 4;
    LDW_STATE_FAULT = 5;
    LDW_STATE_RESERVED = 6;
  }
  enum LKAStateType {
    LKA_STATE_INITIAL_VALUE = 0;
    LKA_STATE_OFF = 1;
    LKA_STATE_STANDBY = 2;
    LKA_STATE_ACTIVE = 3;
    LKA_STATE_PASSIVE = 4;
    LKA_STATE_FAULT = 5;
    LKA_STATE_RESERVED = 6;
  }
  enum VisOptSpdLimType {
    VISOPTSPDLIM_NO_DISPLAY = 0;
    VISOPTSPDLIM_SPL_5 = 1;
    VISOPTSPDLIM_SPL_10 = 2;
    VISOPTSPDLIM_SPL_15 = 3;
    VISOPTSPDLIM_SPL_20 = 4;
    VISOPTSPDLIM_SPL_25 = 5;
    VISOPTSPDLIM_SPL_30 = 6;
    VISOPTSPDLIM_SPL_35 = 7;
    VISOPTSPDLIM_SPL_40 = 8;
    VISOPTSPDLIM_SPL_45 = 9;
    VISOPTSPDLIM_SPL_50 = 10;
    VISOPTSPDLIM_SPL_55 = 11;
    VISOPTSPDLIM_SPL_60 = 12;
    VISOPTSPDLIM_SPL_65 = 13;
    VISOPTSPDLIM_SPL_70 = 14;
    VISOPTSPDLIM_SPL_75 = 15;
    VISOPTSPDLIM_SPL_80 = 16;
    VISOPTSPDLIM_SPL_85 = 17;
    VISOPTSPDLIM_SPL_90 = 18;
    VISOPTSPDLIM_SPL_95 = 19;
    VISOPTSPDLIM_SPL_100 = 20;
    VISOPTSPDLIM_SPL_105 = 21;
    VISOPTSPDLIM_SPL_110 = 22;
    VISOPTSPDLIM_SPL_115 = 23;
    VISOPTSPDLIM_SPL_120 = 24;
    VISOPTSPDLIM_SPL_125 = 25;
    VISOPTSPDLIM_SPL_130 = 26;
    VISOPTSPDLIM_SPL_135 = 27;
    VISOPTSPDLIM_SPL_140 = 28;
    VISOPTSPDLIM_SPL_145 = 29;
    VISOPTSPDLIM_SPL_150 = 30;
    VISOPTSPDLIM_INVALID = 31;
  }
  enum NavOptSpdLimType {
    NAVOPTSPDLIM_NO_DISPLAY = 0;
    NAVOPTSPDLIM_SPL_5 = 1;
    NAVOPTSPDLIM_SPL_10 = 2;
    NAVOPTSPDLIM_SPL_15 = 3;
    NAVOPTSPDLIM_SPL_20 = 4;
    NAVOPTSPDLIM_SPL_25 = 5;
    NAVOPTSPDLIM_SPL_30 = 6;
    NAVOPTSPDLIM_SPL_35 = 7;
    NAVOPTSPDLIM_SPL_40 = 8;
    NAVOPTSPDLIM_SPL_45 = 9;
    NAVOPTSPDLIM_SPL_50 = 10;
    NAVOPTSPDLIM_SPL_55 = 11;
    NAVOPTSPDLIM_SPL_60 = 12;
    NAVOPTSPDLIM_SPL_65 = 13;
    NAVOPTSPDLIM_SPL_70 = 14;
    NAVOPTSPDLIM_SPL_75 = 15;
    NAVOPTSPDLIM_SPL_80 = 16;
    NAVOPTSPDLIM_SPL_85 = 17;
    NAVOPTSPDLIM_SPL_90 = 18;
    NAVOPTSPDLIM_SPL_95 = 19;
    NAVOPTSPDLIM_SPL_100 = 20;
    NAVOPTSPDLIM_SPL_105 = 21;
    NAVOPTSPDLIM_SPL_110 = 22;
    NAVOPTSPDLIM_SPL_115 = 23;
    NAVOPTSPDLIM_SPL_120 = 24;
    NAVOPTSPDLIM_SPL_125 = 25;
    NAVOPTSPDLIM_SPL_130 = 26;
    NAVOPTSPDLIM_SPL_135 = 27;
    NAVOPTSPDLIM_SPL_140 = 28;
    NAVOPTSPDLIM_SPL_145 = 29;
    NAVOPTSPDLIM_SPL_150 = 30;
    NAVOPTSPDLIM_INVALID = 31;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc1 = 1;
  // [] [0|16383] [initial_value:7169]
  optional int32 ifc_trq_ovl_cmd_protn_value = 2;
  // [Nm] [-10.23|10.24] [initial_value:1023]
  optional double ifc_trq_ovl_cmd_req_value = 3;
  // [] [0|7] [initial_value:0]
  optional IFCFuncTrqCmdReqFlagType ifc_func_trq_cmd_req_flag = 4;
  // [] [0|3] [initial_value:0]
  optional IFCVibrationWarningType ifc_vibration_warning = 5;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc1 = 6;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc2 = 7;
  // [\A1\E3] [-780|780] [initial_value:7800]
  optional double ifc_ang_cmd_req_value = 8;
  // [] [0|16383] [initial_value:7169]
  optional int32 ifc_ang_cmd_req_protn_value = 9;
  // [] [0|7] [initial_value:0]
  optional IFCFuncAngCmdReqFlagType ifc_func_ang_cmd_req_flag = 10;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc2 = 11;
  // [NoUnit] [0|7] [initial_value:0]
  optional ESSStateType ess_state = 12;
  // [NoUnit] [0|7] [initial_value:0]
  optional AESStateType aes_state = 13;
  // [NoUnit] [0|7] [initial_value:0]
  optional LDWStateType ldw_state = 14;
  // [NoUnit] [0|7] [initial_value:0]
  optional LKAStateType lka_state = 15;
  // [NoUnit] [0|31] [initial_value:0]
  optional VisOptSpdLimType vis_opt_spd_lim = 16;
  // [NoUnit] [0|31] [initial_value:0]
  optional NavOptSpdLimType nav_opt_spd_lim = 17;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_ifc_fd1 = 18;
}

message IBC_FD1_12E {
// Report Message
  enum PushrodStautsType {
    PUSHRODSTAUTS_NOT_PRESSED_INIT_DEFAULT = 0;
    PUSHRODSTAUTS_PRESSED = 1;
    PUSHRODSTAUTS_RESERVED = 2;
    PUSHRODSTAUTS_ERROR = 3;
  }
  enum DrvgBrkgTrqAvailStsType {
    DRVGBRKGTRQAVAILSTS_INITIAL_VALUE = 0;
    DRVGBRKGTRQAVAILSTS_NOT_AVAILABLE = 1;
    DRVGBRKGTRQAVAILSTS_AVAILABLE = 2;
    DRVGBRKGTRQAVAILSTS_DEGRADED1 = 3;
    DRVGBRKGTRQAVAILSTS_DEGRADED2 = 4;
  }
  enum PrkgBrkgTrqAvailStsType {
    PRKGBRKGTRQAVAILSTS_INITIAL_VALUE = 0;
    PRKGBRKGTRQAVAILSTS_NOT_AVAILABLE = 1;
    PRKGBRKGTRQAVAILSTS_AVAILABLE = 2;
    PRKGBRKGTRQAVAILSTS_DEGRADED1 = 3;
    PRKGBRKGTRQAVAILSTS_DEGRADED2 = 4;
  }
  enum BrkgTrqModRespType {
    BRKGTRQMODRESP_INITIAL_VALUE = 0;
    BRKGTRQMODRESP_NOTACTIVE = 1;
    BRKGTRQMODRESP_APA_ACTIVE = 2;
    BRKGTRQMODRESP_ACC_ACTIVE = 3;
  }
  enum BrkHldAvailStsType {
    BRKHLDAVAILSTS_INITIAL_VALUE = 0;
    BRKHLDAVAILSTS_NOT_AVAILABLE = 1;
    BRKHLDAVAILSTS_AVAILABLE = 2;
  }
  enum RPAavailabilityType {
    RPAAVAILABILITY_NOTAVAILABLE = 0;
    RPAAVAILABILITY_AVAILABLE = 1;
    RPAAVAILABILITY_TESTPENDING = 2;
    RPAAVAILABILITY_RESERVED = 3;
  }
  enum DrvBrkOvrdFlgType {
    DRVBRKOVRDFLG_FLASE = 0;
    DRVBRKOVRDFLG_TRUE = 1;
    DRVBRKOVRDFLG_RESERVED = 2;
  }
  enum APCStsType {
    APCSTS_STANDBY = 0;
    APCSTS_ACTIVE_APA = 1;
    APCSTS_ACTIVE_RPA = 2;
    APCSTS_ACTIVE_AVP = 3;
    APCSTS_ACTIVE_MEB = 4;
    APCSTS_RESERVED = 5;
  }
  enum APCFailrStsType {
    APCFAILRSTS_NO_ERROR = 0;
    APCFAILRSTS_APC1_APA_DRIVER_NOT_PRESENT_A3_A8_C1_F4_A3_A9 = 1;
    APCFAILRSTS_APC124_TOO_HIGH_SLOPE = 2;
    APCFAILRSTS_TOO_HIGH_SPEED = 3;
    APCFAILRSTS_UNEXPECTED_EPB_APPLY = 4;
    APCFAILRSTS_UNEXPECTED_GEAR_INTERVENTION = 5;
    APCFAILRSTS_VEHICLE_BLOCK = 6;
    APCFAILRSTS_ERROR = 7;
  }
  enum AVPavailabilityType {
    AVPAVAILABILITY_NOTAVAILABLE = 0;
    AVPAVAILABILITY_AVAILABLE = 1;
    AVPAVAILABILITY_TESTPENDING = 2;
    AVPAVAILABILITY_RESERVED = 3;
  }
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_ibc_fd1_1 = 1;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool inp_rod_stk_sts = 2;
  // [NoUnit] [0|3] [initial_value:0]
  optional PushrodStautsType pushrod_stauts = 3;
  // [mm] [-5|46.15] [initial_value:0]
  optional double inp_rod_stk = 4;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool plunger_press_sts = 5;
  // [bar] [-5|301.9] [initial_value:0]
  optional double plunger_press = 6;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_ibc_fd1_1 = 7;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_ibc_fd1_2 = 8;
  // [NoUnit] [0|7] [initial_value:0]
  optional DrvgBrkgTrqAvailStsType drvg_brkg_trq_avail_sts = 9;
  // [NoUnit] [0|7] [initial_value:0]
  optional PrkgBrkgTrqAvailStsType prkg_brkg_trq_avail_sts = 10;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgTrqModRespType brkg_trq_mod_resp = 11;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkHldAvailStsType brk_hld_avail_sts = 12;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_ibc_fd1_2 = 13;
  // [Nm] [0|16383] [initial_value:0]
  optional int32 drv_brkg_trq_req = 14;
  // [Nm] [0|16383] [initial_value:0]
  optional int32 hyd_brkg_trq_req = 15;
  // [Nm] [0|16383] [initial_value:0]
  optional int32 hyd_brkg_act_trq = 16;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ibc_fd1_5 = 17;
  // [] [0|3] [initial_value:0]
  optional RPAavailabilityType rp_aavailability = 18;
  // [] [0|1] [initial_value:0]
  optional bool ap_aavailability = 19;
  // [] [0|1] [initial_value:0]
  optional bool cdd_ap_actv_veh_hld = 20;
  // [] [0|3] [initial_value:0]
  optional DrvBrkOvrdFlgType drv_brk_ovrd_flg = 21;
  // [] [0|1] [initial_value:0]
  optional bool me_bavailability = 22;
  // [] [0|7] [initial_value:0]
  optional APCStsType apc_sts = 23;
  // [] [0|7] [initial_value:0]
  optional APCFailrStsType apc_failr_sts = 24;
  // [NoUnit] [0|3] [initial_value:1]
  optional AVPavailabilityType av_pavailability = 25;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool brkg_ovrd_vld = 26;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ibc_fd1_5 = 27;
}

message ESP_FD2_137 {
// Report Message
  enum VehStandstillType {
    VEHSTANDSTILL_NOT_STANDSTILL = 0;
    VEHSTANDSTILL_STANDSTILL = 1;
    VEHSTANDSTILL_INVALID_SHORT_UNAVAILABILITY_MAX_3S = 2;
    VEHSTANDSTILL_RESERVED = 3;
  }
  enum HDCCtrlType {
    HDCCTRL_OFF = 0;
    HDCCTRL_ON_ACTIVE_BRAKING = 1;
    HDCCTRL_ON_NOT_ACTIVE_BRAKING = 2;
    HDCCTRL_RESERVED = 3;
  }
  enum AVHStsType {
    AVHSTS_OFF = 0;
    AVHSTS_STANDBY = 1;
    AVHSTS_ACTIVE = 2;
    AVHSTS_RESERVED = 3;
  }
  enum DrivingModDisType {
    DRIVINGMODDIS_STANDARD = 0;
    DRIVINGMODDIS_SPORT = 1;
    DRIVINGMODDIS_SNOW = 2;
    DRIVINGMODDIS_MUD = 3;
    DRIVINGMODDIS_SAND = 4;
    DRIVINGMODDIS_4L = 5;
    DRIVINGMODDIS_ECONOMIC = 6;
    DRIVINGMODDIS_MODE_UNKOWN = 7;
    DRIVINGMODDIS_AUTO = 8;
    DRIVINGMODDIS_SPORT_9 = 9;
    DRIVINGMODDIS_OFF_ROAD = 10;
    DRIVINGMODDIS_STAND_2H = 11;
    DRIVINGMODDIS_EXPERT = 12;
    DRIVINGMODDIS_STAND_4H = 13;
    DRIVINGMODDIS_FAILED = 14;
    DRIVINGMODDIS_INVALID_VALUE = 15;
    DRIVINGMODDIS_SNOW_4H = 16;
    DRIVINGMODDIS_NORMAL_4L = 17;
    DRIVINGMODDIS_ROCK_4L = 18;
    DRIVINGMODDIS_POTHLE_4L = 19;
    DRIVINGMODDIS_MUD_SAND_4L = 20;
    DRIVINGMODDIS_MOUNTAIN_4L = 21;
    DRIVINGMODDIS_TURBO = 22;
    DRIVINGMODDIS_RACE = 23;
    DRIVINGMODDIS_GRASS_GRAVEL = 24;
    DRIVINGMODDIS_RESERVED = 25;
    DRIVINGMODDIS_WADE_4H = 26;
    DRIVINGMODDIS_WADE_4L = 27;
    DRIVINGMODDIS_2H = 28;
    DRIVINGMODDIS_4H = 29;
    DRIVINGMODDIS_NOVICIATE = 30;
    DRIVINGMODDIS_RESERVED_31 = 31;
  }
  enum DrivingModReqESPType {
    DRIVINGMODREQ_ESP_NO_TERRAIN_CONTROL = 0;
    DRIVINGMODREQ_ESP_STANDARD = 1;
    DRIVINGMODREQ_ESP_SPORT = 2;
    DRIVINGMODREQ_ESP_SNOW_NEX = 3;
    DRIVINGMODREQ_ESP_EXPERT = 4;
    DRIVINGMODREQ_ESP_DEEP_MUD = 5;
    DRIVINGMODREQ_ESP_2H = 6;
    DRIVINGMODREQ_ESP_SOFT_SAND = 7;
    DRIVINGMODREQ_ESP_4H = 8;
    DRIVINGMODREQ_ESP_4L = 9;
    DRIVINGMODREQ_ESP_ECONOMIC = 10;
    DRIVINGMODREQ_ESP_SNOW_ESOF = 11;
    DRIVINGMODREQ_ESP_SNOW_TOD = 12;
    DRIVINGMODREQ_ESP_OFF_ROAD = 13;
    DRIVINGMODREQ_ESP_SPORT_14 = 14;
    DRIVINGMODREQ_ESP_INVALID_VALUE = 15;
    DRIVINGMODREQ_ESP_STAND_2H = 16;
    DRIVINGMODREQ_ESP_NOVICIATE = 17;
    DRIVINGMODREQ_ESP_STAND_4H = 18;
    DRIVINGMODREQ_ESP_SNOW_4H = 19;
    DRIVINGMODREQ_ESP_NORMAL_4L = 20;
    DRIVINGMODREQ_ESP_ROCK_4L = 21;
    DRIVINGMODREQ_ESP_POTHOLE_4L = 22;
    DRIVINGMODREQ_ESP_MUD_SAND_4L = 23;
    DRIVINGMODREQ_ESP_MOUNTAIN_4L = 24;
    DRIVINGMODREQ_ESP_TURBO = 25;
    DRIVINGMODREQ_ESP_RACE = 26;
    DRIVINGMODREQ_ESP_GRASS_GRAVEL = 27;
    DRIVINGMODREQ_ESP_UNEVEN_NEX = 28;
    DRIVINGMODREQ_ESP_WADE_4H = 29;
    DRIVINGMODREQ_ESP_WADE_4L = 30;
    DRIVINGMODREQ_ESP_CLIMB_4L = 31;
  }
  enum AVHErrStsType {
    AVHERRSTS_NO_FAILURE = 0;
    AVHERRSTS_FAILURE_WHEN_AVH_IS_ON_NEED = 1;
    AVHERRSTS_FAILURE_WHEN_IS_NOT_ON_NEED = 2;
    AVHERRSTS_RESERVED = 3;
  }
  enum LgtCtrlrFailrType {
    LGTCTRLRFAILR_NO_ERROR = 0;
    LGTCTRLRFAILR_VEHICLE_BLOCKED = 1;
    LGTCTRLRFAILR_UNEXPECTED_GEARPOSITION = 2;
    LGTCTRLRFAILR_UNEXPECTED_EPB_ACTION = 3;
    LGTCTRLRFAILR_RESERVED = 4;
    LGTCTRLRFAILR_UNEXPECTED_GEARINTERVENTION = 5;
    LGTCTRLRFAILR_ACTIVE_BRAKING_FAILURE = 6;
    LGTCTRLRFAILR_INVALID_VALUES = 7;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_esp2 = 1;
  // [] [0|1] [initial_value:0]
  optional bool esp_diag_actv = 2;
  // [] [0|1] [initial_value:0]
  optional bool brk_dsk_ovrheatd = 3;
  // [] [0|1] [initial_value:0]
  optional bool no_brk_force = 4;
  // [] [0|1] [initial_value:0]
  optional bool ab_aavailable = 5;
  // [] [0|1] [initial_value:0]
  optional bool aba_actv = 6;
  // [] [0|3] [initial_value:0]
  optional VehStandstillType veh_standstill = 7;
  // [] [0|3] [initial_value:0]
  optional HDCCtrlType hdc_ctrl = 8;
  // [] [0|3] [initial_value:0]
  optional AVHStsType avh_sts = 9;
  // [] [0|31] [initial_value:0]
  optional DrivingModDisType driving_mod_dis = 10;
  // [] [0|31] [initial_value:1]
  optional DrivingModReqESPType driving_mod_req_esp = 11;
  // [] [0|3] [initial_value:0]
  optional AVHErrStsType avh_err_sts = 12;
  // [] [0|1] [initial_value:0]
  optional bool aebib_actv = 13;
  // [] [0|1] [initial_value:0]
  optional bool aebba_actv = 14;
  // [] [0|1] [initial_value:0]
  optional bool aeb_available = 15;
  // [] [0|1] [initial_value:0]
  optional bool abp_available = 16;
  // [] [0|1] [initial_value:0]
  optional bool abp_actv = 17;
  // [] [0|1] [initial_value:0]
  optional bool cdd_actv = 18;
  // [] [0|1] [initial_value:0]
  optional bool cdd_available = 19;
  // [] [0|1] [initial_value:0]
  optional bool awb_available = 20;
  // [] [0|1] [initial_value:0]
  optional bool awb_actv = 21;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool cdp_actv = 22;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool cta_brk_available = 23;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool hhc_sts = 24;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_esp2 = 25;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_esp1 = 26;
  // [] [0|1] [initial_value:0]
  optional bool esp_func_off_sts = 27;
  // [] [0|1] [initial_value:0]
  optional bool esp_brk_lmp_ctrl = 28;
  // [] [0|1] [initial_value:0]
  optional bool esp_fail_sts = 29;
  // [] [0|1] [initial_value:1]
  optional bool esp_master_cyl_brk_press_vld = 30;
  // [] [0|1] [initial_value:0]
  optional bool msr_actv = 31;
  // [] [0|1] [initial_value:0]
  optional bool msr_actv_ra = 32;
  // [] [0|1] [initial_value:0]
  optional bool vdc_actv = 33;
  // [] [0|1] [initial_value:0]
  optional bool ptc_actv = 34;
  // [] [0|1] [initial_value:0]
  optional bool ptc_actv_ra = 35;
  // [] [0|1] [initial_value:0]
  optional bool btc_actv = 36;
  // [] [0|1] [initial_value:0]
  optional bool btc_actv_ra = 37;
  // [bar] [-42.5|425] [initial_value:0]
  optional double esp_master_cyl_brk_press = 38;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_esp1 = 39;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_abs3 = 40;
  // [] [0|1] [initial_value:1]
  optional bool abs_fail_sts = 41;
  // [] [0|1] [initial_value:0]
  optional bool ebd_fail_sts = 42;
  // [] [0|1] [initial_value:0]
  optional bool abs_actv = 43;
  // [] [0|1] [initial_value:0]
  optional bool veh_spd_vld = 44;
  // [kph] [0|299.98125] [initial_value:0]
  optional double veh_spd = 45;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool ebd_actv = 46;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_abs3 = 47;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_esp7 = 48;
  // [] [0|16383] [initial_value:0]
  optional int32 four_whls_brk_trq = 49;
  // [] [0|7] [initial_value:0]
  optional LgtCtrlrFailrType lgt_ctrlr_failr = 50;
  // [Nm] [-16383|16384] [initial_value:0]
  optional int32 eng_trq_req = 51;
  // [] [0|1] [initial_value:0]
  optional bool aps_eng_trq_req_ena = 52;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_esp7 = 53;
  // [NM] [-3000|5000] [initial_value:65535]
  optional int32 drv_trq_red_fast_req_fa = 54;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool drv_trq_red_req_actv_fa = 55;
  // [NM] [-3000|5000] [initial_value:0]
  optional int32 drv_trq_inc_req_fa = 56;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool drv_trq_inc_req_actv_fa = 57;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_esp_fd2 = 58;
}

message ESP_FD3_13B {
// Report Message
  enum FLWheelDriveDirectionType {
    FLWHEELDRIVEDIRECTION_INVALID = 0;
    FLWHEELDRIVEDIRECTION_FORWARD = 1;
    FLWHEELDRIVEDIRECTION_BACKWARD = 2;
    FLWHEELDRIVEDIRECTION_STOP = 3;
  }
  enum FRWheelDriveDirectionType {
    FRWHEELDRIVEDIRECTION_INVALID = 0;
    FRWHEELDRIVEDIRECTION_FORWARD = 1;
    FRWHEELDRIVEDIRECTION_BACKWARD = 2;
    FRWHEELDRIVEDIRECTION_STOP = 3;
  }
  enum EPBErrStsType {
    EPBERRSTS_UNDEFINED = 0;
    EPBERRSTS_NO_ERROR = 1;
    EPBERRSTS_ERROR = 2;
    EPBERRSTS_DIAGNOSIS = 3;
  }
  enum EPBStsType {
    EPBSTS_RELEASED = 0;
    EPBSTS_CLOSED = 1;
    EPBSTS_IN_PROGRESS = 2;
    EPBSTS_UNKNOWN = 3;
  }
  enum EPBSwtPosnType {
    EPB_SWTPOSN_NEUTRAL = 0;
    EPB_SWTPOSN_RELEASE = 1;
    EPB_SWTPOSN_APPLY = 2;
    EPB_SWTPOSN_RESERVED = 3;
  }
  enum ESPDSTStsType {
    ESP_DSTSTS_NOT_ACTIVE = 0;
    ESP_DSTSTS_ACTIVE = 1;
    ESP_DSTSTS_NOT_AVAILABLE = 2;
    ESP_DSTSTS_RESERVED = 3;
  }
  enum CCOActiveType {
    CCO_ACTIVE_NOT_ACTIVE = 0;
    CCO_ACTIVE_ACTIVE = 1;
    CCO_ACTIVE_STAND_BY = 2;
    CCO_ACTIVE_FAILED = 3;
  }
  enum RLWheelDriveDirectionType {
    RLWHEELDRIVEDIRECTION_INVALID = 0;
    RLWHEELDRIVEDIRECTION_FORWARD = 1;
    RLWHEELDRIVEDIRECTION_BACKWARD = 2;
    RLWHEELDRIVEDIRECTION_STOP = 3;
  }
  enum RRWheelDriveDirectionType {
    RRWHEELDRIVEDIRECTION_INVALID = 0;
    RRWHEELDRIVEDIRECTION_FORWARD = 1;
    RRWHEELDRIVEDIRECTION_BACKWARD = 2;
    RRWHEELDRIVEDIRECTION_STOP = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_abs1 = 1;
  // [] [0|3] [initial_value:0]
  optional FLWheelDriveDirectionType fl_wheel_drive_direction = 2;
  // [] [0|1] [initial_value:0]
  optional bool fl_wheel_spd_vld = 3;
  // [km/h] [0|299.98125] [initial_value:0]
  optional double fl_wheel_spd = 4;
  // [] [0|3] [initial_value:0]
  optional FRWheelDriveDirectionType fr_wheel_drive_direction = 5;
  // [] [0|1] [initial_value:0]
  optional bool fr_wheel_spd_vld = 6;
  // [km/h] [0|299.98125] [initial_value:0]
  optional double fr_wheel_spd = 7;
  // [] [0|255] [initial_value:0]
  optional int32 wss_fl_edges_sum = 8;
  // [] [0|255] [initial_value:0]
  optional int32 wss_fr_edges_sum = 9;
  // [] [0|1] [initial_value:1]
  optional bool wss_fl_edges_sum_vld = 10;
  // [] [0|1] [initial_value:1]
  optional bool wss_fr_edges_sum_vld = 11;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_abs1 = 12;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_epb1 = 13;
  // [] [0|1] [initial_value:0]
  optional bool epb_swt_posn_vld = 14;
  // [] [0|3] [initial_value:0]
  optional EPBErrStsType epb_err_sts = 15;
  // [] [0|3] [initial_value:0]
  optional EPBStsType epb_sts = 16;
  // [] [0|3] [initial_value:0]
  optional EPBSwtPosnType epb_swt_posn = 17;
  // [] [0|7] [initial_value:0]
  optional ESPDSTStsType esp_dst_sts = 18;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_epb1 = 19;
  // [] [0|3] [initial_value:0]
  optional CCOActiveType cco_active = 20;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_abs2 = 21;
  // [] [0|3] [initial_value:0]
  optional RLWheelDriveDirectionType rl_wheel_drive_direction = 22;
  // [] [0|1] [initial_value:0]
  optional bool rl_wheel_spd_vld = 23;
  // [km/h] [0|299.98125] [initial_value:0]
  optional double rl_wheel_spd = 24;
  // [] [0|3] [initial_value:0]
  optional RRWheelDriveDirectionType rr_wheel_drive_direction = 25;
  // [] [0|1] [initial_value:0]
  optional bool rr_wheel_spd_vld = 26;
  // [km/h] [0|299.98125] [initial_value:0]
  optional double rr_wheel_spd = 27;
  // [] [0|255] [initial_value:0]
  optional int32 wss_rr_edges_sum = 28;
  // [] [0|255] [initial_value:0]
  optional int32 wss_rl_edges_sum = 29;
  // [] [0|1] [initial_value:1]
  optional bool wss_rl_edges_sum_vld = 30;
  // [] [0|1] [initial_value:1]
  optional bool wss_rr_edges_sum_vld = 31;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_abs2 = 32;
  // [Nm] [-6000|14475] [initial_value:0]
  optional int32 drv_trq_inc_req_ra = 33;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool drv_trq_inc_req_actv_ra = 34;
  // [Nm] [-6000|14475] [initial_value:65535]
  optional int32 drv_trq_red_req_ra = 35;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool drv_trq_red_req_actv_ra = 36;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_esp_fd3 = 37;
}

message ACC_FD1_143 {
// Control Message
  enum LongctrlModStsType {
    LONGCTRL_MODSTS_OFF_MODE = 0;
    LONGCTRL_MODSTS_PASSIVE_MODE_REJECT = 1;
    LONGCTRL_MODSTS_PASSIVE_MODE_STAND_BY = 2;
    LONGCTRL_MODSTS_ACTIVE_CONTROL_MODE = 3;
    LONGCTRL_MODSTS_BRAKE_ONLY_MODE = 4;
    LONGCTRL_MODSTS_OVERRIDE = 5;
    LONGCTRL_MODSTS_STANDSTILL = 6;
    LONGCTRL_MODSTS_FAILURE_MODE_LONGCTRL = 7;
    LONGCTRL_MODSTS_RESERVED = 8;
    LONGCTRL_MODSTS_SHUT_OFF = 9;
    LONGCTRL_MODSTS_RESERVED_10 = 10;
  }
  enum LongctrlParkShiftReqType {
    LONGCTRL_PARKSHIFTREQ_NO_REQUEST = 0;
    LONGCTRL_PARKSHIFTREQ_REQUEST_PARK_SHIFT = 1;
    LONGCTRL_PARKSHIFTREQ_INVALID = 2;
    LONGCTRL_PARKSHIFTREQ_RESERVED = 3;
  }
  enum FxTgtTrqEnaType {
    FXTGTTRQENA_INITIAL_VALUE = 0;
    FXTGTTRQENA_DISABLE = 1;
    FXTGTTRQENA_ENABLE = 2;
  }
  enum FxTgtTrqModType {
    FXTGTTRQMOD_INITIAL_VALUE = 0;
    FXTGTTRQMOD_THROTTLE_OVERRIDE_MODE = 1;
    FXTGTTRQMOD_THROTTLE_INHIBITE_MODE = 2;
  }
  enum FxTgtGearReqType {
    FXTGTGEARREQ_NO_REQUEST = 0;
    FXTGTGEARREQ_P = 1;
    FXTGTGEARREQ_R = 2;
    FXTGTGEARREQ_N = 3;
    FXTGTGEARREQ_D = 4;
  }
  enum FxTgtGearEnaType {
    FXTGTGEARENA_INITIAL_VALUE = 0;
    FXTGTGEARENA_DISABLE = 1;
    FXTGTGEARENA_ENABLE = 2;
  }
  enum DrvgPrkgFuncActvnStsType {
    DRVGPRKGFUNCACTVNSTS_INITIAL_VALUE = 0;
    DRVGPRKGFUNCACTVNSTS_OFF = 1;
    DRVGPRKGFUNCACTVNSTS_ON = 2;
  }
  enum LongctrlTgtGearReqType {
    LONGCTRL_TGTGEARREQ_NO_REQUEST = 0;
    LONGCTRL_TGTGEARREQ_P = 1;
    LONGCTRL_TGTGEARREQ_R = 2;
    LONGCTRL_TGTGEARREQ_N = 3;
    LONGCTRL_TGTGEARREQ_D = 4;
    LONGCTRL_TGTGEARREQ_RESERVED = 5;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc1 = 1;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_veh_hld_req = 2;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_drv_off_req = 3;
  // [] [0|15] [initial_value:0]
  optional LongctrlModStsType longctrl_mod_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_decel_req = 5;
  // [%] [-100|99.999713] [initial_value:32767]
  optional double longctrl_eng_trq_req = 6;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_eng_trq_req_active = 7;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_fuel_cut_off_prevn = 8;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_decel_to_stop_req = 9;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_epb_takeover_req = 10;
  // [] [0|3] [initial_value:0]
  optional LongctrlParkShiftReqType longctrl_park_shift_req = 11;
  // [kph/mph] [0|254] [initial_value:0]
  optional int32 actual_target_speed = 12;
  // [m/s^2] [-7|5.75] [initial_value:0]
  optional double longctrl_decel_req_value = 13;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc1 = 14;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc2 = 15;
  // [] [0|1] [initial_value:0]
  optional bool acc_cr_interface = 16;
  // [NoUnit] [-16|16] [initial_value:0]
  optional double hwp_tgt_accel_l2 = 17;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc2 = 18;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc10 = 19;
  // [NM] [-16384|16383.5] [initial_value:0]
  optional double longctrl_wheel_trq_req = 20;
  // [] [0|1] [initial_value:0]
  optional bool longctrl_wheel_trq_req_active = 21;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc10 = 22;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_acc12 = 23;
  // [Nm] [-32768|32766] [initial_value:0]
  optional int32 fx_tgt_trq = 24;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool fx_tgt_trq_vld = 25;
  // [NoUnit] [0|3] [initial_value:0]
  optional FxTgtTrqEnaType fx_tgt_trq_ena = 26;
  // [NoUnit] [0|3] [initial_value:0]
  optional FxTgtTrqModType fx_tgt_trq_mod = 27;
  // [Nm] [-32768|32766] [initial_value:0]
  optional int32 tot_ref_trq = 28;
  // [NoUnit] [0|7] [initial_value:0]
  optional FxTgtGearReqType fx_tgt_gear_req = 29;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool fx_tgt_gear_req_vld = 30;
  // [NoUnit] [0|3] [initial_value:0]
  optional FxTgtGearEnaType fx_tgt_gear_ena = 31;
  // [NoUnit] [0|3] [initial_value:0]
  optional DrvgPrkgFuncActvnStsType drvg_prkg_func_actvn_sts = 32;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc12 = 33;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_acc11 = 34;
  // [NoUnit] [0|7] [initial_value:0]
  optional LongctrlTgtGearReqType longctrl_tgt_gear_req = 35;
  // [NoUnit] [0|1] [initial_value:1]
  optional bool longctrl_tgt_gear_req_vld = 36;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool longctrl_accel_ovrd_actn_req = 37;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc11 = 38;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_acc_fd1 = 39;
}

message EPS_FD1_147 {
// Report Message
  enum EPSFailStsType {
    EPS_FAILSTS_NO_FAULT = 0;
    EPS_FAILSTS_PERMANENT_ERROR_DETECTED = 1;
    EPS_FAILSTS_INTERMITTENT_ERROR_DETECTED = 2;
    EPS_FAILSTS_RESERVED = 3;
  }
  enum EPSSteerModType {
    EPS_STEERMOD_COMFORT_MODE = 0;
    EPS_STEERMOD_SPORT_MODE = 1;
    EPS_STEERMOD_HANDINESS_MODE = 2;
    EPS_STEERMOD_RESERVED = 3;
  }
  enum EPSAvailStsType {
    EPS_AVAILSTS_TEMPORARY_INHIBIT = 0;
    EPS_AVAILSTS_AVAILABLE_FOR_CONTROL = 1;
    EPS_AVAILSTS_ACTIVE = 2;
    EPS_AVAILSTS_PERMANENTLY_FAILED = 3;
  }
  enum EPSDSTCtrlStsType {
    EPS_DSTCTRLSTS_PERMANENTLY_FAIL = 0;
    EPS_DSTCTRLSTS_TEMPORARY_INHIBIT = 1;
    EPS_DSTCTRLSTS_AVAILABLE_FOR_CONTROL = 2;
    EPS_DSTCTRLSTS_ACTIVE = 3;
    EPS_DSTCTRLSTS_RESERVED = 4;
  }
  enum EPSLKATrqOvlDlvdStsType {
    EPS_LKATRQOVLDLVDSTS_NOT_ACTIVE = 0;
    EPS_LKATRQOVLDLVDSTS_ACTIVE = 1;
    EPS_LKATRQOVLDLVDSTS_TEMPORARY_INHIBIT = 2;
    EPS_LKATRQOVLDLVDSTS_PERMANENTLY_FAILED = 3;
  }
  enum EPSLKAAngDlvdStsType {
    EPS_LKAANGDLVDSTS_NOT_ACTIVE = 0;
    EPS_LKAANGDLVDSTS_ACTIVE = 1;
    EPS_LKAANGDLVDSTS_TEMPORARY_INHIBIT = 2;
    EPS_LKAANGDLVDSTS_PERMANENTLY_FAILED = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_eps1 = 1;
  // [] [0|3] [initial_value:0]
  optional EPSFailStsType eps_fail_sts = 2;
  // [] [0|1] [initial_value:1]
  optional bool eps_trq_snsr_sts = 3;
  // [] [0|7] [initial_value:0]
  optional EPSSteerModType eps_steer_mod = 4;
  // [] [0|1] [initial_value:0]
  optional bool eps_interfer_detd = 5;
  // [] [0|1] [initial_value:0]
  optional bool eps_interfer_detd_vld = 6;
  // [] [0|3] [initial_value:0]
  optional EPSAvailStsType eps_avail_sts = 7;
  // [] [0|7] [initial_value:1]
  optional EPSDSTCtrlStsType eps_dst_ctrl_sts = 8;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_eps1 = 9;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_eps2 = 10;
  // [Nm] [-15|15] [initial_value:0]
  optional double eps_lka_torq_ovrl_dlvd = 11;
  // [] [0|1] [initial_value:0]
  optional bool eps_lka_torq_ovrl_dlvd_vld = 12;
  // [] [0|3] [initial_value:0]
  optional EPSLKATrqOvlDlvdStsType eps_lka_trq_ovl_dlvd_sts = 13;
  // [Nm] [-31.984375|32] [initial_value:2047]
  optional double eps_drv_inp_trq_val_high_reslolution = 14;
  // [] [0|3] [initial_value:0]
  optional EPSLKAAngDlvdStsType eps_lka_ang_dlvd_sts = 15;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_eps2 = 16;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_eps3 = 17;
  // [deg] [0|780] [initial_value:0]
  optional double eps_steer_wheel_ang = 18;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool eps_steer_wheel_ang_sign = 19;
  // [DegPerSec] [0|1016] [initial_value:0]
  optional double eps_steer_wheel_spd = 20;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool eps_steer_wheel_spd_sign = 21;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool eps_steer_wheel_ang_vld = 22;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_eps3 = 23;
}

message HAP_FD1_15B {
// Control Message
  enum APABrkFctnModType {
    APA_BRKFCTNMOD_NONE = 0;
    APA_BRKFCTNMOD_APA = 1;
    APA_BRKFCTNMOD_RPA = 2;
    APA_BRKFCTNMOD_AVP = 3;
    APA_BRKFCTNMOD_MEB = 4;
    APA_BRKFCTNMOD_RESERVED = 5;
  }
  enum APAApbReqType {
    APA_APBREQ_NONE = 0;
    APA_APBREQ_RELEASEREQ = 1;
    APA_APBREQ_APPLYREQ = 2;
  }
  enum APSBrkFctnModType {
    APS_BRKFCTNMOD_PARKASSIST_NOSIGNAL = 0;
    APS_BRKFCTNMOD_PARKASSIST_REMOTEPARK = 1;
    APS_BRKFCTNMOD_RESERVED = 2;
    APS_BRKFCTNMOD_PARKASSIST_AUTOMATICPARK = 3;
    APS_BRKFCTNMOD_RESERVED_4 = 4;
  }
  enum APSESPTgtGearReqType {
    APS_ESP_TGTGEARREQ_NO_REQUEST = 0;
    APS_ESP_TGTGEARREQ_P = 1;
    APS_ESP_TGTGEARREQ_R = 2;
    APS_ESP_TGTGEARREQ_N = 3;
    APS_ESP_TGTGEARREQ_D = 4;
    APS_ESP_TGTGEARREQ_RESERVED = 5;
  }
  enum APSBrkModStsType {
    APS_BRKMODSTS_PARKASSIST_INT = 0;
    APS_BRKMODSTS_PARKASSIST_STANDBY = 1;
    APS_BRKMODSTS_PARKASSIST_ACTIVE = 2;
    APS_BRKMODSTS_PARKASSIST_MANEUVERFINISHED = 3;
    APS_BRKMODSTS_PARKASSIST_SUSPEND_RESERVED = 4;
    APS_BRKMODSTS_PARKASSIST_ABORT = 5;
    APS_BRKMODSTS_RESERVED = 6;
  }
  enum APSBrkCategoryType {
    APS_BRKCATEGORY_PARKASSIST_IDLE = 0;
    APS_BRKCATEGORY_PARKASSIST_COMFORT = 1;
    APS_BRKCATEGORY_PARKASSIST_EMERGENCY = 2;
    APS_BRKCATEGORY_RESERVED = 3;
  }
  enum APSWorkStsType {
    APS_WORKSTS_DISABLE = 0;
    APS_WORKSTS_STANDBY = 1;
    APS_WORKSTS_SEARCHING = 2;
    APS_WORKSTS_GUIDANCE = 3;
    APS_WORKSTS_FAILED = 4;
    APS_WORKSTS_WAIT_FOR_ENGINE_RESTART = 5;
    APS_WORKSTS_RESERVED = 6;
  }
  enum PrkModType {
    PRKMOD_NO_ACTION = 0;
    PRKMOD_HEAD_PARKING_IN = 1;
    PRKMOD_TAIL_PARKING_IN = 2;
    PRKMOD_RESERVED = 3;
  }
  enum APSPrkgModType {
    APS_PRKGMOD_NONE = 0;
    APS_PRKGMOD_PARKING_IN = 1;
    APS_PRKGMOD_PARKING_OUT = 2;
    APS_PRKGMOD_RESERVED = 3;
  }
  enum APSProcBarType {
    APS_PROCBAR_0_100 = 0;
    APS_PROCBAR_RESERVED = 101;
    APS_PROCBAR_NO_DISPLAY = 127;
  }
  enum APSSlotDispType {
    APS_SLOTDISP_NO_PARKING_SLOT = 0;
    APS_SLOTDISP_LEFT_PARALLEL_PARKING_SLOT = 1;
    APS_SLOTDISP_LEFT_VERTICAL_PARKING_SLOT = 2;
    APS_SLOTDISP_LEFT_T_SHAPE_PARKING_SLOT = 3;
    APS_SLOTDISP_RIGHT_PARALLEL_PARKING_SLOT = 4;
    APS_SLOTDISP_RIGHT_VERTICAL_PARKING_SLOT = 5;
    APS_SLOTDISP_RIGHT_T_SHAPE_PARKING_SLOT = 6;
    APS_SLOTDISP_RESERVED = 7;
  }
  enum TurnLightsCmdType {
    TURNLIGHTSCMD_NO_REQUEST = 0;
    TURNLIGHTSCMD_LEFTLIGHT_ON = 1;
    TURNLIGHTSCMD_RIGHTLIGHT_ON = 2;
    TURNLIGHTSCMD_HAZARDLIGHT_ON = 3;
  }
  enum APSSysSoundIndcnType {
    APS_SYSSOUNDINDCN_NO_SOUND = 0;
    APS_SYSSOUNDINDCN_PARKING_UNAVAILABLE = 1;
    APS_SYSSOUNDINDCN_PARKING_FOUND = 2;
    APS_SYSSOUNDINDCN_PARKING_SUSPEND = 3;
    APS_SYSSOUNDINDCN_PARKING_START = 4;
    APS_SYSSOUNDINDCN_HAVP_AVP_READY = 5;
    APS_SYSSOUNDINDCN_HAVP_AVP_FINISH = 6;
    APS_SYSSOUNDINDCN_HAVP_AVP_ROUTE_FINISH = 7;
  }
  enum APSPrkgTypType {
    APS_PRKGTYP_NONE = 0;
    APS_PRKGTYP_PARALLEL_PARKING = 1;
    APS_PRKGTYP_VERTICAL_PARKING = 2;
    APS_PRKGTYP_RESERVED = 3;
  }
  enum APSTextDispType {
    APS_TEXTDISP_NO_REQUEST = 0;
    APS_TEXTDISP_WRONG_GEAR_POSITION_TO_START_PARKING = 1;
    APS_TEXTDISP_TURN_D_TO_SEARCH_FOR_SLOT = 2;
    APS_TEXTDISP_PLEASE_CLOSE_THE_TRUNK = 3;
    APS_TEXTDISP_PLEASE_CLOSE_THE_DOOR = 4;
    APS_TEXTDISP_PLEASE_FASTEN_THE_SEAT_BELT = 5;
    APS_TEXTDISP_PLEASE_UNFOLD_THE_MIRROR = 6;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_NEED_23KM_H = 7;
    APS_TEXTDISP_SYSTEM_ERROR_PARKING_SYS_EXITS = 8;
    APS_TEXTDISP_CONTROL_UNIT_ERROR_PARKING_SYS_EXITS = 9;
    APS_TEXTDISP_PLEASE_BRAKE = 10;
    APS_TEXTDISP_PLEASE_SELECT_PARKING_MODE = 11;
    APS_TEXTDISP_PLEASE_SELECT_INTERNAL_PARKING = 12;
    APS_TEXTDISP_PLEASE_SELECT_SLOT_AND_PARKING_MODE = 13;
    APS_TEXTDISP_PLEASE_SELECT_SLOT_AND_INTERNAL_PARKING = 14;
    APS_TEXTDISP_PLEASE_KEEP_BRAKING = 15;
    APS_TEXTDISP_HANDS_OFF_THE_STEERING_WHEEL = 16;
    APS_TEXTDISP_RELEASE_THE_BRAKE_PARKING_START = 17;
    APS_TEXTDISP_AUTO_PARKING_BE_READY_TO_BRAKE = 18;
    APS_TEXTDISP_PARKING_SUSPENDED_CLOSE_TRUNK = 19;
    APS_TEXTDISP_PARKING_SUSPENDED_CLOSE_DOOR = 20;
    APS_TEXTDISP_PARKING_SUSPENDED_FASTEN_SEAT_BELT = 21;
    APS_TEXTDISP_OBSTACLES_DETECTED = 22;
    APS_TEXTDISP_BRAKE_RECOVERED_CONFIRM_TO_CONTINUE = 23;
    APS_TEXTDISP_PARKING_QUITED = 24;
    APS_TEXTDISP_PARKING_FINISHED = 25;
    APS_TEXTDISP_INTERFERED_BY_DRIVER_PARKING_QUITED = 26;
    APS_TEXTDISP_TOO_MANY_PARKING_TIMES_PARKING_SYS_EXITS = 27;
    APS_TEXTDISP_PARKING_TIME_TOO_LONG_PARKING_SYS_EXITS = 28;
    APS_TEXTDISP_TOO_SMALL_SLOT_PARKING_SYS_EXITS = 29;
    APS_TEXTDISP_PLEASE_CLEAN_THE_CAMERA = 30;
    APS_TEXTDISP_SELECT_THE_PARKING_MODE_APA3 = 31;
    APS_TEXTDISP_SEARCHING_THE_RIGHT_SLOT = 32;
    APS_TEXTDISP_SEARCHING_THE_LEFT_SLOT = 33;
    APS_TEXTDISP_PLEASE_TURN_D_TO_SEARCH_FOR_PARKING_SLOT = 34;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_NEED_30KM_H = 35;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_SYS_EXITS = 36;
    APS_TEXTDISP_SELECT_PARKING_TYPE = 37;
    APS_TEXTDISP_LONG_PRESS_SWITCH_2S_TO_ENTER_AUTO_PARKING = 38;
    APS_TEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_GAS_PEDAL = 39;
    APS_TEXTDISP_PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_CONTINUE_AUTO_PARKING = 40;
    APS_TEXTDISP_PARKING_IS_NOT_AVAILABLE = 41;
    APS_TEXTDISP_PLEASE_SELECT_THE_PARKING_DIRECTION_BY_TURNING_THE_TURN_SWITCH = 42;
    APS_TEXTDISP_PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_ENTER_POC_LEFT = 43;
    APS_TEXTDISP_PARKING_SYS_EXITS = 44;
    APS_TEXTDISP_PARKING_SUSPENDED_FOLD_THE_MIRROR = 45;
    APS_TEXTDISP_TOO_SMALL_SLOT_PLEASE_SELECT_ANOTHER_DIRECTION = 46;
    APS_TEXTDISP_DRIVING_MODE_NOT_SUPPORTED = 47;
    APS_TEXTDISP_PATH_IS_SMOOTH = 48;
    APS_TEXTDISP_PLEASE_SELECT_PARK_OUT = 49;
    APS_TEXTDISP_PLEASE_SELECT_PARK_OUT_DIRECTION = 50;
    APS_TEXTDISP_PLEASE_GET_OFF_THE_CAR_WITH_KEYS_TO_START_REMOTE_PARKING = 51;
    APS_TEXTDISP_PLEASE_CONFIRM_TO_START_PARKING = 52;
    APS_TEXTDISP_REMOTE_PARKING_IN_EXIT = 53;
    APS_TEXTDISP_PARKING_SUSPENDED_REMOTE_CONNECTION_ERROR = 54;
    APS_TEXTDISP_SYSTEM_ERROR_PARKING_SYS_CAN_NOT_START = 55;
    APS_TEXTDISP_CONTROL_UNIT_ERROR_PARKING_SYS_CAN_NOT_START = 56;
    APS_TEXTDISP_PLEASE_TURN_P_AND_APPLY_EPB_REMOTE_PARKING_SYS_NOT_RUNNING = 57;
    APS_TEXTDISP_SELECT_REMOTE_PARKING_OUT_TYPE = 58;
    APS_TEXTDISP_PLEASE_BRAKE_AND_LONG_PRESS_SWITCH_2S_TO_ENTER_POC_RIGHT = 59;
    APS_TEXTDISP_PLEASE_RELEASE_SWITCH_TO_EN_TER_GUIDANCE = 60;
    APS_TEXTDISP_SLOPE_OVER_SYSTEM_EXITS = 61;
    APS_TEXTDISP_PLEASE_SELECT_SUMMON_PATH = 62;
    APS_TEXTDISP_RESERVED = 63;
    APS_TEXTDISP_PATH_STUDY_BEGIN_PLEASE_DRIVE_THE_CAR = 64;
    APS_TEXTDISP_PATH_STUDYING = 65;
    APS_TEXTDISP_CONFIRM_STUDY_FINISHED = 66;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_PATH_TOO_LONG = 67;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_SPEED_TOO_HIGH = 68;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 69;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_PATH_ERROR = 70;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_LEARN_TIME_TOO_LONG = 71;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_INTERNAL_ERROR = 72;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_PATH_TOO_SHORT = 73;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_PATH_BACKWARD_TOO_LONG = 74;
    APS_TEXTDISP_PATH_STUDYING_FAILED_CAUSED_BY_SHIFT_GEAR_TOO_MANY_TIMES = 75;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_NEED_5KM_H = 76;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_NEED_10KM_H = 77;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_NEED_15KM_H = 78;
    APS_TEXTDISP_RESERVED_79 = 79;
    APS_TEXTDISP_PATH_SAVING = 80;
    APS_TEXTDISP_PATH_SAVING_SUCCEED = 81;
    APS_TEXTDISP_PATH_SAVING_FAILED_CAUSED_BY_PATH_TOO_SHORT = 82;
    APS_TEXTDISP_PATH_SAVING_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 83;
    APS_TEXTDISP_PATH_SAVING_FAILED_CAUSED_BY_PATH_ERROR = 84;
    APS_TEXTDISP_PATH_SAVING_FAILED_CAUSED_BY_PATH_ERROR_LIMIT_STORAGE_ROOM = 85;
    APS_TEXTDISP_PATH_SAVING_FAILED_CAUSED_BY_INTERNAL_ERROR = 86;
    APS_TEXTDISP_RESERVED_87 = 87;
    APS_TEXTDISP_PLEASE_DRIVE_TO_PATH_START_POINT = 96;
    APS_TEXTDISP_PATH_MATCHE_PLEASE_STOP = 97;
    APS_TEXTDISP_PLEASE_SELECT_PLAY_BACK_MODE = 98;
    APS_TEXTDISP_PATH_MATCH_FAILED_CAUSED_BY_COMMUNICATION_ERROR = 99;
    APS_TEXTDISP_PATH_MATCH_FAILED_CAUSED_BY_PATH_ERROR = 100;
    APS_TEXTDISP_PATH_MATCH_FAILED_CAUSED_BY_LONG_TIME_NO_ACTION = 101;
    APS_TEXTDISP_PATH_MATCH_FAILED_CAUSED_BY_INTERNAL_ERROR = 102;
    APS_TEXTDISP_THE_MAXIMUN_PATH_LENGTH_IS_ABOUT_TO_BE_EXCEEDED = 103;
    APS_TEXTDISP_AUTO_PARKING_BE_READY_TO_BREAK = 104;
    APS_TEXTDISP_MAP_DATA_ABNORMAL_SYSTEM_EXIT = 105;
    APS_TEXTDISP_RESERVED_106 = 106;
    APS_TEXTDISP_NO_SAVE_DISTANCE_RADS_SYS_ESITS = 112;
    APS_TEXTDISP_RADS_RUNNING_BE_READY_TO_BRAKE = 113;
    APS_TEXTDISP_NEAR_THE_BOURN = 114;
    APS_TEXTDISP_ARRIVE_AT_THE_BOURN = 115;
    APS_TEXTDISP_RELEASE_THE_BRAKE = 116;
    APS_TEXTDISP_TRUNK_OPEN_RADS_SYS_EXITS = 117;
    APS_TEXTDISP_DOOR_OPEN_RADS_SYS_EXITS = 118;
    APS_TEXTDISP_SEAT_BELT_DROP_RADS_SYS_EXITS = 119;
    APS_TEXTDISP_MIRROR_FOLD_RADS_SYS_EXITS = 120;
    APS_TEXTDISP_OBSTACLES_DETECTED_RADS_SYS_EXITS = 121;
    APS_TEXTDISP_NO_CONTINUE_DRIVING_PARKING_QUITED = 122;
    APS_TEXTDISP_PLEASE_TURN_TO_R = 123;
    APS_TEXTDISP_RELEASE_THE_BRAKE_DRIVING_ASSISTANCE_START = 124;
    APS_TEXTDISP_PLEASE_TURN_TO_D = 125;
    APS_TEXTDISP_RADS_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 126;
    APS_TEXTDISP_RADS_RECOVERED_CONFIRM_TO_CONTINUE = 127;
    APS_TEXTDISP_FADS_RUNNING_BE_READY_TO_BRAKE = 128;
    APS_TEXTDISP_TRUNK_OPEN_FADS_SYS_EXITS = 129;
    APS_TEXTDISP_DOOR_OPEN_FADS_SYS_EXITS = 130;
    APS_TEXTDISP_SEAT_BELT_DROP_FADS_SYS_EXITS = 131;
    APS_TEXTDISP_MIRROR_FOLD_FADS_SYS_EXITS = 132;
    APS_TEXTDISP_OBSTACLES_DETECTED_FADS_SYS_EXITS = 133;
    APS_TEXTDISP_VEHICLE_CAN_NOT_MOVE_FADS_SYS_EXITS = 134;
    APS_TEXTDISP_REMOTE_CONTROL_STRAIGHT_SEARCH = 135;
    APS_TEXTDISP_OVER_LIMIT_SEARCH_DISTANCE = 136;
    APS_TEXTDISP_PLEASE_SELECT_SUITABLE_PARKING_SLOT_ICON = 137;
    APS_TEXTDISP_PLEASE_DRAG_THE_PARKING_SLOT_TO_SUITABLE_PLACE_AND_ADJUST_THE_ANGLE = 138;
    APS_TEXTDISP_MOVE_OVER_25M_SYSTEM_EXITS = 139;
    APS_TEXTDISP_MOVE_OVER_5M_SYSTEM_EXITS = 140;
    APS_TEXTDISP_PARKING_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 141;
    APS_TEXTDISP_RADS_SUSPENDED_DETECTE_OBSTACLES = 142;
    APS_TEXTDISP_FADS_SUSPENDED_DETECTE_OBSTACLES = 143;
    APS_TEXTDISP_FADS_RECOVERED_CONFIRM_TO_CONTINUE = 144;
    APS_TEXTDISP_FRONT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 145;
    APS_TEXTDISP_REAR_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 146;
    APS_TEXTDISP_LEFT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 147;
    APS_TEXTDISP_RIGHT_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 148;
    APS_TEXTDISP_DVR_CAMERA_DIRTY_PLEASE_CLEAN_THE_CAMERA = 149;
    APS_TEXTDISP_FADS_SUSPENDED_PLEASE_RELEASE_THE_BREAK_PEDAL = 150;
    APS_TEXTDISP_PLEASE_CLOSE_CABIN_COVER = 151;
    APS_TEXTDISP_PLEASE_PULL_OUT_THE_CHARGING_GUN = 152;
    APS_TEXTDISP_WOULD_YOU_LIKE_TO_PARK_IN = 153;
    APS_TEXTDISP_YOU_CAN_SELECT_SLOT_AND_PARK_IT = 154;
    APS_TEXTDISP_PAY_ATTENTION_TO_THE_RISK_OF_SCRATCHES = 155;
    APS_TEXTDISP_DETECTING_PARKING_SPACE = 156;
    APS_TEXTDISP_PLEASE_READY = 157;
    APS_TEXTDISP_GUIDE_SUCCESSFUL_PLEASE_START_CHARGING = 158;
    APS_TEXTDISP_WIRELESS_CHARGING_GUIDANCE_POSITIONING = 159;
    APS_TEXTDISP_WIRELESS_CHARGING_ALIGNED_PARKING_COMPLETE = 160;
    APS_TEXTDISP_PARKING_SUSPENDED_PEDESTRIAN_DETECTED = 161;
    APS_TEXTDISP_PARKING_SUSPENDED_VEHICLE_DETECTED = 162;
    APS_TEXTDISP_PARKING_CONTINUES = 163;
    APS_TEXTDISP_PARKING_OUT_COMPLETED_PLEASE_TAKE_OVER_VEHICLE = 164;
    APS_TEXTDISP_ENVIRONMENT_CANNOT_BE_PARKED = 165;
    APS_TEXTDISP_PUT_ON_THE_P_PULL_UP_EPB_CLOSE_DOOR_FOR_REMOTE_PARKING = 166;
    APS_TEXTDISP_CONNECTION_ABNORMAL_PARKING_CANNOT_BE_STARTED = 167;
    APS_TEXTDISP_REMOTE_PARKING_BE_READY_TO_STOP = 168;
    APS_TEXTDISP_SELECT_PARKING_OUT_MODE = 169;
    APS_TEXTDISP_PARKING_SUSPENDED_CLOSE_CABIN_COVER = 170;
    APS_TEXTDISP_RADS_SUSPENDED_LOOSEN_ACCELERATOR_PEDAL = 171;
    APS_TEXTDISP_RADS_SUSPENDED_CLOSE_CABIN_COVER = 172;
    APS_TEXTDISP_STEERING_WHEEL_INTERVENTION_RADS_EXITS = 173;
    APS_TEXTDISP_EPB_INTERVENTION_RADS_EXITS = 174;
    APS_TEXTDISP_GEAR_INTERVENTION_RADS_EXITS = 175;
    APS_TEXTDISP_SPEED_TOO_HIGH_RADS_EXITS = 176;
    APS_TEXTDISP_TIME_OUT_RADS_EXITS = 177;
    APS_TEXTDISP_TOO_MANY_TIMES_RADS_EXITS = 178;
    APS_TEXTDISP_TRUNK_OPEN_RADS_SYS_SUSPEND = 179;
    APS_TEXTDISP_DOOR_OPEN_RADS_SYS_SUSPEND = 180;
    APS_TEXTDISP_SEAT_BELT_DROP_RADS_SYS_SUSPEND = 181;
    APS_TEXTDISP_MIRROR_FOLD_RADS_SYS_SUSPEND = 182;
    APS_TEXTDISP_OBSTACLES_DETECTED_183 = 183;
    APS_TEXTDISP_DETECTED_OBSTACLES = 184;
    APS_TEXTDISP_AVOID_NEARBY_OBSTACLES = 185;
    APS_TEXTDISP_RADS_EXIT = 186;
    APS_TEXTDISP_SLOT_SMALL_USE_RPA = 187;
    APS_TEXTDISP_CAMERA_FAULT_SYS_EXITS = 188;
    APS_TEXTDISP_CAMERA_BLOCK_SYS_EXITS = 189;
    APS_TEXTDISP_SLOPE_OVER_SYS_UNAVAILABLE = 190;
    APS_TEXTDISP_OTHER_ASSISIT_ACTIVATE_SYS_EXITS = 191;
    APS_TEXTDISP_USS_SYSTEM_FAILURE_SYS_UNAVAILABLE = 192;
    APS_TEXTDISP_PARKING_SYSTEM_FAILURE_SYS_UNAVAILABLE = 193;
    APS_TEXTDISP_SPEED_IS_TOO_HIGH_SYS_UNAVAILABLE = 194;
    APS_TEXTDISP_CONTROL_UNIT_ERROR_SYS_UNAVAILABLE = 195;
    APS_TEXTDISP_AVM_NOT_CALIBRATED_SYS_UNAVAILABLE = 196;
    APS_TEXTDISP_CAMERA_FAILURE_SYS_UNAVAILABLE = 197;
    APS_TEXTDISP_OTHER_AUXILIARY_DRIVING_FUNCTIONS_ARE_ACTIVATED_SYS_UNAVAILABLE = 198;
    APS_TEXTDISP_ACTIVE_SECURITY_FEATURE_ACTIVATION_SYS_UNAVAILABLE = 199;
    APS_TEXTDISP_CAMERA_DIRTY_SYS_UNAVAILABLE = 200;
    APS_TEXTDISP_CAMERA_IS_BLOCKED_SYS_UNAVAILABLE = 201;
    APS_TEXTDISP_DRIVING_MODE_NOT_SUPPORTED_SYS_UNAVAILABLE = 202;
    APS_TEXTDISP_PATH_IS_UNOBSTRUCTED_SYS_UNAVAILABLE = 203;
    APS_TEXTDISP_SINGLE_CALL_EXCEEDS_THE_LIMIT_DISTANCE = 204;
    APS_TEXTDISP_ENCOUNTER_OBSTACLES_LATERALLY_PARKING_SUSPENSION = 205;
    APS_TEXTDISP_PARKING_SYSTEM_FAILURE_PLEASE_TAKE_OVER_THE_VEHICLE_IN_TIME = 206;
    APS_TEXTDISP_TRUNK_OPEN_FADS_SYS_SUSPEND = 207;
    APS_TEXTDISP_SYSTEM_EXIT_PLEASE_TAKE_OVER_THE_VEHICLE = 208;
    APS_TEXTDISP_DOOR_OPEN_FADS_SYS_SUSPEND = 209;
    APS_TEXTDISP_SEAT_BELT_DROP_FADS_SYS_SUSPEND = 210;
    APS_TEXTDISP_MIRROR_FOLD_FADS_SYS_SUSPEND = 211;
    APS_TEXTDISP_FADS_EXIT = 212;
    APS_TEXTDISP_FADS_SUSPENDED_LOOSEN_ACCELERATOR_PEDAL = 213;
    APS_TEXTDISP_SPEED_TOO_HIGH_FADS_EXITS = 214;
    APS_TEXTDISP_SLOPE_EXCEEDING_LIMIT_PLEASE_LEAVE_THE_RAMP = 215;
    APS_TEXTDISP_VEHICLE_NOT_READY_PARKING_SYSTEM_EXIT = 216;
    APS_TEXTDISP_SUSPENSION_MODE_NOT_SUPPORTED_SYS_UNAVAILABLE = 217;
    APS_TEXTDISP_SYS_UNAVAILABLE = 218;
    APS_TEXTDISP_PARKING_HAS_NOT_STARTED_PLEASE_BE_AWARE_OF_THE_SLIP_DRIVE = 219;
    APS_TEXTDISP_TRAJECTORY_PLANNING = 220;
    APS_TEXTDISP_TRAJECTORY_PLANNING_FAILED = 221;
    APS_TEXTDISP_LOOKING_FOR_A_PARKING_SPACE = 222;
    APS_TEXTDISP_PARK_AT_THE_REAR_OF_THE_CAR_CLICK_START_PARKING = 223;
    APS_TEXTDISP_PARK_AT_THE_FRONT_OF_THE_CAR_CLICK_START_PARKING = 224;
    APS_TEXTDISP_CAN_NOT_PARK_IN_PARK_AND_EXIT = 225;
    APS_TEXTDISP_REMOTE_PARKING_IS_NOT_AVAILABLE = 226;
    APS_TEXTDISP_THE_REAR_OF_THE_CAR_IS_PARKED_AND_SLOT_SMALL_USE_RPA = 227;
    APS_TEXTDISP_THE_FRONT_OF_THE_CAR_IS_PARKED_AND_SLOT_SMALL_USE_RPA = 228;
    APS_TEXTDISP_THE_MIRRORS_ARE_FOLDED_BE_AWARE_OF_YOUR_SURROUNDINGS = 229;
    APS_TEXTDISP_SUSPENSION_MODE_NOT_SUPPORTED_SYS_UNAVAILABLE_230 = 230;
    APS_TEXTDISP_NO_DRIVING_TRAJECTORY_THE_SYSTEM_IS_TEMPORARILY_UNAVAILABLE = 231;
    APS_TEXTDISP_LIDAR_FAILURE_SYS_UNAVAILABLE = 232;
    APS_TEXTDISP_LIDAR_OBSTRUCTION_SYS_UNAVAILABLE = 233;
    APS_TEXTDISP_LIDAR_OBSTRUCTION_SYS_EXITS = 234;
    APS_TEXTDISP_LIDAR_ABNORMAL_SYS_EXITS = 235;
    APS_TEXTDISP_USS_FAILURE_SYS_EXITS = 236;
    APS_TEXTDISP_UNABLE_PARK_OUT_SYS_EXIT = 237;
    APS_TEXTDISP_PLEASE_PRESS_THE_BRAKE_AND_SELECT_THE_PARKING_DIRECTION = 238;
    APS_TEXTDISP_UNMANNED_PARKING_PROCESS = 239;
    APS_TEXTDISP_UNMANNED_PARKING_IS_SUSPENDED = 240;
    APS_TEXTDISP_PRESS_THE_BRAKES_AND_ADJUST_THE_PARKING_POSITION = 241;
    APS_TEXTDISP_THE_REARVIEW_MIRROR_IS_ABOUT_TO_BE_FOLDED = 242;
    APS_TEXTDISP_UNMANNED_PARKING_IS_SUCCESS = 243;
    APS_TEXTDISP_UNMANNED_PARKING_ABNORMAL_SYS_EXIT = 244;
  }
  enum AVPESPLSMSubMTLevelType {
    AVP_ESP_LSMSUBMTLEVEL_MTLEVEL_NONE = 0;
    AVP_ESP_LSMSUBMTLEVEL_MTLEVEL_12 = 1;
    AVP_ESP_LSMSUBMTLEVEL_MTLEVEL_23 = 2;
    AVP_ESP_LSMSUBMTLEVEL_MTLEVEL_RESERVED = 3;
  }
  enum AVPESPLSMSubMTReqType {
    AVP_ESP_LSMSUBMTREQ_MTREQ_NONE = 0;
    AVP_ESP_LSMSUBMTREQ_MTREQ_DRIVE = 1;
    AVP_ESP_LSMSUBMTREQ_MTREQ_LSM = 2;
    AVP_ESP_LSMSUBMTREQ_RESERVED = 3;
  }
  enum AVPESPLSMSubMTLongType {
    AVP_ESP_LSMSUBMTLONG_MTLONG_NONE = 0;
    AVP_ESP_LSMSUBMTLONG_MTLONG_COMFORT = 1;
    AVP_ESP_LSMSUBMTLONG_MTLONG_EMERGENCY = 2;
    AVP_ESP_LSMSUBMTLONG_MTLONG_RESERVED = 3;
  }
  enum AVPESPLSMVehDirRqType {
    AVP_ESP_LSMVEHDIRRQ_DIRREQ_NONE = 0;
    AVP_ESP_LSMVEHDIRRQ_DIRREQ_FORWARD = 1;
    AVP_ESP_LSMVEHDIRRQ_DIRREQ_BACKWARD = 2;
    AVP_ESP_LSMVEHDIRRQ_RESERVED = 3;
  }
  enum AVPESPLSMComfBrakeReqType {
    AVP_ESP_LSMCOMFBRAKEREQ_COMFBRAKEREQ_NOREQ = 0;
    AVP_ESP_LSMCOMFBRAKEREQ_COMFBRAKEREQ_TYPE1 = 1;
    AVP_ESP_LSMCOMFBRAKEREQ_COMFBRAKEREQ_TYPE2 = 2;
    AVP_ESP_LSMCOMFBRAKEREQ_RESERVED = 3;
  }
  enum HAPSwtDispCtrlCmdType {
    HAP_SWTDISPCTRLCMD_NO_DISPLAY = 0;
    HAP_SWTDISPCTRLCMD_PARK_OUT_DIRECTION_SELECT_MENU = 1;
    HAP_SWTDISPCTRLCMD_CONTINUE_PARK_MENU = 2;
    HAP_SWTDISPCTRLCMD_STUDY_FINISH_BUTTON = 3;
    HAP_SWTDISPCTRLCMD_CONTINUE_RADS_MENU = 4;
    HAP_SWTDISPCTRLCMD_RDAS_ACTIVE_DISTANCE_ARROW_STEERING = 5;
    HAP_SWTDISPCTRLCMD_CONTINUE_FADS_MENU = 6;
    HAP_SWTDISPCTRLCMD_RESERVED = 7;
  }
  enum P2PPahDelStsType {
    P2P_PAHDELSTS_NO_ACTION = 0;
    P2P_PAHDELSTS_DETECTING = 1;
    P2P_PAHDELSTS_SUCCESSED = 2;
    P2P_PAHDELSTS_FAILED = 3;
  }
  enum P2PSelfDetErrCodeType {
    P2P_SELFDETERRCODE_NO_ERROR = 0;
    P2P_SELFDETERRCODE_HAP_ERROR = 1;
    P2P_SELFDETERRCODE_TYRE_PRESSURE_ERROR = 2;
    P2P_SELFDETERRCODE_DOOR_OPEN = 3;
    P2P_SELFDETERRCODE_TRUNK_OPEN = 4;
    P2P_SELFDETERRCODE_VEHICLE_MOVING = 5;
    P2P_SELFDETERRCODE_ENVIRONMENT_ERROR = 6;
    P2P_SELFDETERRCODE_CAMERA_IS_SMUDGY = 7;
    P2P_SELFDETERRCODE_SEAT_BELT_NOT_FASTENED = 8;
    P2P_SELFDETERRCODE_MIRROR_IS_FOLDED = 9;
    P2P_SELFDETERRCODE_ASSOCIATED_SYSTEM_ERROR = 10;
    P2P_SELFDETERRCODE_RESERVED = 11;
  }
  enum P2PSelfDetStsType {
    P2P_SELFDETSTS_NO_ACTION = 0;
    P2P_SELFDETSTS_DETECTING = 1;
    P2P_SELFDETSTS_SUCCESSED = 2;
    P2P_SELFDETSTS_FAILED = 3;
  }
  enum APSPASSwtReqType {
    APS_PASSWTREQ_NO_REQUEST = 0;
    APS_PASSWTREQ_REQUEST_TO_CLOSE = 1;
    APS_PASSWTREQ_REQUEST_TO_OPEN = 2;
    APS_PASSWTREQ_RESERVED = 3;
  }
  enum HAPPrkgModCurrStsType {
    HAP_PRKGMODCURRSTS_NO_DISPLAY = 0;
    HAP_PRKGMODCURRSTS_LEFT_PARALLEL_PARK_IN = 1;
    HAP_PRKGMODCURRSTS_RIGHT_PARALLEL_PARK_IN = 2;
    HAP_PRKGMODCURRSTS_LEFT_VERT_PARK_IN = 3;
    HAP_PRKGMODCURRSTS_RIGHT_VERT_PARK_IN = 4;
    HAP_PRKGMODCURRSTS_LEFT_TSHAPE_PARK_IN = 5;
    HAP_PRKGMODCURRSTS_RIGHT_TSHAPE_PARK_IN = 6;
    HAP_PRKGMODCURRSTS_LEFT_PARALLEL_PARK_OUT = 7;
    HAP_PRKGMODCURRSTS_RIGHT_PARALLEL_PARK_OUT = 8;
    HAP_PRKGMODCURRSTS_FRONT_VERT_PARK_OUT = 9;
    HAP_PRKGMODCURRSTS_REAR_VERT_PARK_OUT = 10;
    HAP_PRKGMODCURRSTS_P2P_SUMMON = 11;
    HAP_PRKGMODCURRSTS_LEFT_FRONT_VERT_PARK_OUT = 12;
    HAP_PRKGMODCURRSTS_RIGHT_FRONT_VERT_PARK_OUT = 13;
    HAP_PRKGMODCURRSTS_LEFT_REAR_VERT_PARK_OUT = 14;
    HAP_PRKGMODCURRSTS_RIGHT_REAR_VERT_PARK_OUT = 15;
  }
  enum APSMovgDircType {
    APS_MOVGDIRC_NO_DISPLAY = 0;
    APS_MOVGDIRC_MOVING_FORWARD = 1;
    APS_MOVGDIRC_MOVING_BACKWARD = 2;
    APS_MOVGDIRC_RESERVED = 3;
  }
  enum AVPTBOXWorkStsType {
    AVP_T_BOX_WORKSTS_OFF = 0;
    AVP_T_BOX_WORKSTS_HAVP_ON = 1;
    AVP_T_BOX_WORKSTS_PAVP_ON = 2;
    AVP_T_BOX_WORKSTS_FAILURE = 3;
  }
  enum AVPHiBeamtReqType {
    AVP_HIBEAMTREQ_NO_REQUEST = 0;
    AVP_HIBEAMTREQ_TURN_OFF = 1;
    AVP_HIBEAMTREQ_TURN_ON = 2;
    AVP_HIBEAMTREQ_RESERVED = 3;
  }
  enum AVPDippedBeamReqType {
    AVP_DIPPEDBEAMREQ_NO_REQUEST = 0;
    AVP_DIPPEDBEAMREQ_TURN_OFF = 1;
    AVP_DIPPEDBEAMREQ_TURN_ON = 2;
    AVP_DIPPEDBEAMREQ_RESERVED = 3;
  }
  enum AVPHornReqType {
    AVP_HORNREQ_NO_REQUEST = 0;
    AVP_HORNREQ_TURN_OFF = 1;
    AVP_HORNREQ_TURN_ON = 2;
    AVP_HORNREQ_RESERVED = 3;
  }
  enum AVPFWiperReqType {
    AVP_FWIPERREQ_NO_REQUEST = 0;
    AVP_FWIPERREQ_WIPER_AUTO_OR_INTERMITTENT = 1;
    AVP_FWIPERREQ_LOW_SPEED = 2;
    AVP_FWIPERREQ_HIGH_SPEED = 3;
    AVP_FWIPERREQ_WIPER_OFF = 4;
    AVP_FWIPERREQ_RESERVED = 5;
    AVP_FWIPERREQ_ERROR = 7;
  }
  enum AVPFWshrReqType {
    AVP_FWSHRREQ_NO_REQUEST = 0;
    AVP_FWSHRREQ_WIPER_WASH = 1;
    AVP_FWSHRREQ_WASH_IN_AUTO_MODE = 2;
    AVP_FWSHRREQ_OFF = 3;
  }
  enum AVPPowerModeReqType {
    AVP_POWERMODEREQ_NO_REQUEST = 0;
    AVP_POWERMODEREQ_POWER_OFF_REQUEST = 1;
    AVP_POWERMODEREQ_POWER_ON_REQUEST = 2;
    AVP_POWERMODEREQ_RESERVED = 3;
  }
  enum AVPWindLockReqType {
    AVP_WINDLOCKREQ_NO_REQUEST = 0;
    AVP_WINDLOCKREQ_REQUEST_TO_CLOSE = 1;
    AVP_WINDLOCKREQ_REQUEST_TO_OPEN = 2;
    AVP_WINDLOCKREQ_RESERVED = 3;
  }
  enum AVPSunroofCloseReqType {
    AVP_SUNROOFCLOSEREQ_NO_REQUEST = 0;
    AVP_SUNROOFCLOSEREQ_REQUEST_TO_CLOSE = 1;
    AVP_SUNROOFCLOSEREQ_REQUEST_TO_OPEN = 2;
    AVP_SUNROOFCLOSEREQ_RESERVED = 3;
  }
  enum AVPCenCtrllockReqType {
    AVP_CENCTRLLOCKREQ_NO_REQUEST = 0;
    AVP_CENCTRLLOCKREQ_LOCK = 1;
    AVP_CENCTRLLOCKREQ_UNLOCK = 2;
    AVP_CENCTRLLOCKREQ_RESERVED = 3;
  }
  enum ParallelSlotBtnEnaType {
    PARALLELSLOTBTN_ENA_NO_DISPLAY = 0;
    PARALLELSLOTBTN_ENA_DISABLE = 1;
    PARALLELSLOTBTN_ENA_ENABLE = 2;
    PARALLELSLOTBTN_ENA_ACTIVE = 3;
  }
  enum VerticalSlotBtnEnaType {
    VERTICALSLOTBTN_ENA_NO_DISPLAY = 0;
    VERTICALSLOTBTN_ENA_DISABLE = 1;
    VERTICALSLOTBTN_ENA_ENABLE = 2;
    VERTICALSLOTBTN_ENA_ACTIVE = 3;
  }
  enum ClockwiseBtnEnaType {
    CLOCKWISEBTN_ENA_NO_DISPLAY = 0;
    CLOCKWISEBTN_ENA_DISABLE = 1;
    CLOCKWISEBTN_ENA_ENABLE = 2;
    CLOCKWISEBTN_ENA_ACTIVE = 3;
  }
  enum AnticlockwiseBtnEnaType {
    ANTICLOCKWISEBTN_ENA_NO_DISPLAY = 0;
    ANTICLOCKWISEBTN_ENA_DISABLE = 1;
    ANTICLOCKWISEBTN_ENA_ENABLE = 2;
    ANTICLOCKWISEBTN_ENA_ACTIVE = 3;
  }
  enum SlotConfirmBtnEnaType {
    SLOTCONFIRMBTN_ENA_NO_DISPLAY = 0;
    SLOTCONFIRMBTN_ENA_DISABLE = 1;
    SLOTCONFIRMBTN_ENA_ENABLE = 2;
    SLOTCONFIRMBTN_ENA_ACTIVE = 3;
  }
  enum HAVPStartBtnEnaType {
    HAVP_STARTBTN_ENA_NO_DISPLAY = 0;
    HAVP_STARTBTN_ENA_DISABLE = 1;
    HAVP_STARTBTN_ENA_ENABLE = 2;
    HAVP_STARTBTN_ENA_ACTIVE = 3;
  }
  enum HAVPStopBtnEnaType {
    HAVP_STOPBTN_ENA_NO_DISPLAY = 0;
    HAVP_STOPBTN_ENA_DISABLE = 1;
    HAVP_STOPBTN_ENA_ENABLE = 2;
    HAVP_STOPBTN_ENA_ACTIVE = 3;
  }
  enum HAVPSelfParLotEndingEnaType {
    HAVP_SELFPARLOTENDING_ENA_NO_DISPLAY = 0;
    HAVP_SELFPARLOTENDING_ENA_DISABLE = 1;
    HAVP_SELFPARLOTENDING_ENA_ENABLE = 2;
    HAVP_SELFPARLOTENDING_ENA_ACTIVE = 3;
  }
  enum HAVPWorkStsType {
    HAVP_WORKSTS_OFF = 0;
    HAVP_WORKSTS_STANDBY = 1;
    HAVP_WORKSTS_POSITION = 2;
    HAVP_WORKSTS_PREACTIVE = 3;
    HAVP_WORKSTS_RESTORE_CRUISING = 4;
    HAVP_WORKSTS_SUMMON_CRUSING = 5;
    HAVP_WORKSTS_PARKING = 6;
    HAVP_WORKSTS_OUTING = 7;
    HAVP_WORKSTS_COMPLETED = 8;
    HAVP_WORKSTS_SUSPEND = 9;
    HAVP_WORKSTS_ABORT = 10;
    HAVP_WORKSTS_FAILURE = 11;
    HAVP_WORKSTS_DISABLE = 12;
    HAVP_WORKSTS_STUDY = 13;
    HAVP_WORKSTS_RESERVED = 14;
  }
  enum PAVPTextDisType {
    PAVP_TEXTDIS_NO_TEXT = 0;
    PAVP_TEXTDIS_SELFCHECKING = 1;
    PAVP_TEXTDIS_AVP_UNAVAILABLE = 2;
    PAVP_TEXTDIS_MAP_UPDATE_FAILED = 3;
    PAVP_TEXTDIS_WRONG_POSITION = 4;
    PAVP_TEXTDIS_REQ_CLOSE_DOOR_ALL_DOORS = 5;
    PAVP_TEXTDIS_REQ_CLOSE_HOOD_DOOR = 6;
    PAVP_TEXTDIS_REQ_CLOSE_TRUNK_DOOR = 7;
    PAVP_TEXTDIS_REQ_GEAR_P_EPB = 8;
    PAVP_TEXTDIS_PATH_DOWNLOAD_FAILED = 9;
    PAVP_TEXTDIS_POWER_OFF_FAILED = 10;
    PAVP_TEXTDIS_PICK_UP_LOCATION = 11;
    PAVP_TEXTDIS_DROP_OFF_AREA = 12;
    PAVP_TEXTDIS_NO_PARKING_LOT = 13;
    PAVP_TEXTDIS_PARKING_IN_INPROCESS = 14;
    PAVP_TEXTDIS_PARKING_IN_COMPLETE = 15;
    PAVP_TEXTDIS_TASK_FAILED = 16;
    PAVP_TEXTDIS_PARKING_OUT_IN_PROCESS = 17;
    PAVP_TEXTDIS_CRUISING = 18;
    PAVP_TEXTDIS_PARKING_OUT_COMPLETE = 19;
    PAVP_TEXTDIS_REQ_QUIT_SYSTEM_FAILURE = 20;
    PAVP_TEXTDIS_REQ_SUSPEND_USER_REQ = 21;
    PAVP_TEXTDIS_REQ_QUIT_EPS_FAILURE = 22;
    PAVP_TEXTDIS_REQ_QUIT_ESP_FAILURE = 23;
    PAVP_TEXTDIS_REQ_ABORT_TERMINATE_BUTTON_PRESSED_HMI_CANCEL_RD_CANCEL = 24;
    PAVP_TEXTDIS_REQ_ABORT_STEERING_WHEEL_INTERVENTION = 25;
    PAVP_TEXTDIS_REQ_ABORT_GEAR_INTERVENTION = 26;
    PAVP_TEXTDIS_REQ_ABORT_EPB_INTERVENTION = 27;
    PAVP_TEXTDIS_REQ_ABORT_FUNCTION_ACTIVATION = 28;
    PAVP_TEXTDIS_RESERVED = 29;
    PAVP_TEXTDIS_REQ_ABORT_PARKING_TIMEOUT = 32;
    PAVP_TEXTDIS_REQ_ABORT_SPEED_HIGH = 33;
    PAVP_TEXTDIS_REQ_ABORT_PARKING_TOO_MANY_TIMES = 34;
    PAVP_TEXTDIS_REQ_ABORT_HOOD_OPEN = 35;
    PAVP_TEXTDIS_REQ_ABORT_TRUNK_OPEN = 36;
    PAVP_TEXTDIS_REQ_ABORT_DOOR_OPEN = 37;
    PAVP_TEXTDIS_REQ_ABORT_DEFICIENT_SPACE = 38;
    PAVP_TEXTDIS_REQ_ABORT_NETWORK_DISCONNECTION = 39;
    PAVP_TEXTDIS_REQ_ABORT_PATH_INVALID = 40;
    PAVP_TEXTDIS_REQ_ABORT_PARKING_LOT_INVALID = 41;
    PAVP_TEXTDIS_REQ_ABORT_REAR_MIRROR_FOLD = 42;
    PAVP_TEXTDIS_REQ_ABORT_ODD_DISSATISFIED = 43;
    PAVP_TEXTDIS_REQ_ABORT_SUSPEND_TOO_MANY_TIMES = 44;
    PAVP_TEXTDIS_REQ_ABORT_SUSPEND_TIMEOUT = 45;
    PAVP_TEXTDIS_REQ_ABORT_TIRE_PRESSURE = 46;
    PAVP_TEXTDIS_REQ_ABORT_TBOX_ERR = 47;
    PAVP_TEXTDIS_NETWORK_FAULT = 48;
    PAVP_TEXTDIS_NO_PARKING_AREA = 49;
    PAVP_TEXTDIS_PARKINGLIST_SCREEN = 50;
    PAVP_TEXTDIS_RESERVED_51 = 51;
  }
  enum PAVPStatusType {
    PAVP_STATUS_OFF = 0;
    PAVP_STATUS_STANDBY = 1;
    PAVP_STATUS_POSITION = 2;
    PAVP_STATUS_PREACTIVE = 3;
    PAVP_STATUS_RESTORE_CRUISING = 4;
    PAVP_STATUS_SUMMON_CRUSING = 5;
    PAVP_STATUS_PARKING = 6;
    PAVP_STATUS_OUTING = 7;
    PAVP_STATUS_COMPLETED = 8;
    PAVP_STATUS_SUSPEND = 9;
    PAVP_STATUS_ABORT = 10;
    PAVP_STATUS_FAILURE = 11;
    PAVP_STATUS_DISABLE = 12;
    PAVP_STATUS_RESERVED = 13;
  }
  enum PAVPTextDis2Type {
    PAVP_TEXTDIS2_NO_TEX = 0;
    PAVP_TEXTDIS2_DROP_OFF_AREA_PICTURE_REQUEST = 1;
    PAVP_TEXTDIS2_PATH_LIST_SHOWING_REQUEST = 2;
    PAVP_TEXTDIS2_FIND_A_SLOT_DO_YOU_WANT_TO_START_PARKING_OR_RPA = 3;
    PAVP_TEXTDIS2_DO_YOU_WANT_TO_START_PARKING = 4;
    PAVP_TEXTDIS2_WHICH_SLOTS_DO_YOU_WANT_TO_PARK_AND_START_PARKING = 5;
    PAVP_TEXTDIS2_WHICH_SLOTS_DO_YOU_WANT_TO_PARK_AND_START_PARKING_NO_RPA = 6;
    PAVP_TEXTDIS2_WHICH_SLOTS_DO_YOU_WANT_TO_PARK = 7;
    PAVP_TEXTDIS2_DO_YOU_WANT_TO_START_PARKING_OR_RPA = 8;
  }
  enum HAVPFuncStsType {
    HAVP_FUNCSTS_HAVP_FUNCTION_STANDBY = 0;
    HAVP_FUNCSTS_HAVP_FUNCTION_ACTIVE = 1;
    HAVP_FUNCSTS_HAVP_FUNCTION_DISABLE = 2;
    HAVP_FUNCSTS_RESERVED = 3;
  }
  enum PAVPFuncStsType {
    PAVP_FUNCSTS_PAVP_FUNCTION_STANDBY = 0;
    PAVP_FUNCSTS_PAVP_FUNCTION_DISABLE = 1;
    PAVP_FUNCSTS_RESERVED = 2;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_apa1 = 1;
  // [] [0|7] [initial_value:0]
  optional APABrkFctnModType apa_brk_fctn_mod = 2;
  // [m/s2] [-10|6] [initial_value:0]
  optional double apa_ax_req = 3;
  // [] [0|1] [initial_value:0]
  optional bool apa_ax_req_ena = 4;
  // [cm] [0|1023] [initial_value:0]
  optional int32 apa_disto_stop = 5;
  // [] [0|3] [initial_value:0]
  optional APAApbReqType apa_apb_req = 6;
  // [] [0|1] [initial_value:0]
  optional bool apa_ap_emgy_brk_req = 7;
  // [] [0|1] [initial_value:0]
  optional bool apa_ap_drive_off_req = 8;
  // [] [0|1] [initial_value:0]
  optional bool apa_ap_standstill_req = 9;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_apa1 = 10;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_aps4 = 11;
  // [] [0|7] [initial_value:0]
  optional APSBrkFctnModType aps_brk_fctn_mod = 12;
  // [] [0|7] [initial_value:0]
  optional APSESPTgtGearReqType aps_esp_tgt_gear_req = 13;
  // [kmh] [0|25.5] [initial_value:0]
  optional double aps_esp_spd_limn = 14;
  // [] [0|7] [initial_value:0]
  optional APSBrkModStsType aps_brk_mod_sts = 15;
  // [cm] [0|4095] [initial_value:0]
  optional int32 aps_esp_brk_distance = 16;
  // [] [0|3] [initial_value:0]
  optional APSBrkCategoryType aps_brk_category = 17;
  // [m/s^2] [-10|0] [initial_value:200]
  optional double meb_brkg_req_value = 18;
  // [] [0|1] [initial_value:0]
  optional bool meb_brkg_req = 19;
  // [] [0|1] [initial_value:0]
  optional bool meb_err_sts = 20;
  // [] [0|7] [initial_value:0]
  optional APSWorkStsType aps_work_sts = 21;
  // [] [0|1] [initial_value:0]
  optional bool meb_avh_req = 22;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool l_tail_vert_prkg_out_vald = 23;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool r_tail_vert_prkg_out_vald = 24;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool l_head_vert_prkg_out_vald = 25;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool r_head_vert_prkg_out_vald = 26;
  // [NoUnit] [0|3] [initial_value:0]
  optional PrkModType prk_mod = 27;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_aps4 = 28;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_aps1 = 29;
  // [] [0|3] [initial_value:0]
  optional APSPrkgModType aps_prkg_mod = 30;
  // [%] [0|127] [initial_value:127]
  optional APSProcBarType aps_proc_bar = 31;
  // [] [0|1] [initial_value:0]
  optional bool aps_swt_sts = 32;
  // [] [0|7] [initial_value:0]
  optional APSSlotDispType aps_slot_disp = 33;
  // [] [0|1] [initial_value:0]
  optional bool aps_req_eps_tgt_ang_valid = 34;
  // [] [0|1] [initial_value:0]
  optional bool aps_req_ctrl_eps = 35;
  // [deg] [-1638.3|1638.3] [initial_value:0]
  optional double aps_req_eps_tgt_ang = 36;
  // [] [0|3] [initial_value:0]
  optional TurnLightsCmdType turn_lights_cmd = 37;
  // [] [0|7] [initial_value:0]
  optional APSSysSoundIndcnType aps_sys_sound_indcn = 38;
  // [] [0|3] [initial_value:0]
  optional APSPrkgTypType aps_prkg_typ = 39;
  // [] [0|255] [initial_value:0]
  optional APSTextDispType aps_text_disp = 40;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_aps1 = 41;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_avp1 = 42;
  // [] [0|3] [initial_value:0]
  optional AVPESPLSMSubMTLevelType avp_esp_lsm_sub_mt_level = 43;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_esp_lsm_prefill = 44;
  // [NoUnit] [0|3] [initial_value:0]
  optional AVPESPLSMSubMTReqType avp_esp_lsm_sub_mt_req = 45;
  // [NoUnit] [0|3] [initial_value:0]
  optional AVPESPLSMSubMTLongType avp_esp_lsm_sub_mt_long = 46;
  // [kph] [0|20] [initial_value:0]
  optional double avp_esp_ls_mv_max_rq = 47;
  // [cm] [0|4095] [initial_value:0]
  optional int32 avp_esp_lsm_dist_to_stop = 48;
  // [NoUnit] [0|3] [initial_value:0]
  optional AVPESPLSMVehDirRqType avp_esp_lsm_veh_dir_rq = 49;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_esp_lsm_veh_sec_req = 50;
  // [NoUnit] [0|3] [initial_value:0]
  optional AVPESPLSMComfBrakeReqType avp_esp_lsm_comf_brake_req = 51;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_esp_driver_presence_external = 52;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_esp_lsm_nudge_req = 53;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_esp_safe_drvr_handover_req = 54;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_avp1 = 55;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_hap1 = 56;
  // [] [0|1] [initial_value:0]
  optional bool rmt_prkg_req = 57;
  // [] [0|1] [initial_value:0]
  optional bool l_para_prkg_out_vald = 58;
  // [] [0|1] [initial_value:0]
  optional bool r_para_prkg_out_vald = 59;
  // [] [0|1] [initial_value:0]
  optional bool head_vert_prkg_out_vald = 60;
  // [] [0|1] [initial_value:0]
  optional bool tail_vert_prkg_out_vald = 61;
  // [] [0|7] [initial_value:0]
  optional HAPSwtDispCtrlCmdType hap_swt_disp_ctrl_cmd = 62;
  // [] [0|3] [initial_value:0]
  optional P2PPahDelStsType p2_p_pah_del_sts = 63;
  // [] [0|15] [initial_value:0]
  optional P2PSelfDetErrCodeType p2_p_self_det_err_code = 64;
  // [] [0|3] [initial_value:0]
  optional P2PSelfDetStsType p2_p_self_det_sts = 65;
  // [] [0|1] [initial_value:0]
  optional bool p2_p_pah1_avail = 66;
  // [] [0|1] [initial_value:0]
  optional bool p2_p_pah2_avail = 67;
  // [] [0|3] [initial_value:0]
  optional APSPASSwtReqType aps_pas_swt_req = 68;
  // [] [0|15] [initial_value:0]
  optional HAPPrkgModCurrStsType hap_prkg_mod_curr_sts = 69;
  // [] [0|3] [initial_value:0]
  optional APSMovgDircType aps_movg_dirc = 70;
  // [] [0|1] [initial_value:0]
  optional bool p2_p_prkg_direct_sts = 71;
  // [] [0|3] [initial_value:0]
  optional AVPTBOXWorkStsType avp_t_box_work_sts = 72;
  // [] [0|3] [initial_value:0]
  optional AVPHiBeamtReqType avp_hi_beamt_req = 73;
  // [] [0|3] [initial_value:0]
  optional AVPDippedBeamReqType avp_dipped_beam_req = 74;
  // [] [0|3] [initial_value:0]
  optional AVPHornReqType avp_horn_req = 75;
  // [] [0|7] [initial_value:0]
  optional AVPFWiperReqType avp_f_wiper_req = 76;
  // [] [0|3] [initial_value:0]
  optional AVPFWshrReqType avp_f_wshr_req = 77;
  // [] [0|3] [initial_value:0]
  optional AVPPowerModeReqType avp_power_mode_req = 78;
  // [] [0|3] [initial_value:0]
  optional AVPWindLockReqType avp_wind_lock_req = 79;
  // [] [0|3] [initial_value:0]
  optional AVPSunroofCloseReqType avp_sunroof_close_req = 80;
  // [] [0|3] [initial_value:0]
  optional AVPCenCtrllockReqType avp_cen_ctrllock_req = 81;
  // [] [0|1] [initial_value:0]
  optional bool rmt_ctrl_sts = 82;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_f_fog_light_req = 83;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool avp_r_fog_light_req = 84;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_hap1 = 85;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_hap3 = 86;
  // [] [0|3] [initial_value:0]
  optional ParallelSlotBtnEnaType parallel_slot_btn_ena = 87;
  // [] [0|3] [initial_value:0]
  optional VerticalSlotBtnEnaType vertical_slot_btn_ena = 88;
  // [] [0|3] [initial_value:0]
  optional ClockwiseBtnEnaType clockwise_btn_ena = 89;
  // [] [0|3] [initial_value:0]
  optional AnticlockwiseBtnEnaType anticlockwise_btn_ena = 90;
  // [] [0|3] [initial_value:0]
  optional SlotConfirmBtnEnaType slot_confirm_btn_ena = 91;
  // [] [0|3] [initial_value:0]
  optional HAVPStartBtnEnaType havp_start_btn_ena = 92;
  // [] [0|3] [initial_value:0]
  optional HAVPStopBtnEnaType havp_stop_btn_ena = 93;
  // [] [0|3] [initial_value:0]
  optional HAVPSelfParLotEndingEnaType havp_self_par_lot_ending_ena = 94;
  // description B [] [0|15] [initial_value:0]
  optional HAVPWorkStsType havp_work_sts = 95;
  // [] [0|63] [initial_value:0]
  optional PAVPTextDisType pavp_text_dis = 96;
  // description B [] [0|15] [initial_value:0]
  optional PAVPStatusType pavp_status = 97;
  // [] [0|63] [initial_value:0]
  optional PAVPTextDis2Type pavp_text_dis2 = 98;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_hap3 = 99;
  // [NoUnit] [0|3] [initial_value:2]
  optional HAVPFuncStsType havp_func_sts = 100;
  // [NoUnit] [0|3] [initial_value:1]
  optional PAVPFuncStsType pavp_func_sts = 101;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_hap_fd1 = 102;
}

message CR_FD1_15E {
// Control Message
  enum FCTAStateType {
    FCTA_STATE_INITIAL_VALUE = 0;
    FCTA_STATE_OFF = 1;
    FCTA_STATE_STANDBY = 2;
    FCTA_STATE_ACTIVE = 3;
    FCTA_STATE_PASSIVE = 4;
    FCTA_STATE_FAULT = 5;
    FCTA_STATE_RESERVED = 6;
  }
  enum FCTBStateType {
    FCTB_STATE_INITIAL_VALUE = 0;
    FCTB_STATE_OFF = 1;
    FCTB_STATE_STANDBY = 2;
    FCTB_STATE_ACTIVE = 3;
    FCTB_STATE_PASSIVE = 4;
    FCTB_STATE_FAULT = 5;
    FCTB_STATE_HOLD = 6;
    FCTB_STATE_RESERVED = 7;
  }
  enum FCTABFuncStsType {
    FCTA_B_FUNCSTS_NO_DISPLAY = 0;
    FCTA_B_FUNCSTS_ERROR = 1;
    FCTA_B_FUNCSTS_FCTA_B_TEMPORARILY_UNAVAILABLE = 2;
    FCTA_B_FUNCSTS_FCTA_B_PERFORMANCE_DEGRADATION = 3;
  }
  enum FCTBABALevelType {
    FCTB_ABA_LEVEL_LEVEL_0 = 0;
    FCTB_ABA_LEVEL_LEVEL_1 = 1;
    FCTB_ABA_LEVEL_LEVEL_2 = 2;
    FCTB_ABA_LEVEL_LEVEL_3 = 3;
  }
  enum CRObjMotionTypeLeType {
    CR_OBJMOTIONTYPE_LE_INT = 0;
    CR_OBJMOTIONTYPE_LE_UNKNOWN = 1;
    CR_OBJMOTIONTYPE_LE_DRIVE = 2;
    CR_OBJMOTIONTYPE_LE_STOPPED = 3;
    CR_OBJMOTIONTYPE_LE_STAND = 4;
    CR_OBJMOTIONTYPE_LE_RESERVED = 5;
    CR_OBJMOTIONTYPE_LE_RESERVED_6 = 6;
    CR_OBJMOTIONTYPE_LE_RESERVED_7 = 7;
  }
  enum CRObjMotionTypeRiType {
    CR_OBJMOTIONTYPE_RI_INT = 0;
    CR_OBJMOTIONTYPE_RI_UNKNOWN = 1;
    CR_OBJMOTIONTYPE_RI_DRIVE = 2;
    CR_OBJMOTIONTYPE_RI_STOPPED = 3;
    CR_OBJMOTIONTYPE_RI_STAND = 4;
    CR_OBJMOTIONTYPE_RI_RESERVED = 5;
    CR_OBJMOTIONTYPE_RI_RESERVED_6 = 6;
    CR_OBJMOTIONTYPE_RI_RESERVED_7 = 7;
  }
  // [NoUnit] [0|7] [initial_value:0]
  optional FCTAStateType fcta_state = 1;
  // [NoUnit] [0|7] [initial_value:0]
  optional FCTBStateType fctb_state = 2;
  // [] [0|1] [initial_value:0]
  optional bool cr_fcta_resp = 3;
  // [] [0|1] [initial_value:0]
  optional bool cr_fctb_resp = 4;
  // [] [0|1] [initial_value:0]
  optional bool cr_bli_sts = 5;
  // [] [0|1] [initial_value:0]
  optional bool fcta_warn = 6;
  // [] [0|1] [initial_value:0]
  optional bool fctb_trig = 7;
  // [] [0|3] [initial_value:0]
  optional FCTABFuncStsType fcta_b_func_sts = 8;
  // [s] [0|6.3] [initial_value:0]
  optional double fcta_b_ttc = 9;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_cr_r_1 = 10;
  // [] [0|1] [initial_value:0]
  optional bool cr_err_sts = 11;
  // [] [0|3] [initial_value:0]
  optional FCTBABALevelType fctb_aba_level = 12;
  // [] [0|1] [initial_value:0]
  optional bool fctb_aba_req = 13;
  // [] [0|1] [initial_value:0]
  optional bool fctb_abp_req = 14;
  // [m/s2] [-16|16] [initial_value:0]
  optional double cr_brkg_req_val = 15;
  // [] [0|1] [initial_value:0]
  optional bool cr_brkg_req = 16;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_cr_r_1 = 17;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_cr_r_2 = 18;
  // [] [0|3] [initial_value:2]
  optional int32 cr_obj_sts_le = 19;
  // [] [0|63] [initial_value:0]
  optional int32 cr_obj_id_le = 20;
  // [m] [-10|92.3] [initial_value:0]
  optional double cr_obj_dist_x_le = 21;
  // [m] [-12.8|12.7] [initial_value:128]
  optional double cr_obj_dist_y_le = 22;
  // [m/s] [-60|42.3] [initial_value:0]
  optional double cr_obj_rel_vel_x_le = 23;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double cr_obj_rel_accel_x_le = 24;
  // [] [0|7] [initial_value:0]
  optional CRObjMotionTypeLeType cr_obj_motion_type_le = 25;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_cr_r_2 = 26;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_cr_r_3 = 27;
  // [] [0|3] [initial_value:2]
  optional int32 cr_obj_sts_ri = 28;
  // [] [0|63] [initial_value:0]
  optional int32 cr_obj_id_ri = 29;
  // [m] [-10|92.3] [initial_value:0]
  optional double cr_obj_dist_x_ri = 30;
  // [m] [-12.8|12.7] [initial_value:128]
  optional double cr_obj_dist_y_ri = 31;
  // [m/s] [-60|42.3] [initial_value:0]
  optional double cr_obj_rel_vel_x_ri = 32;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double cr_obj_rel_accel_x_ri = 33;
  // [] [0|7] [initial_value:0]
  optional CRObjMotionTypeRiType cr_obj_motion_type_ri = 34;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_cr_r_3 = 35;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_cr_r_4 = 36;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double cr_obj_rel_accel_y_ri = 37;
  // [m/s] [-6.4|6.3] [initial_value:64]
  optional double cr_obj_rel_vel_y_ri = 38;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double cr_obj_rel_accel_y_le = 39;
  // [m/s] [-6.4|6.3] [initial_value:64]
  optional double cr_obj_rel_vel_y_le = 40;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_cr_r_4 = 41;
  // [m] [0|11.625] [initial_value:0]
  optional double cr_obj_dist_x_std_le = 42;
  // [m] [0|11.625] [initial_value:0]
  optional double cr_obj_dist_y_std_le = 43;
  // [mps] [0|11.625] [initial_value:0]
  optional double cr_obj_rel_vel_x_std_le = 44;
  // [mps] [0|11.625] [initial_value:0]
  optional double cr_obj_rel_vel_y_std_le = 45;
  // [mps2] [0|11.625] [initial_value:0]
  optional double cr_obj_r_accel_x_std_le = 46;
  // [mps2] [0|11.625] [initial_value:0]
  optional double cr_obj_r_accel_y_std_le = 47;
  // [m] [0|11.625] [initial_value:0]
  optional double cr_obj_dist_x_std_ri = 48;
  // [m] [0|11.625] [initial_value:0]
  optional double cr_obj_dist_y_std_ri = 49;
  // [mps] [0|11.625] [initial_value:0]
  optional double cr_obj_rel_vel_x_std_ri = 50;
  // [mps] [0|11.625] [initial_value:0]
  optional double cr_obj_rel_vel_y_std_ri = 51;
  // [mps2] [0|11.625] [initial_value:0]
  optional double cr_obj_r_accel_x_std_ri = 52;
  // [mps2] [0|11.625] [initial_value:0]
  optional double cr_obj_r_accel_y_std_ri = 53;
}

message CSA1_165 {
// Report Message
  enum FrntWshrSwtStsType {
    FRNTWSHRSWTSTS_OFF = 0;
    FRNTWSHRSWTSTS_WIPER_WASH = 1;
    FRNTWSHRSWTSTS_WASH_IN_AUTO_MODE = 2;
    FRNTWSHRSWTSTS_RESERVED = 3;
  }
  enum FrntWiprSwtStsType {
    FRNTWIPRSWTSTS_WIPER_OFF = 0;
    FRNTWIPRSWTSTS_WIPER_AUTO_OR_INTERMITTENT = 1;
    FRNTWIPRSWTSTS_LOW_SPEED = 2;
    FRNTWIPRSWTSTS_HIGH_SPEED = 3;
    FRNTWIPRSWTSTS_RESERVED = 4;
    FRNTWIPRSWTSTS_ERROR = 7;
  }
  // [] [0|3] [initial_value:0]
  optional FrntWshrSwtStsType frnt_wshr_swt_sts = 1;
  // [] [0|7] [initial_value:0]
  optional FrntWiprSwtStsType frnt_wipr_swt_sts = 2;
  // [] [0|1] [initial_value:0]
  optional bool l_turn_lmp_swt_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool r_turn_lmp_swt_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool frnt_fog_lmp_swt_sts = 5;
  // [] [0|1] [initial_value:0]
  optional bool low_beam_swt_sts = 6;
  // [] [0|1] [initial_value:0]
  optional bool hi_beam_swt_sts = 7;
  // [] [0|1] [initial_value:0]
  optional bool over_veh_beam_swt_sts = 8;
  // [] [0|1] [initial_value:0]
  optional bool auto_lmp_swt_sts = 9;
}

message AEB_FD1_18B {
// Control Message
  enum ADASErrStsType {
    ADAS_ERRSTS_SYSTEM_INITIALIZING = 0;
    ADAS_ERRSTS_NO_ERROR = 1;
    ADAS_ERRSTS_BLINDNESS = 2;
    ADAS_ERRSTS_MISALIGNMENT = 3;
    ADAS_ERRSTS_FAILURE = 4;
    ADAS_ERRSTS_RESERVED = 5;
  }
  enum AEBObjStyVSIType {
    AEB_OBJSTY_VSI_NO_OBJECT = 0;
    AEB_OBJSTY_VSI_CAR = 1;
    AEB_OBJSTY_VSI_PEDESTRIAN = 2;
    AEB_OBJSTY_VSI_CYCLIST = 3;
    AEB_OBJSTY_VSI_UNKNOWN = 4;
    AEB_OBJSTY_VSI_TRUCK = 5;
    AEB_OBJSTY_VSI_RESVERED = 6;
  }
  enum AEBABALevelType {
    AEB_ABA_LEVEL_LEVEL_0 = 0;
    AEB_ABA_LEVEL_LEVEL_1 = 1;
    AEB_ABA_LEVEL_LEVEL_2 = 2;
    AEB_ABA_LEVEL_LEVEL_3 = 3;
  }
  enum AEBAWBLevelType {
    AEB_AWB_LEVEL_NO_LEVEL = 0;
    AEB_AWB_LEVEL_LEVEL_1 = 1;
    AEB_AWB_LEVEL_LEVEL_2 = 2;
    AEB_AWB_LEVEL_LEVEL_3 = 3;
    AEB_AWB_LEVEL_LEVEL_4 = 4;
    AEB_AWB_LEVEL_RESVERED = 5;
  }
  enum AEBObjStyType {
    AEB_OBJSTY_NO_OBJECT = 0;
    AEB_OBJSTY_VEHICLE = 1;
    AEB_OBJSTY_PEDESTRIAN = 2;
    AEB_OBJSTY_CYCLIST = 3;
    AEB_OBJSTY_UNKNOWN = 4;
    AEB_OBJSTY_SPEED_DROP_INCREASE = 5;
  }
  enum AEBBrkReqFuncType {
    AEB_BRKREQFUNC_NO_REQUEST = 0;
    AEB_BRKREQFUNC_AEB_V = 1;
    AEB_BRKREQFUNC_AEB_VRU = 2;
    AEB_BRKREQFUNC_AEB_JA = 3;
    AEB_BRKREQFUNC_RESERVED = 4;
    AEB_BRKREQFUNC_AEB_FCTB = 5;
    AEB_BRKREQFUNC_RESERVED_6 = 6;
  }
  enum BrkgTgtTrqEnaType {
    BRKGTGTTRQENA_INITIAL_VALUE = 0;
    BRKGTGTTRQENA_DISABLE = 1;
    BRKGTGTTRQENA_ENABLE = 2;
  }
  enum BrkgTgtTrqModType {
    BRKGTGTTRQMOD_NO_REQUEST = 0;
    BRKGTGTTRQMOD_ACC = 1;
    BRKGTGTTRQMOD_APA = 2;
  }
  enum BrkgTgtTypType {
    BRKGTGTTYP_INITIAL_VALUE = 0;
    BRKGTGTTYP_COMFORT = 1;
    BRKGTGTTYP_EMERGENCY = 2;
  }
  enum EPBModReqType {
    EPBMODREQ_NO_REQUEST = 0;
    EPBMODREQ_LOCK = 1;
    EPBMODREQ_RELEASE = 2;
  }
  enum EPBModEnaType {
    EPBMODENA_INITIAL_VALUE = 0;
    EPBMODENA_DISABLE = 1;
    EPBMODENA_ENABLE = 2;
  }
  enum BrkgSwtReqType {
    BRKGSWTREQ_INITIAL_VALUE = 0;
    BRKGSWTREQ_ON = 1;
    BRKGSWTREQ_OFF = 2;
  }
  enum BrkgHldReqType {
    BRKGHLDREQ_NO_REQUEST = 0;
    BRKGHLDREQ_AVH_OFF = 1;
    BRKGHLDREQ_AVH_ACTIVE = 2;
    BRKGHLDREQ_AVH_STANDY = 3;
  }
  enum DrvPrsntType {
    DRVPRSNT_INITIAL_VALUE = 0;
    DRVPRSNT_WITH_DRIVER = 1;
    DRVPRSNT_WITHOUT_DRIVER = 2;
  }
  enum DWStateType {
    DW_STATE_INITIAL_VALUE = 0;
    DW_STATE_OFF = 1;
    DW_STATE_STANDBY = 2;
    DW_STATE_ACTIVE = 3;
    DW_STATE_PASSIVE = 4;
    DW_STATE_FAULT = 5;
    DW_STATE_RESERVED = 6;
  }
  enum FCWStateType {
    FCW_STATE_INITIAL_VALUE = 0;
    FCW_STATE_OFF = 1;
    FCW_STATE_STANDBY = 2;
    FCW_STATE_ACTIVE = 3;
    FCW_STATE_PASSIVE = 4;
    FCW_STATE_FAULT = 5;
    FCW_STATE_RESERVED = 6;
  }
  enum AWBStateType {
    AWB_STATE_INITIAL_VALUE = 0;
    AWB_STATE_OFF = 1;
    AWB_STATE_STANDBY = 2;
    AWB_STATE_ACTIVE = 3;
    AWB_STATE_PASSIVE = 4;
    AWB_STATE_FAULT = 5;
    AWB_STATE_RESERVED = 6;
  }
  enum AEBStateType {
    AEB_STATE_INITIAL_VALUE = 0;
    AEB_STATE_OFF = 1;
    AEB_STATE_STANDBY = 2;
    AEB_STATE_ACTIVE = 3;
    AEB_STATE_PASSIVE = 4;
    AEB_STATE_FAULT = 5;
    AEB_STATE_HOLD = 6;
    AEB_STATE_EBA_ACTIVE = 7;
  }
  enum EBAStateType {
    EBA_STATE_INITIAL_VALUE = 0;
    EBA_STATE_OFF = 1;
    EBA_STATE_STANDBY = 2;
    EBA_STATE_ACTIVE = 3;
    EBA_STATE_PASSIVE = 4;
    EBA_STATE_FAULT = 5;
    EBA_STATE_RESERVED = 6;
  }
  // [s] [0|1] [initial_value:250]
  optional double time_to_cllsn = 1;
  // [mps] [0|42] [initial_value:0]
  optional double rel_tgt_obj_vel_x = 2;
  // [] [0|7] [initial_value:0]
  optional ADASErrStsType adas_err_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool aeb_flag_vsi = 4;
  // [] [0|7] [initial_value:0]
  optional AEBObjStyVSIType aeb_obj_sty_vsi = 5;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_aeb1 = 6;
  // [] [0|3] [initial_value:0]
  optional AEBABALevelType aeb_aba_level = 7;
  // [] [0|1] [initial_value:0]
  optional bool aeb_aba_req = 8;
  // [] [0|1] [initial_value:0]
  optional bool eba_tgt_decel_req = 9;
  // [] [0|1] [initial_value:0]
  optional bool aeb_veh_hld_req = 10;
  // [] [0|1] [initial_value:0]
  optional bool aeb_tgt_decel_req = 11;
  // [m/s2] [-16|16] [initial_value:0]
  optional double aeb_tgt_decel_req_value = 12;
  // [] [0|1] [initial_value:0]
  optional bool aeb_awb_req = 13;
  // [] [0|15] [initial_value:0]
  optional AEBAWBLevelType aeb_awb_level = 14;
  // [] [0|7] [initial_value:0]
  optional AEBObjStyType aeb_obj_sty = 15;
  // [] [0|1] [initial_value:0]
  optional bool aeb_abp_req = 16;
  // [] [0|7] [initial_value:0]
  optional AEBBrkReqFuncType aeb_brk_req_func = 17;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_aeb1 = 18;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_aeb6 = 19;
  // [Nm] [0|65534] [initial_value:0]
  optional int32 brkg_tgt_trq = 20;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool brkg_tgt_trq_vld = 21;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgTgtTrqEnaType brkg_tgt_trq_ena = 22;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgTgtTrqModType brkg_tgt_trq_mod = 23;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgTgtTypType brkg_tgt_typ = 24;
  // [NoUnit] [0|3] [initial_value:0]
  optional EPBModReqType epb_mod_req = 25;
  // [NoUnit] [0|3] [initial_value:0]
  optional EPBModEnaType epb_mod_ena = 26;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool epb_mod_req_vld = 27;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgSwtReqType brkg_swt_req = 28;
  // [NoUnit] [0|3] [initial_value:0]
  optional BrkgHldReqType brkg_hld_req = 29;
  // [NoUnit] [0|3] [initial_value:0]
  optional DrvPrsntType drv_prsnt = 30;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_aeb6 = 31;
  // [NoUnit] [0|7] [initial_value:0]
  optional DWStateType dw_state = 32;
  // [NoUnit] [0|7] [initial_value:0]
  optional FCWStateType fcw_state = 33;
  // [NoUnit] [0|7] [initial_value:0]
  optional AWBStateType awb_state = 34;
  // [NoUnit] [0|7] [initial_value:0]
  optional AEBStateType aeb_state = 35;
  // [NoUnit] [0|7] [initial_value:0]
  optional EBAStateType eba_state = 36;
}

message F_PBOX1_19B {
// Report Message
  // [] [0|1] [initial_value:0]
  optional bool f_fog_lmp_sts_f_p_box = 1;
  // [] [0|1] [initial_value:0]
  optional bool hi_beam_sts_f_p_box = 2;
  // [] [0|1] [initial_value:0]
  optional bool hood_sts_f_p_box = 3;
  // [] [0|1] [initial_value:0]
  optional bool low_beam_sts_f_p_box = 4;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool l_head_lmp_fail_sts = 5;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool r_head_lmp_fail_sts = 6;
}

message R_PBOX1_19C {
// Report Message
  enum LTurnLmpFailStsType {
    LTURNLMPFAILSTS_NO_FAILURE = 0;
    LTURNLMPFAILSTS_SHORT_CIRCUIT = 1;
    LTURNLMPFAILSTS_OPEN_CIRCUIT = 2;
    LTURNLMPFAILSTS_RESERVED = 3;
  }
  enum RTurnLmpFailStsType {
    RTURNLMPFAILSTS_NO_FAILURE = 0;
    RTURNLMPFAILSTS_SHORT_CIRCUIT = 1;
    RTURNLMPFAILSTS_OPEN_CIRCUIT = 2;
    RTURNLMPFAILSTS_RESERVED = 3;
  }
  // [] [0|1] [initial_value:0]
  optional bool l_turn_lmp_sts_r_p_box = 1;
  // [] [0|1] [initial_value:0]
  optional bool r_turn_lmp_sts_r_p_box = 2;
  // [] [0|1] [initial_value:0]
  optional bool hi_posn_brk_lmp_fail_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool l_brk_lmp_fail_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool r_fog_lmp_fail_sts_r_p_box = 5;
  // [] [0|1] [initial_value:0]
  optional bool r_fog_lmp_sts_r_p_box = 6;
  // [] [0|1] [initial_value:0]
  optional bool r_brk_lmp_fail_sts = 7;
  // [] [0|1] [initial_value:0]
  optional bool posn_lmp_outp_sts_r_p_box = 8;
  // [NoUnit] [0|3] [initial_value:0]
  optional LTurnLmpFailStsType l_turn_lmp_fail_sts = 9;
  // [NoUnit] [0|3] [initial_value:0]
  optional RTurnLmpFailStsType r_turn_lmp_fail_sts = 10;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_r_p_box1 = 11;
}

message IFC_FD5_19F {
// Control Message
  enum IFCLine01TypeType {
    IFC_LINE01_TYPE_UNDECIDED = 0;
    IFC_LINE01_TYPE_SOLID = 1;
    IFC_LINE01_TYPE_DASHED = 2;
    IFC_LINE01_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE01_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE01_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE01_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE01_TYPE_BOTTS = 7;
    IFC_LINE01_TYPE_DECELERATION = 8;
    IFC_LINE01_TYPE_HOV_LANE = 9;
    IFC_LINE01_TYPE_ROAD_EDGE = 10;
    IFC_LINE01_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE01_TYPE_CURB = 12;
    IFC_LINE01_TYPE_CONES_POLES = 13;
  }
  enum IFCLine02TypeType {
    IFC_LINE02_TYPE_UNDECIDED = 0;
    IFC_LINE02_TYPE_SOLID = 1;
    IFC_LINE02_TYPE_DASHED = 2;
    IFC_LINE02_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE02_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE02_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE02_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE02_TYPE_BOTTS = 7;
    IFC_LINE02_TYPE_DECELERATION = 8;
    IFC_LINE02_TYPE_HOV_LANE = 9;
    IFC_LINE02_TYPE_ROAD_EDGE = 10;
    IFC_LINE02_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE02_TYPE_CURB = 12;
    IFC_LINE02_TYPE_CONES_POLES = 13;
  }
  enum IFCLine03TypeType {
    IFC_LINE03_TYPE_UNDECIDED = 0;
    IFC_LINE03_TYPE_SOLID = 1;
    IFC_LINE03_TYPE_DASHED = 2;
    IFC_LINE03_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE03_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE03_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE03_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE03_TYPE_BOTTS = 7;
    IFC_LINE03_TYPE_DECELERATION = 8;
    IFC_LINE03_TYPE_HOV_LANE = 9;
    IFC_LINE03_TYPE_ROAD_EDGE = 10;
    IFC_LINE03_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE03_TYPE_CURB = 12;
    IFC_LINE03_TYPE_CONES_POLES = 13;
  }
  enum IFCLine04TypeType {
    IFC_LINE04_TYPE_UNDECIDED = 0;
    IFC_LINE04_TYPE_SOLID = 1;
    IFC_LINE04_TYPE_DASHED = 2;
    IFC_LINE04_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE04_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE04_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE04_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE04_TYPE_BOTTS = 7;
    IFC_LINE04_TYPE_DECELERATION = 8;
    IFC_LINE04_TYPE_HOV_LANE = 9;
    IFC_LINE04_TYPE_ROAD_EDGE = 10;
    IFC_LINE04_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE04_TYPE_CURB = 12;
    IFC_LINE04_TYPE_CONES_POLES = 13;
  }
  enum IFCRoadedge01TypeType {
    IFC_ROADEDGE01_TYPE_UNDECIDED = 0;
    IFC_ROADEDGE01_TYPE_SOLID = 1;
    IFC_ROADEDGE01_TYPE_DASHED = 2;
    IFC_ROADEDGE01_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_ROADEDGE01_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_ROADEDGE01_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_ROADEDGE01_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_ROADEDGE01_TYPE_BOTTS = 7;
    IFC_ROADEDGE01_TYPE_DECELERATION = 8;
    IFC_ROADEDGE01_TYPE_HOV_LANE = 9;
    IFC_ROADEDGE01_TYPE_ROAD_EDGE = 10;
    IFC_ROADEDGE01_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_ROADEDGE01_TYPE_CURB = 12;
    IFC_ROADEDGE01_TYPE_CONES_POLES = 13;
  }
  enum IFCRoadedge02TypeType {
    IFC_ROADEDGE02_TYPE_UNDECIDED = 0;
    IFC_ROADEDGE02_TYPE_SOLID = 1;
    IFC_ROADEDGE02_TYPE_DASHED = 2;
    IFC_ROADEDGE02_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_ROADEDGE02_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_ROADEDGE02_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_ROADEDGE02_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_ROADEDGE02_TYPE_BOTTS = 7;
    IFC_ROADEDGE02_TYPE_DECELERATION = 8;
    IFC_ROADEDGE02_TYPE_HOV_LANE = 9;
    IFC_ROADEDGE02_TYPE_ROAD_EDGE = 10;
    IFC_ROADEDGE02_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_ROADEDGE02_TYPE_CURB = 12;
    IFC_ROADEDGE02_TYPE_CONES_POLES = 13;
  }
  enum ACCRiTgtObjBarDisp03Type {
    ACC_RITGTOBJBARDISP_03_NO_DISPLAY = 0;
    ACC_RITGTOBJBARDISP_03_DISTANCE_1_ONE_BAR = 1;
    ACC_RITGTOBJBARDISP_03_DISTANCE_2_TWO_BARS = 2;
    ACC_RITGTOBJBARDISP_03_DISTANCE_3_THREE_BARS = 3;
    ACC_RITGTOBJBARDISP_03_DISTANCE_4_FOUR_BARS = 4;
    ACC_RITGTOBJBARDISP_03_RESVERED = 5;
  }
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_line01_dy = 1;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line01_curv = 2;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line01_curv_change = 3;
  // [] [0|15] [initial_value:0]
  optional IFCLine01TypeType ifc_line01_type = 4;
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_line02_dy = 5;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line02_curv = 6;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line02_curv_change = 7;
  // [] [0|15] [initial_value:0]
  optional IFCLine02TypeType ifc_line02_type = 8;
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_line03_dy = 9;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line03_curv = 10;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line03_curv_change = 11;
  // [] [0|15] [initial_value:0]
  optional IFCLine03TypeType ifc_line03_type = 12;
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_line04_dy = 13;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line04_curv = 14;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line04_curv_change = 15;
  // [] [0|15] [initial_value:0]
  optional IFCLine04TypeType ifc_line04_type = 16;
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_roadedge01_dy = 17;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_roadedge01_curv = 18;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_roadedge01_curv_change = 19;
  // [] [0|15] [initial_value:0]
  optional IFCRoadedge01TypeType ifc_roadedge01_type = 20;
  // [m] [-30|30] [initial_value:2825]
  optional double ifc_roadedge02_dy = 21;
  // [1/m] [-0.25|0.25] [initial_value:15625]
  optional double ifc_roadedge02_curv = 22;
  // [1/(m*m)] [-0.024|0.024] [initial_value:480000]
  optional double ifc_roadedge02_curv_change = 23;
  // [] [0|15] [initial_value:0]
  optional IFCRoadedge02TypeType ifc_roadedge02_type = 24;
  // [PerMtrSq] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line_special02_curv_change = 25;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCRiTgtObjBarDisp03Type acc_ri_tgt_obj_bar_disp_03 = 26;
  // [m] [-30|30.01] [initial_value:2825]
  optional double target_path_c0 = 27;
  // [PerMtr] [-0.25|0.25] [initial_value:15625]
  optional double target_path_c2 = 28;
}

message GW_OTA_1D9 {
// Report Message
  enum OTApowertrainmodeType {
    OTAPOWERTRAINMODE_NO_ACTION = 0;
    OTAPOWERTRAINMODE_POWERTRAIN_ON = 1;
    OTAPOWERTRAINMODE_POWERTRAIN_OFF = 2;
    OTAPOWERTRAINMODE_RESERVED = 3;
  }
  // description \C9\FD\BC\B6\B9\FD\B3\CC\D6жԸ\DFѹON/OFF\B5\C4\D0\E8\C7\F3\A3\AC\C7л\BB\C7\EB\C7\F3 [NoUnit] [0|3] [initial_value:0]
  optional OTApowertrainmodeType ot_apowertrainmode = 1;
}

message HUT_FD4_1DA {
// Report Message
  enum UserDefPrkMenuSelCmdType {
    USERDEFPRKMENUSELCMD_NO_REQUEST = 0;
    USERDEFPRKMENUSELCMD_PARALLEL_SLOT_SELECTED = 1;
    USERDEFPRKMENUSELCMD_VERTICAL_SLOT_SELECTED = 2;
    USERDEFPRKMENUSELCMD_RESERVED = 3;
    USERDEFPRKMENUSELCMD_RESERVED_4 = 4;
    USERDEFPRKMENUSELCMD_CLOCK_WISE_SELECTED = 5;
    USERDEFPRKMENUSELCMD_ANTICLOCK_WISE_SELECTED = 6;
    USERDEFPRKMENUSELCMD_RESERVED_7 = 7;
  }
  enum HUTHAVPAPAPermitType {
    HUT_HAVP_APA_PERMIT_IDLE = 0;
    HUT_HAVP_APA_PERMIT_NO = 1;
    HUT_HAVP_APA_PERMIT_YES = 2;
    HUT_HAVP_APA_PERMIT_RESERVED = 3;
  }
  enum PrkModReqType {
    PRKMODREQ_NO_ACTION = 0;
    PRKMODREQ_HEAD_PARKING_IN = 1;
    PRKMODREQ_TAIL_PARKING_IN = 2;
    PRKMODREQ_RESERVED = 3;
  }
  enum TJAASCruiseType {
    TJA_ASCRUISE_NO_ACTION = 0;
    TJA_ASCRUISE_OFF = 1;
    TJA_ASCRUISE_ON = 2;
    TJA_ASCRUISE_RESERVED = 3;
  }
  enum HAVPUseModType {
    HAVP_USEMOD_NO_ACTION = 0;
    HAVP_USEMOD_USE_ON_CAR = 1;
    HAVP_USEMOD_USE_ON_MOBILE = 2;
    HAVP_USEMOD_RESERVED = 3;
  }
  // [] [0|15] [initial_value:0]
  optional int32 slot_num_vr = 1;
  // description \D7\D4ѡ\B3\B5λ\B9\A6\C4\DC [] [0|1] [initial_value:0]
  optional bool user_defined_park_cmd = 2;
  // description \D7\D4ѡ\B3\B5λ\B9\A6\C4\DC [] [0|1] [initial_value:0]
  optional UserDefPrkMenuSelCmdType user_def_prk_menu_sel_cmd = 3;
  // [] [0|1] [initial_value:0]
  optional bool hut_havp_comfirm_btn = 4;
  // [] [0|1] [initial_value:0]
  optional bool hut_havp_self_par_lot_ending = 5;
  // [] [0|1] [initial_value:0]
  optional bool hut_havp_set_starting = 6;
  // [] [0|1] [initial_value:0]
  optional bool hut_havp_set_path_ending = 7;
  // [] [0|3] [initial_value:0]
  optional HUTHAVPAPAPermitType hut_havp_apa_permit = 8;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool auto_prk_out_swt_req = 9;
  // [NoUnit] [0|3] [initial_value:0]
  optional PrkModReqType prk_mod_req = 10;
  // description / [NoUnit] [0|1] [initial_value:0]
  optional bool back_req_rpa = 11;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool cam_folw_swt_req = 12;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool rcta_disp_swt_req = 13;
  // description A [NoUnit] [0|3] [initial_value:0]
  optional TJAASCruiseType tja_as_cruise = 14;
  // [NoUnit] [1|1023] [initial_value:0]
  optional int32 prkg_slot_id = 15;
  // [NoUnit] [0|3] [initial_value:0]
  optional HAVPUseModType havp_use_mod = 16;
}

message HUT30_1E9 {
// Report Message
  // [] [0|1] [initial_value:0]
  optional bool back_req_avm = 1;
}

message BLE2_1EA {
// Report Message
  enum RmtPrkRadShieldSetType {
    RMTPRKRADSHIELDSET_NO_ACTION = 0;
    RMTPRKRADSHIELDSET_OFF = 1;
    RMTPRKRADSHIELDSET_SHIELDED_BILATERAL_RADAR = 2;
    RMTPRKRADSHIELDSET_SHIELD_ALL_RADARS = 3;
  }
  // [NoUnit] [0|3] [initial_value:0]
  optional RmtPrkRadShieldSetType rmt_prk_rad_shield_set = 1;
}

message HUT33_1ED {
// Report Message
  enum PrkInDirChoiceType {
    PRKINDIRCHOICE_NONE = 0;
    PRKINDIRCHOICE_TAIL_IN = 1;
    PRKINDIRCHOICE_HEAD_IN = 2;
    PRKINDIRCHOICE_RESERVED = 3;
  }
  enum SelPrkOutDirReqType {
    SELPRKOUTDIRREQ_NO_SELECTION = 0;
    SELPRKOUTDIRREQ_PARK_OUT_FRONT_VERTICAL_HEAD_OUT = 1;
    SELPRKOUTDIRREQ_PARK_OUT_REAR_VERTICAL_TAIL_OUT = 2;
    SELPRKOUTDIRREQ_PARK_OUT_LEFT_PARALLEL_LEFT_OUT = 3;
    SELPRKOUTDIRREQ_PARK_OUT_RIGHT_PARALLEL_RIGHT_OUT = 4;
    SELPRKOUTDIRREQ_PARK_OUT_LEFT_VERTICAL_LEFT_OUT = 5;
    SELPRKOUTDIRREQ_PARK_OUT_RIGHT_VERTICAL_RIGHT_OUT = 6;
    SELPRKOUTDIRREQ_RESERVED = 7;
  }
  enum SelPrkgFctnCmdType {
    SELPRKGFCTNCMD_NO_SELECTION = 0;
    SELPRKGFCTNCMD_SELECT_APA_PARKING = 1;
    SELPRKGFCTNCMD_SELECT_P2P_PARKING = 2;
    SELPRKGFCTNCMD_SELECT_EXPLORE_FORWARD = 3;
    SELPRKGFCTNCMD_SELECT_DRIVE_BACK = 4;
    SELPRKGFCTNCMD_SELECT_HAVP_PARKING = 5;
    SELPRKGFCTNCMD_SELECT_PAVP_PARKING = 6;
    SELPRKGFCTNCMD_RESERVED = 7;
  }
  // [] [0|3] [initial_value:0]
  optional PrkInDirChoiceType prk_in_dir_choice = 1;
  // [] [0|1] [initial_value:0]
  optional bool path_lrng_finsh_cmd = 2;
  // [] [0|1] [initial_value:0]
  optional bool path_lrng_start_cmd = 3;
  // [] [0|7] [initial_value:0]
  optional SelPrkOutDirReqType sel_prk_out_dir_req = 4;
  // [] [0|1] [initial_value:0]
  optional bool park_mdl_cmd = 5;
  // [] [0|1] [initial_value:0]
  optional bool back_req_aps = 6;
  // [] [0|1] [initial_value:0]
  optional bool start_prkg_path2_cmd = 7;
  // [] [0|1] [initial_value:0]
  optional bool delete_path2_cmd = 8;
  // [] [0|1] [initial_value:0]
  optional bool start_prkg_path1_cmd = 9;
  // [] [0|1] [initial_value:0]
  optional bool delete_path1_cmd = 10;
  // [] [0|7] [initial_value:0]
  optional SelPrkgFctnCmdType sel_prkg_fctn_cmd = 11;
  // [] [0|1] [initial_value:0]
  optional bool p2_p_prkg_direct_swt_cmd = 12;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool tra_ovl_swt_cmd = 13;
}

message HUT15_1EE {
// Report Message
  enum APSSwtReqVRType {
    APSSWTREQ_VR_NO_ACTION = 0;
    APSSWTREQ_VR_OFF = 1;
    APSSWTREQ_VR_ON = 2;
    APSSWTREQ_VR_RESERVED = 3;
  }
  enum APSPrkgTypSelnType {
    APSPRKGTYPSELN_NO_SELECT = 0;
    APSPRKGTYPSELN_SELECT_PARALLEL_PARKING = 1;
    APSPRKGTYPSELN_SELECT_VERTICAL_PARKING = 2;
    APSPRKGTYPSELN_RESERVED = 3;
  }
  enum PASExitSpdSwtType {
    PASEXITSPDSWT_NO_COMMAND = 0;
    PASEXITSPDSWT_15KM_H = 1;
    PASEXITSPDSWT_30KM_H = 2;
    PASEXITSPDSWT_RESERVED = 3;
  }
  // [] [0|1] [initial_value:0]
  optional bool sdw_swt_set = 1;
  // [] [0|3] [initial_value:0]
  optional APSSwtReqVRType aps_swt_req_vr = 2;
  // [] [0|3] [initial_value:0]
  optional APSPrkgTypSelnType aps_prkg_typ_seln = 3;
  // [] [0|3] [initial_value:0]
  optional PASExitSpdSwtType pas_exit_spd_swt = 4;
}

message HUT_FD2_203 {
// Report Message
  enum HUTODObj0typeType {
    HUT_OD_OBJ0_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ0_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ0_TYPE_VEHICLE = 2;
    HUT_OD_OBJ0_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ0_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ0_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj1typeType {
    HUT_OD_OBJ1_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ1_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ1_TYPE_VEHICLE = 2;
    HUT_OD_OBJ1_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ1_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ1_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj2typeType {
    HUT_OD_OBJ2_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ2_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ2_TYPE_VEHICLE = 2;
    HUT_OD_OBJ2_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ2_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ2_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj3typeType {
    HUT_OD_OBJ3_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ3_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ3_TYPE_VEHICLE = 2;
    HUT_OD_OBJ3_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ3_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ3_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj4typeType {
    HUT_OD_OBJ4_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ4_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ4_TYPE_VEHICLE = 2;
    HUT_OD_OBJ4_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ4_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ4_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj5typeType {
    HUT_OD_OBJ5_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ5_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ5_TYPE_VEHICLE = 2;
    HUT_OD_OBJ5_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ5_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ5_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODObj6typeType {
    HUT_OD_OBJ6_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ6_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ6_TYPE_VEHICLE = 2;
    HUT_OD_OBJ6_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ6_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ6_TYPE_INVALID_VALUES = 5;
  }
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj0_id = 1;
  // [] [0|7] [initial_value:0]
  optional HUTODObj0typeType hut_od_obj0_type = 2;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj0_confidence = 3;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj0_width = 4;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj0_ground_pos_x = 5;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj0_ground_pos_y = 6;
  // [] [0|9] [initial_value:0]
  optional int32 htu_od_obj1_id = 7;
  // [] [0|7] [initial_value:0]
  optional HUTODObj1typeType hut_od_obj1_type = 8;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj1_confidence = 9;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj1_width = 10;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj1_ground_pos_x = 11;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj1_ground_pos_y = 12;
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj2_id = 13;
  // [] [0|7] [initial_value:0]
  optional HUTODObj2typeType hut_od_obj2_type = 14;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj2_confidence = 15;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj2_width = 16;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj2_ground_pos_x = 17;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj2_ground_pos_y = 18;
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj3_id = 19;
  // [] [0|7] [initial_value:0]
  optional HUTODObj3typeType hut_od_obj3_type = 20;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj3_confidence = 21;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj3_width = 22;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj3_ground_pos_x = 23;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj3_ground_pos_y = 24;
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj4_id = 25;
  // [] [0|7] [initial_value:0]
  optional HUTODObj4typeType hut_od_obj4_type = 26;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj4_confidence = 27;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj4_width = 28;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj4_ground_pos_x = 29;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj4_ground_pos_y = 30;
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj5_id = 31;
  // [] [0|7] [initial_value:0]
  optional HUTODObj5typeType hut_od_obj5_type = 32;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj5_confidence = 33;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj5_width = 34;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj5_ground_pos_x = 35;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj5_ground_pos_y = 36;
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj6_id = 37;
  // [] [0|7] [initial_value:0]
  optional HUTODObj6typeType hut_od_obj6_type = 38;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj6_confidence = 39;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj6_width = 40;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj6_ground_pos_x = 41;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj6_ground_pos_y = 42;
}

message DCT5_221 {
// Report Message
  enum TARGETGEARType {
    TARGET_GEAR_TARGET_GEAR_N_OR_P = 0;
    TARGET_GEAR_TARGET_GEAR_1 = 1;
    TARGET_GEAR_TARGET_GEAR_2 = 2;
    TARGET_GEAR_TARGET_GEAR_3 = 3;
    TARGET_GEAR_TARGET_GEAR_4 = 4;
    TARGET_GEAR_TARGET_GEAR_5 = 5;
    TARGET_GEAR_TARGET_GEAR_6 = 6;
    TARGET_GEAR_TARGET_GEAR_7 = 7;
    TARGET_GEAR_TARGET_GEAR_R = 8;
    TARGET_GEAR_TARGET_GEAR_8 = 9;
    TARGET_GEAR_TARGET_GEAR_9 = 10;
    TARGET_GEAR_RESERVED = 11;
  }
  enum CURRENTGEARType {
    CURRENT_GEAR_CURRENT_GEAR_N_OR_P = 0;
    CURRENT_GEAR_CURRENT_GEAR_1 = 1;
    CURRENT_GEAR_CURRENT_GEAR_2 = 2;
    CURRENT_GEAR_CURRENT_GEAR_3 = 3;
    CURRENT_GEAR_CURRENT_GEAR_4 = 4;
    CURRENT_GEAR_CURRENT_GEAR_5 = 5;
    CURRENT_GEAR_CURRENT_GEAR_6 = 6;
    CURRENT_GEAR_CURRENT_GEAR_7 = 7;
    CURRENT_GEAR_CURRENT_GEAR_R = 8;
    CURRENT_GEAR_CURRENT_GEAR_8 = 9;
    CURRENT_GEAR_CURRENT_GEAR_9 = 10;
    CURRENT_GEAR_RESERVED = 11;
  }
  enum TGSLEVERType {
    TGS_LEVER_SELECT_LEVER_IN_POSITION_P = 0;
    TGS_LEVER_RESERVED = 1;
    TGS_LEVER_RESERVED_2 = 2;
    TGS_LEVER_RESERVED_3 = 3;
    TGS_LEVER_RESERVED_4 = 4;
    TGS_LEVER_SELECT_LEVER_IN_POSITION_D = 5;
    TGS_LEVER_SELECT_LEVER_IN_POSITION_N = 6;
    TGS_LEVER_SELECT_LEVER_IN_POSITION_R = 7;
    TGS_LEVER_SELECT_LEVER_IN_POSITION_M = 8;
    TGS_LEVER_RESERVED_9 = 9;
    TGS_LEVER_INTERMEDIATE_POSITION = 14;
    TGS_LEVER_INVALID_VALUES = 15;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_dct5 = 1;
  // [] [0|1] [initial_value:0]
  optional bool current_gear_valid = 2;
  // [] [0|1] [initial_value:0]
  optional bool shift_in_progress = 3;
  // [] [0|1] [initial_value:0]
  optional bool target_gear_valid = 4;
  // [] [0|15] [initial_value:0]
  optional TARGETGEARType target_gear = 5;
  // [] [0|1] [initial_value:0]
  optional bool shift_in_prg_valid = 6;
  // [] [0|15] [initial_value:0]
  optional CURRENTGEARType current_gear = 7;
  // [] [0|15] [initial_value:14]
  optional TGSLEVERType tgs_lever = 8;
  // [] [0|1] [initial_value:0]
  optional bool tcu_aps_available = 9;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool tgs_lever_valid = 10;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_dct5 = 11;
}

message IFC_FD6_222 {
// Control Message
  // [m] [0|200] [initial_value:0]
  optional double ifc_line01_dx_start = 1;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line01_dx_end = 2;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_line01_heading_angle = 3;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_line02_heading_angle = 4;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line02_dx_start = 5;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line02_dx_end = 6;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line03_dx_start = 7;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line03_dx_end = 8;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_line03_heading_angle = 9;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line04_dx_start = 10;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_line04_heading_angle = 11;
  // [m] [0|200] [initial_value:0]
  optional double ifc_line04_dx_end = 12;
  // [m] [0|200] [initial_value:0]
  optional double ifc_roadedge01_dx_start = 13;
  // [m] [0|200] [initial_value:0]
  optional double ifc_roadedge01_dx_end = 14;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_roadedge01_heading_angle = 15;
  // [m] [0|200] [initial_value:0]
  optional double ifc_roadedge02_dx_start = 16;
  // [m] [0|200] [initial_value:0]
  optional double ifc_roadedge02_dx_end = 17;
  // [rad] [-2|2] [initial_value:201]
  optional double ifc_roadedge02_heading_angle = 18;
  // [m] [0|200.01] [initial_value:0]
  optional double ifc_line_special02_dx_start = 19;
  // [m] [0|200.01] [initial_value:0]
  optional double ifc_line_special02_dx_end = 20;
  // [m] [0|250] [initial_value:0]
  optional double target_position_dx = 21;
}

message AEB_FD2_227 {
// Control Message
  enum FCWSnvtyType {
    FCW_SNVTY_NORMAL = 0;
    FCW_SNVTY_HIGH_SENSITIVITY = 1;
    FCW_SNVTY_LOW_SENSITIVITY = 2;
    FCW_SNVTY_RESERVED = 3;
  }
  enum FCWAEBVehFctStsType {
    FCW_AEB_VEHFCTSTS_NO_DISPLAY = 0;
    FCW_AEB_VEHFCTSTS_ERROR = 1;
    FCW_AEB_VEHFCTSTS_FCW_AEB_V_TEMPORARILY_UNAVAILABLE = 2;
    FCW_AEB_VEHFCTSTS_FCW_AEB_V_PERFORMANCE_DEGRADATION = 3;
  }
  enum FCWAEBPedFctStsType {
    FCW_AEB_PEDFCTSTS_NO_DISPLAY = 0;
    FCW_AEB_PEDFCTSTS_ERROR = 1;
    FCW_AEB_PEDFCTSTS_FCW_AEB_P_TEMPORARILY_UNAVAILABLE = 2;
    FCW_AEB_PEDFCTSTS_FCW_AEB_P_PERFORMANCE_DEGRADATION = 3;
  }
  enum FCWWarnType {
    FCW_WARN_NO_WARNING = 0;
    FCW_WARN_LATENT_WARNING = 1;
    FCW_WARN_WARNING_LEVEL_1 = 2;
    FCW_WARN_WARNING_LEVEL_2 = 3;
  }
  enum AEBJAFuncStsType {
    AEB_JAFUNCSTS_NO_DISPLAY = 0;
    AEB_JAFUNCSTS_ERROR = 1;
    AEB_JAFUNCSTS_FCW_AEB_JA_TEMPORARILY_UNAVAILABLE = 2;
    AEB_JAFUNCSTS_FCW_AEB_JA_PERFORMANCE_DEGRADATION = 3;
  }
  enum AEBJAWarnType {
    AEB_JA_WARN_NO_WARNING = 0;
    AEB_JA_WARN_WARNING_FOR_VEHICLE = 1;
    AEB_JA_WARN_WARNING_FOR_PEDESTRIAN = 2;
    AEB_JA_WARN_REVERSE = 3;
  }
  enum AEBJABrkTrigType {
    AEB_JABRKTRIG_NOT_TIRGGERED = 0;
    AEB_JABRKTRIG_AEB_JA_FUNCTION_TRIGGERED_FOR_VEHICLE = 1;
    AEB_JABRKTRIG_AEB_JA_FUNCTION_TRIGGERED_FOR_PEDESTRIAN = 2;
    AEB_JABRKTRIG_REVERSE = 3;
  }
  enum ACCPotentialTgt02DetnType {
    ACC_POTENTIALTGT_02_DETN_NOT_DECTECTED = 0;
    ACC_POTENTIALTGT_02_DETN_DECTECTED = 1;
    ACC_POTENTIALTGT_02_DETN_RESERVED = 2;
    ACC_POTENTIALTGT_02_DETN_INVALID = 3;
  }
  enum ACCPotentialTgt02TypType {
    ACC_POTENTIALTGT_02_TYP_UNKOWN = 0;
    ACC_POTENTIALTGT_02_TYP_SEDAN = 1;
    ACC_POTENTIALTGT_02_TYP_TRUCK = 2;
    ACC_POTENTIALTGT_02_TYP_MOTORCYCLE = 3;
    ACC_POTENTIALTGT_02_TYP_PEDESTRIAN = 4;
    ACC_POTENTIALTGT_02_TYP_BICYCLE = 5;
    ACC_POTENTIALTGT_02_TYP_TRAFFIC_CONE = 6;
    ACC_POTENTIALTGT_02_TYP_OBSTACLE = 7;
  }
  enum ACCLeTargrt03DetnType {
    ACC_LETARGRT_03_DETN_NOT_DECTECTED = 0;
    ACC_LETARGRT_03_DETN_DECTECTED = 1;
    ACC_LETARGRT_03_DETN_RESERVED = 2;
    ACC_LETARGRT_03_DETN_INVALID = 3;
  }
  enum ACCLeTgt03TypType {
    ACC_LETGT_03_TYP_UNKOWN = 0;
    ACC_LETGT_03_TYP_SEDAN = 1;
    ACC_LETGT_03_TYP_TRUCK = 2;
    ACC_LETGT_03_TYP_MOTORCYCLE = 3;
    ACC_LETGT_03_TYP_PEDESTRIAN = 4;
    ACC_LETGT_03_TYP_BICYCLE = 5;
    ACC_LETGT_03_TYP_TRAFFIC_CONE = 6;
    ACC_LETGT_03_TYP_OBSTACLE = 7;
  }
  enum ACCRiTargrt03DetnType {
    ACC_RITARGRT_03_DETN_NOT_DECTECTED = 0;
    ACC_RITARGRT_03_DETN_DECTECTED = 1;
    ACC_RITARGRT_03_DETN_RESERVED = 2;
    ACC_RITARGRT_03_DETN_INVALID = 3;
  }
  enum ACCRiTgt03TypType {
    ACC_RITGT_03_TYP_UNKOWN = 0;
    ACC_RITGT_03_TYP_SEDAN = 1;
    ACC_RITGT_03_TYP_TRUCK = 2;
    ACC_RITGT_03_TYP_MOTORCYCLE = 3;
    ACC_RITGT_03_TYP_PEDESTRIAN = 4;
    ACC_RITGT_03_TYP_BICYCLE = 5;
    ACC_RITGT_03_TYP_TRAFFIC_CONE = 6;
    ACC_RITGT_03_TYP_OBSTACLE = 7;
  }
  // [] [0|1] [initial_value:1]
  optional bool aeb_ped_resp = 1;
  // [] [0|1] [initial_value:1]
  optional bool aeb_veh_resp = 2;
  // [] [0|3] [initial_value:0]
  optional FCWSnvtyType fcw_snvty = 3;
  // [] [0|1] [initial_value:1]
  optional bool aeb_ja_resp = 4;
  // description / [] [0|1] [initial_value:0]
  optional bool intellgntcurve_resp = 5;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_aeb2 = 6;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_aeb2 = 7;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_aeb3 = 8;
  // [] [0|1] [initial_value:0]
  optional bool aeb_veh_trig = 9;
  // [] [0|1] [initial_value:0]
  optional bool aeb_ped_trig = 10;
  // [] [0|3] [initial_value:0]
  optional FCWAEBVehFctStsType fcw_aeb_veh_fct_sts = 11;
  // [] [0|3] [initial_value:0]
  optional FCWAEBPedFctStsType fcw_aeb_ped_fct_sts = 12;
  // [] [0|3] [initial_value:0]
  optional FCWWarnType fcw_warn = 13;
  // [] [0|3] [initial_value:0]
  optional AEBJAFuncStsType aeb_ja_func_sts = 14;
  // [] [0|3] [initial_value:0]
  optional AEBJAWarnType aeb_ja_warn = 15;
  // [] [0|3] [initial_value:0]
  optional AEBJABrkTrigType aeb_ja_brk_trig = 16;
  // [m] [0|10] [initial_value:0]
  optional double le_road_mark_length = 17;
  // [m] [0|4] [initial_value:0]
  optional double le_road_mark_width = 18;
  // [m] [0|10] [initial_value:0]
  optional double ri_road_mark_length = 19;
  // [m] [0|4] [initial_value:0]
  optional double ri_road_mark_width = 20;
  // [m] [0|10] [initial_value:0]
  optional double road_mark_length = 21;
  // [m] [0|4] [initial_value:0]
  optional double road_mark_width = 22;
  // [m] [-30|30.01] [initial_value:2825]
  optional double ifc_line_special02_dy = 23;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_aeb3 = 24;
  // [m] [-8|8] [initial_value:0]
  optional double acc_potential_tgt_02_dy = 25;
  // [m] [0|150] [initial_value:0]
  optional double acc_potential_tgt_02_dx = 26;
  // [] [0|3] [initial_value:0]
  optional ACCPotentialTgt02DetnType acc_potential_tgt_02_detn = 27;
  // [] [0|7] [initial_value:0]
  optional ACCPotentialTgt02TypType acc_potential_tgt_02_typ = 28;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_potential_tgt_02_heading = 29;
  // [m] [-8|8] [initial_value:0]
  optional double acc_le_tgt_03_dy = 30;
  // [m] [-50|150] [initial_value:0]
  optional double acc_le_tgt_03_dx = 31;
  // [] [0|3] [initial_value:0]
  optional ACCLeTargrt03DetnType acc_le_targrt_03_detn = 32;
  // [] [0|7] [initial_value:0]
  optional ACCLeTgt03TypType acc_le_tgt_03_typ = 33;
  // description \D0\C2\D4\F6\D0ź\C5 [] [0|1] [initial_value:0]
  optional bool acc_potential_tgt_02_fusion = 34;
  // description \D0\C2\D4\F6\D0ź\C5 [] [0|1] [initial_value:0]
  optional bool acc_le_tgt_03_fusion = 35;
  // description \D0\C2\D4\F6\D0ź\C5 [] [0|1] [initial_value:0]
  optional bool acc_ri_tgt_03_fusion = 36;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_le_tgt_03_heading = 37;
  // [m] [-8|7] [initial_value:0]
  optional double acc_ri_tgt_03_dy = 38;
  // [m] [-50|150] [initial_value:0]
  optional double acc_ri_tgt_03_dx = 39;
  // [] [0|3] [initial_value:0]
  optional ACCRiTargrt03DetnType acc_ri_targrt_03_detn = 40;
  // [] [0|7] [initial_value:0]
  optional ACCRiTgt03TypType acc_ri_tgt_03_typ = 41;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_ri_tgt_03_heading = 42;
  // [PerMtrSq] [-0.024|0.024] [initial_value:480000]
  optional double target_path_c3 = 43;
  // [m] [-8|8] [initial_value:0]
  optional double feat_pt_dy = 44;
  // [m] [-30|30.01] [initial_value:2825]
  optional double target_position_dy = 45;
  // [m] [0|150] [initial_value:0]
  optional double feat_pt_dx = 46;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool unp_swt_resp = 47;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_aeb_fd2 = 48;
}

message DCT7_235 {
// Report Message
  enum DrvFaiMsgType {
    DRVFAIMSG_NO_MESSAGE = 0;
    DRVFAIMSG_TRANSMISSION_MALFUNCTION = 1;
    DRVFAIMSG_TRANSMISSION_P_ENGAGEMENT_FAULT = 2;
    DRVFAIMSG_TRANSMISSION_P_DISENGAGEMENT_FAULT = 3;
    DRVFAIMSG_AUTHENTICATION_FAILURE = 4;
    DRVFAIMSG_E_SHIFT_FAULTY = 5;
    DRVFAIMSG_PLEASE_PRESS_THE_BRAKE_PEDAL_UNTILL_THE_P_GEAR_REFERENCE_IS_COMPLETED = 6;
    DRVFAIMSG_REFERENCE_IS_NOT_COMPLETED_PLEASE_SHIFT_GEARS_AFTER_REFERENCE_IS_COMPLETED = 7;
    DRVFAIMSG_P_GEAR_REFERENCE_COMPLETED_PLEASE_RELEASED_THE_BRAKE_PEDAL = 8;
    DRVFAIMSG_RESERVED = 9;
    DRVFAIMSG_TRANSMISSION_LIMIT_FUNCTION = 15;
  }
  // [NoUnit] [0|15] [initial_value:0]
  optional DrvFaiMsgType drv_fai_msg = 1;
}

message BCM12_238 {
// Report Message
  enum SrPosnVRAPPType {
    SRPOSN_VR_APP_TILT_UP = 0;
    SRPOSN_VR_APP_RESERVED = 1;
    SRPOSN_VR_APP_10 = 10;
    SRPOSN_VR_APP_RESERVED_11 = 11;
    SRPOSN_VR_APP_20 = 20;
    SRPOSN_VR_APP_RESERVED_21 = 21;
    SRPOSN_VR_APP_30 = 30;
    SRPOSN_VR_APP_RESERVED_31 = 31;
    SRPOSN_VR_APP_40 = 40;
    SRPOSN_VR_APP_RESERVED_41 = 41;
    SRPOSN_VR_APP_50 = 50;
    SRPOSN_VR_APP_RESERVED_51 = 51;
    SRPOSN_VR_APP_60 = 60;
    SRPOSN_VR_APP_RESERVED_61 = 61;
    SRPOSN_VR_APP_70 = 70;
    SRPOSN_VR_APP_RESERVED_71 = 71;
    SRPOSN_VR_APP_80 = 80;
    SRPOSN_VR_APP_RESERVED_81 = 81;
    SRPOSN_VR_APP_90 = 90;
    SRPOSN_VR_APP_RESERVED_91 = 91;
    SRPOSN_VR_APP_FULLY_OPEN = 100;
    SRPOSN_VR_APP_FULLY_CLOSE = 101;
    SRPOSN_VR_APP_VENT_AREA = 102;
    SRPOSN_VR_APP_RESERVED_103 = 103;
  }
  // [] [0|127] [initial_value:127]
  optional SrPosnVRAPPType sr_posn_vr_app = 1;
  // [] [0|24564] [initial_value:0]
  optional double rls_fw_brightness = 2;
  // [] [0|81880] [initial_value:0]
  optional double rls_amb_brightness = 3;
}

message GW1_239 {
// Report Message
  enum BasicFunIDType {
    BASICFUNID_NO_CONTROL = 0;
    BASICFUNID_USER_DEFINED_BASIC_FUNCTION = 1;
    BASICFUNID_END = 255;
  }
  enum BasicFunReqType {
    BASICFUNREQ_NO_CONTROL = 0;
    BASICFUNREQ_USER_DEFINED_OPERATION = 1;
    BASICFUNREQ_RESERVED = 255;
  }
  // [NoUnit] [0|255] [initial_value:0]
  optional BasicFunIDType basic_fun_id = 1;
  // [NoUnit] [0|255] [initial_value:0]
  optional BasicFunReqType basic_fun_req = 2;
}

message CSA5_23A {
// Report Message
  enum CustomSwtStsParkType {
    CUSTOMSWTSTS_PARK_RELEASED = 0;
    CUSTOMSWTSTS_PARK_PRESSED = 1;
    CUSTOMSWTSTS_PARK_ERROR = 2;
    CUSTOMSWTSTS_PARK_INVALIDATION = 3;
  }
  // [NoUnit] [0|3] [initial_value:0]
  optional CustomSwtStsParkType custom_swt_sts_park = 1;
}

message IFC_FD2_23D {
// Control Message
  enum LDWLKASnvtyType {
    LDW_LKA_SNVTY_NORMAL = 0;
    LDW_LKA_SNVTY_HIGH_SENSITIVITY = 1;
    LDW_LKA_SNVTY_LOW_SENSITIVITY = 2;
    LDW_LKA_SNVTY_RESERVED = 3;
  }
  enum LSSWarnFormRespType {
    LSSWARNFORMRESP_VOICE = 0;
    LSSWARNFORMRESP_HAPTIC = 1;
    LSSWARNFORMRESP_VOICE_HAPTIC = 2;
    LSSWARNFORMRESP_RESERVED = 3;
  }
  enum AESStsType {
    AESSTS_OFF = 0;
    AESSTS_ON = 1;
    AESSTS_RESERVED = 2;
  }
  enum ESSStsType {
    ESSSTS_OFF = 0;
    ESSSTS_ON = 1;
    ESSSTS_RESERVED = 2;
  }
  enum IFCHandsoffwarnType {
    IFC_HANDSOFFWARN_NO_WARNING = 0;
    IFC_HANDSOFFWARN_LEVEL1_WARNING = 1;
    IFC_HANDSOFFWARN_LEVEL2_WARNING = 2;
    IFC_HANDSOFFWARN_LEVEL3_WARNING = 3;
    IFC_HANDSOFFWARN_LEVEL4_WARNING = 4;
    IFC_HANDSOFFWARN_LEVEL5_WARNING = 5;
    IFC_HANDSOFFWARN_LKA_WARNING = 6;
    IFC_HANDSOFFWARN_LEVEL6_WARNING = 7;
  }
  enum IFCCalibrationStsType {
    IFCCALIBRATIONSTS_CALIBRATION_SUCCESS = 0;
    IFCCALIBRATIONSTS_CALIBRATION_IN_PROCESS = 1;
    IFCCALIBRATIONSTS_CALIBRATION_FAILED = 2;
    IFCCALIBRATIONSTS_RESERVED = 3;
  }
  enum LDWLKAWarnType {
    LDW_LKAWARN_NO_LDW_LKA_WARNING = 0;
    LDW_LKAWARN_LDW_LKA_LEFT_WARNING = 1;
    LDW_LKAWARN_LDW_LKA_RIGHT_WARNING = 2;
    LDW_LKAWARN_RESERVED = 3;
  }
  enum LSSInterventionType {
    LSSINTERVENTION_NO_WARNING_AND_INTERVENTION = 0;
    LSSINTERVENTION_LEFT_WARNING = 1;
    LSSINTERVENTION_RIGHT_WARNING = 2;
    LSSINTERVENTION_LEFT_INTERVENTION = 3;
    LSSINTERVENTION_RIGHT_INTERVENTION = 4;
    LSSINTERVENTION_BOTH_INTERVENTION = 5;
    LSSINTERVENTION_LEFT_WARN_ONLY_REMINDER = 6;
    LSSINTERVENTION_RIGHT_WARN_ONLY_REMINDER = 7;
  }
  enum LSSFuncStsType {
    LSSFUNCSTS_OFF = 0;
    LSSFUNCSTS_LDW_ON = 1;
    LSSFUNCSTS_LKA_ON = 2;
    LSSFUNCSTS_LCK_ON = 3;
    LSSFUNCSTS_RESERVED = 4;
  }
  enum ELKMainStateType {
    ELKMAINSTATE_ELK_OFF = 0;
    ELKMAINSTATE_ELK_STANDBY = 1;
    ELKMAINSTATE_ELK_ENABLE = 2;
    ELKMAINSTATE_ELK_ROAD_EDGE_ACTIVE = 3;
    ELKMAINSTATE_ELK_ONCOMING_ACTIVE = 4;
    ELKMAINSTATE_ELK_OVERTAKING_ACTIVE = 5;
    ELKMAINSTATE_ELK_FAILURE = 6;
    ELKMAINSTATE_RESERVED = 7;
  }
  enum LaneAvailabilityType {
    LANEAVAILABILITY_NOT_AVALIABLE = 0;
    LANEAVAILABILITY_LEFT_LANE_MARKER_AVAILABLE = 1;
    LANEAVAILABILITY_RIGHT_LANE_MARKER_AVAILABLE = 2;
    LANEAVAILABILITY_BOTH_LANE_MARKERS_AVAILABLE = 3;
  }
  enum TJAFollowStsType {
    TJA_FOLLOWSTS_NO_FOLLOW = 0;
    TJA_FOLLOWSTS_FOLLOW_VEHICLE = 1;
    TJA_FOLLOWSTS_RESERVED = 2;
  }
  enum LSSErrStsType {
    LSSERRSTS_NO_ERROR = 0;
    LSSERRSTS_LDW_ERROR = 1;
    LSSERRSTS_LKA_ERROR = 2;
    LSSERRSTS_LCK_ERROR = 3;
    LSSERRSTS_ELK_ERROR = 4;
    LSSERRSTS_ESS_ERROR = 5;
    LSSERRSTS_LSS_TEMPORARILY_UNAVAILABLE = 6;
    LSSERRSTS_RESERVED = 7;
  }
  enum AESinterventionType {
    AESINTERVENTION_NO_INTERVENTION = 0;
    AESINTERVENTION_LEFT_INTERVENTION = 1;
    AESINTERVENTION_RIGHT_INTERVENTION = 2;
    AESINTERVENTION_RESERVED = 3;
  }
  enum ESSinterventionType {
    ESSINTERVENTION_NO_INTERVENTION = 0;
    ESSINTERVENTION_LEFT_INTERVENTION = 1;
    ESSINTERVENTION_RIGHT_INTERVENTION = 2;
    ESSINTERVENTION_RESERVED = 3;
  }
  enum ELKActStsType {
    ELKACTSTS_INACTIVE = 0;
    ELKACTSTS_LEFT_ACTIVE = 1;
    ELKACTSTS_RIGHT_ACTIVE = 2;
    ELKACTSTS_RESERVED = 3;
  }
  enum IntelligentEvaActStsType {
    INTELLIGENTEVAACTSTS_INACTIVE = 0;
    INTELLIGENTEVAACTSTS_LEFT_EVA = 1;
    INTELLIGENTEVAACTSTS_RIGHT_EVA = 2;
    INTELLIGENTEVAACTSTS_INTELLIGENTDETOUR = 3;
  }
  enum LCKStsIndcrType {
    LCKSTSINDCR_NO_INDICATOR = 0;
    LCKSTSINDCR_ACTIVE_INDICATOR = 1;
    LCKSTSINDCR_PASSIVE_INDICATOR = 2;
    LCKSTSINDCR_RESERVED = 3;
  }
  enum IFCLinespecial01TypeType {
    IFC_LINE_SPECIAL01_TYPE_UNDECIDED = 0;
    IFC_LINE_SPECIAL01_TYPE_SOLID = 1;
    IFC_LINE_SPECIAL01_TYPE_DASHED = 2;
    IFC_LINE_SPECIAL01_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE_SPECIAL01_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE_SPECIAL01_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE_SPECIAL01_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE_SPECIAL01_TYPE_BOTTS = 7;
    IFC_LINE_SPECIAL01_TYPE_DECELERATION = 8;
    IFC_LINE_SPECIAL01_TYPE_HOV_LANE = 9;
    IFC_LINE_SPECIAL01_TYPE_ROAD_EDGE = 10;
    IFC_LINE_SPECIAL01_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE_SPECIAL01_TYPE_CURB = 12;
    IFC_LINE_SPECIAL01_TYPE_CONES_POLES = 13;
    IFC_LINE_SPECIAL01_TYPE_REVERSED = 14;
  }
  enum IFCLinespecial02TypeType {
    IFC_LINE_SPECIAL02_TYPE_UNDECIDED = 0;
    IFC_LINE_SPECIAL02_TYPE_SOLID = 1;
    IFC_LINE_SPECIAL02_TYPE_DASHED = 2;
    IFC_LINE_SPECIAL02_TYPE_DOUBLE_SOLID_DASHED = 3;
    IFC_LINE_SPECIAL02_TYPE_DOUBLE_DASHED_SOLID = 4;
    IFC_LINE_SPECIAL02_TYPE_DOUBLE_SOLID_SOLID = 5;
    IFC_LINE_SPECIAL02_TYPE_DOUBLE_DASHED_DASHED = 6;
    IFC_LINE_SPECIAL02_TYPE_BOTTS = 7;
    IFC_LINE_SPECIAL02_TYPE_DECELERATION = 8;
    IFC_LINE_SPECIAL02_TYPE_HOV_LANE = 9;
    IFC_LINE_SPECIAL02_TYPE_ROAD_EDGE = 10;
    IFC_LINE_SPECIAL02_TYPE_ELEVATED_STRUCTURE = 11;
    IFC_LINE_SPECIAL02_TYPE_CURB = 12;
    IFC_LINE_SPECIAL02_TYPE_CONES_POLES = 13;
    IFC_LINE_SPECIAL02_TYPE_REVERSED = 14;
  }
  enum IFCLeLaneTypType {
    IFC_LELANETYP_NO_DISPLAY = 0;
    IFC_LELANETYP_SOLID_LANE = 1;
    IFC_LELANETYP_DASHED_LANE = 2;
    IFC_LELANETYP_RODE_EDGE = 3;
    IFC_LELANETYP_RESERVED = 4;
  }
  enum IFCRiLaneTypType {
    IFC_RILANETYP_NO_DISPLAY = 0;
    IFC_RILANETYP_SOLID_LANE = 1;
    IFC_RILANETYP_DASHED_LANE = 2;
    IFC_RILANETYP_RODE_EDGE = 3;
    IFC_RILANETYP_RESERVED = 4;
  }
  enum IFCNextLeLaneTypType {
    IFC_NEXTLELANETYP_NO_DISPLAY = 0;
    IFC_NEXTLELANETYP_SOLID_LANE = 1;
    IFC_NEXTLELANETYP_DASHED_LANE = 2;
    IFC_NEXTLELANETYP_RODE_EDGE = 3;
    IFC_NEXTLELANETYP_RESERVED = 4;
  }
  enum IFCNextRiLaneTypType {
    IFC_NEXTRILANETYP_NO_DISPLAY = 0;
    IFC_NEXTRILANETYP_SOLID_LANE = 1;
    IFC_NEXTRILANETYP_DASHED_LANE = 2;
    IFC_NEXTRILANETYP_RODE_EDGE = 3;
    IFC_NEXTRILANETYP_RESERVED = 4;
  }
  enum HandsoffTimStsType {
    HANDSOFFTIMSTS_INITIAL = 0;
    HANDSOFFTIMSTS_HANDSOFF_TIME_IS_NORMAL = 1;
    HANDSOFFTIMSTS_HANDSOFF_TIME_IS_LONG = 2;
    HANDSOFFTIMSTS_RESERVED = 3;
  }
  enum IFCFeatPtTypeType {
    IFC_FEATPT_TYPE_NO_DISPLAY = 0;
    IFC_FEATPT_TYPE_MAIN_ROAD = 1;
    IFC_FEATPT_TYPE_ROAD_SPLIT = 2;
    IFC_FEATPT_TYPE_LANE_END = 3;
    IFC_FEATPT_TYPE_CROSSING = 4;
    IFC_FEATPT_TYPE_T_JUNCTION = 5;
    IFC_FEATPT_TYPE_ROUNDABOUT = 6;
    IFC_FEATPT_TYPE_GUIDE_LINE = 10;
    IFC_FEATPT_TYPE_WAITING_AREA = 11;
    IFC_FEATPT_TYPE_VLB = 12;
    IFC_FEATPT_TYPE_RESERVED = 13;
  }
  enum HWATextLibType {
    HWA_TEXT_LIB_DEFAULT = 0;
    HWA_TEXT_LIB_CANCEL_FOR_LONG_OVERRIDE = 1;
    HWA_TEXT_LIB_CANCEL_FOR_LAT_OVERRIDE = 2;
    HWA_TEXT_LIB_AVOID_OBSTACLE = 3;
    HWA_TEXT_LIB_ISL = 4;
    HWA_TEXT_LIB_START_CONFIRM = 5;
    HWA_TEXT_LIB_FOLLOW_CONFIRM = 6;
    HWA_TEXT_LIB_LONG_OVERRIDE = 7;
    HWA_TEXT_LIB_OVER_SPEED = 8;
    HWA_TEXT_LIB_LC_NOT_CANCEL = 9;
    HWA_TEXT_LIB_ADVERSE_WEATHER = 10;
    HWA_TEXT_LIB_SYSTEM_FAULT = 11;
    HWA_TEXT_LIB_NOH_FAULT = 12;
    HWA_TEXT_LIB_NOH_STANDBY = 13;
    HWA_TEXT_LIB_NOH_DEGRADE = 14;
    HWA_TEXT_LIB_DDVERSE_WEATHER2 = 15;
    HWA_TEXT_LIB_FUNCTION_INHIBIT = 17;
    HWA_TEXT_LIB_PILOT_FAIL = 18;
    HWA_TEXT_LIB_AVOID_OBSTACLE2 = 19;
    HWA_TEXT_LIB_AUTO_SPD = 20;
    HWA_TEXT_LIB_NOH_INHIBIT = 21;
    HWA_TEXT_LIB_RESTART_TO_ACTIVATE_ALC = 22;
    HWA_TEXT_LIB_4S_STORE_TO_DEBUG_ALC = 23;
    HWA_TEXT_LIB_ATTENTION_TO_SURROUNDING = 24;
    HWA_TEXT_LIB_TURN_OFF_SIGNAL = 25;
    HWA_TEXT_LIB_SPEED_IS_TOO_HIGH_TO_SUPPORT_INTELLIGENT_CRUISE = 26;
    HWA_TEXT_LIB_U_TURN_WARNING = 27;
    HWA_TEXT_LIB_ROUNDABOUT_WARNING = 28;
    HWA_TEXT_LIB_MANUALLY_SWITCH_ROUTE = 29;
    HWA_TEXT_LIB_TOO_LATE_TO_CHANGE_LANE = 30;
    HWA_TEXT_LIB_OFF_THE_PLANED_ROUTE = 31;
    HWA_TEXT_LIB_SPEEDING = 32;
    HWA_TEXT_LIB_TJA_IS_SUSPEND = 33;
    HWA_TEXT_LIB_EXITING_SOON = 34;
    HWA_TEXT_LIB_NAVIGATION_LEFT_LANE_CHANGE = 35;
    HWA_TEXT_LIB_NAVIGATION_RIGHT_LANE_CHANGE = 36;
    HWA_TEXT_LIB_TRANSIT_LANE = 37;
    HWA_TEXT_LIB_LEFT_LANE_CHANGE_DUE_BARRIER = 38;
    HWA_TEXT_LIB_RIGHT_LANE_CHANGE_DUE_BARRIER = 39;
    HWA_TEXT_LIB_STAY_AWAY_FROM_THE_CART = 40;
    HWA_TEXT_LIB_ABOUCHEMENT = 41;
    HWA_TEXT_LIB_FUNCTION_ABOUT_TO_EXIT = 42;
    HWA_TEXT_LIB_PASSING_DIRECTION = 43;
    HWA_TEXT_LIB_RED_LIGHT_RUNNING_RISK = 44;
    HWA_TEXT_LIB_ROAD_PATTERN_LEFT_CHANGE = 45;
    HWA_TEXT_LIB_ROAD_PATTERN_RIGHT_CHANGE = 46;
    HWA_TEXT_LIB_DEPARTING_PEDESTRIAN = 47;
    HWA_TEXT_LIB_AVOID_BIG_CARS = 48;
    HWA_TEXT_LIB_CUT_IN_CAR = 49;
    HWA_TEXT_LIB_EXTREMITY_LAND_CHANGE = 50;
    HWA_TEXT_LIB_LAND_CHANGE_DUE_TO_SPECIAL_LANE = 51;
    HWA_TEXT_LIB_SPECIAL_LANE = 52;
    HWA_TEXT_LIB_TJA_ICA_RESUMED = 53;
    HWA_TEXT_LIB_CHANGE_LANES_ON_SOLID_LINES = 54;
    HWA_TEXT_LIB_UNABLE_TO_TURN = 55;
    HWA_TEXT_LIB_ATTENTION_ELECTRONIC_EYE = 56;
  }
  enum RoadMarkTypType {
    ROAD_MARK_TYP_DEFAULT = 0;
    ROAD_MARK_TYP_ARROW_STRAIGHT = 1;
    ROAD_MARK_TYP_ARROW_RIGHT = 2;
    ROAD_MARK_TYP_ARROW_LEFT = 3;
    ROAD_MARK_TYP_ARROW_STRAIGHT_RIGHT = 4;
    ROAD_MARK_TYP_ARROW_STRAIGHT_LEFT = 5;
    ROAD_MARK_TYP_ARROW_STRAIGHT_LEFT_RIGHT = 6;
    ROAD_MARK_TYP_ARROW_LEFT_RIGHT = 7;
    ROAD_MARK_TYP_U_TURN_LEFT = 8;
    ROAD_MARK_TYP_U_TURN_RIGHT = 9;
    ROAD_MARK_TYP_DIAMOND = 10;
    ROAD_MARK_TYP_BUS = 11;
    ROAD_MARK_TYP_RESERVED = 12;
  }
  enum LeRoadMarkTypType {
    LEROAD_MARK_TYP_DEFAULT = 0;
    LEROAD_MARK_TYP_ARROW_STRAIGHT = 1;
    LEROAD_MARK_TYP_ARROW_RIGHT = 2;
    LEROAD_MARK_TYP_ARROW_LEFT = 3;
    LEROAD_MARK_TYP_ARROW_STRAIGHT_RIGHT = 4;
    LEROAD_MARK_TYP_ARROW_STRAIGHT_LEFT = 5;
    LEROAD_MARK_TYP_ARROW_STRAIGHT_LEFT_RIGHT = 6;
    LEROAD_MARK_TYP_ARROW_LEFT_RIGHT = 7;
    LEROAD_MARK_TYP_U_TURN_LEFT = 8;
    LEROAD_MARK_TYP_U_TURN_RIGHT = 9;
    LEROAD_MARK_TYP_DIAMOND = 10;
    LEROAD_MARK_TYP_BUS = 11;
    LEROAD_MARK_TYP_RESERVED = 12;
  }
  enum RiRoadMarkTypType {
    RIROAD_MARK_TYP_DEFAULT = 0;
    RIROAD_MARK_TYP_ARROW_STRAIGHT = 1;
    RIROAD_MARK_TYP_ARROW_RIGHT = 2;
    RIROAD_MARK_TYP_ARROW_LEFT = 3;
    RIROAD_MARK_TYP_ARROW_STRAIGHT_RIGHT = 4;
    RIROAD_MARK_TYP_ARROW_STRAIGHT_LEFT = 5;
    RIROAD_MARK_TYP_ARROW_STRAIGHT_LEFT_RIGHT = 6;
    RIROAD_MARK_TYP_ARROW_LEFT_RIGHT = 7;
    RIROAD_MARK_TYP_U_TURN_LEFT = 8;
    RIROAD_MARK_TYP_U_TURN_RIGHT = 9;
    RIROAD_MARK_TYP_DIAMOND = 10;
    RIROAD_MARK_TYP_BUS = 11;
    RIROAD_MARK_TYP_RESERVED = 12;
  }
  enum ACCTgtObjBarDisp02Type {
    ACC_TGTOBJBARDISP_02_NO_DISPLAY = 0;
    ACC_TGTOBJBARDISP_02_DISTANCE_1_ONE_BAR = 1;
    ACC_TGTOBJBARDISP_02_DISTANCE_2_TWO_BARS = 2;
    ACC_TGTOBJBARDISP_02_DISTANCE_3_THREE_BARS = 3;
    ACC_TGTOBJBARDISP_02_DISTANCE_4_FOUR_BARS = 4;
    ACC_TGTOBJBARDISP_02_RESVERED = 5;
  }
  enum ACCTgtObjBarDisp03Type {
    ACC_TGTOBJBARDISP_03_NO_DISPLAY = 0;
    ACC_TGTOBJBARDISP_03_DISTANCE_1_ONE_BAR = 1;
    ACC_TGTOBJBARDISP_03_DISTANCE_2_TWO_BARS = 2;
    ACC_TGTOBJBARDISP_03_DISTANCE_3_THREE_BARS = 3;
    ACC_TGTOBJBARDISP_03_DISTANCE_4_FOUR_BARS = 4;
    ACC_TGTOBJBARDISP_03_RESVERED = 5;
  }
  enum ACCLeTgtObjBarDisp02Type {
    ACC_LETGTOBJBARDISP_02_NO_DISPLAY = 0;
    ACC_LETGTOBJBARDISP_02_DISTANCE_1_ONE_BAR = 1;
    ACC_LETGTOBJBARDISP_02_DISTANCE_2_TWO_BARS = 2;
    ACC_LETGTOBJBARDISP_02_DISTANCE_3_THREE_BARS = 3;
    ACC_LETGTOBJBARDISP_02_DISTANCE_4_FOUR_BARS = 4;
    ACC_LETGTOBJBARDISP_02_RESVERED = 5;
  }
  enum ACCRiTgtObjBarDisp02Type {
    ACC_RITGTOBJBARDISP_02_NO_DISPLAY = 0;
    ACC_RITGTOBJBARDISP_02_DISTANCE_1_ONE_BAR = 1;
    ACC_RITGTOBJBARDISP_02_DISTANCE_2_TWO_BARS = 2;
    ACC_RITGTOBJBARDISP_02_DISTANCE_3_THREE_BARS = 3;
    ACC_RITGTOBJBARDISP_02_DISTANCE_4_FOUR_BARS = 4;
    ACC_RITGTOBJBARDISP_02_RESVERED = 5;
  }
  enum ACCLeTgtObjBarDisp03Type {
    ACC_LETGTOBJBARDISP_03_NO_DISPLAY = 0;
    ACC_LETGTOBJBARDISP_03_DISTANCE_1_ONE_BAR = 1;
    ACC_LETGTOBJBARDISP_03_DISTANCE_2_TWO_BARS = 2;
    ACC_LETGTOBJBARDISP_03_DISTANCE_3_THREE_BARS = 3;
    ACC_LETGTOBJBARDISP_03_DISTANCE_4_FOUR_BARS = 4;
    ACC_LETGTOBJBARDISP_03_RESVERED = 5;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc3 = 1;
  // [] [0|1] [initial_value:0]
  optional bool ldw_resp = 2;
  // [] [0|1] [initial_value:0]
  optional bool lka_resp = 3;
  // [] [0|1] [initial_value:0]
  optional bool lck_resp = 4;
  // [] [0|1] [initial_value:0]
  optional bool elk_resp = 5;
  // [] [0|1] [initial_value:0]
  optional bool aes_resp = 6;
  // [] [0|3] [initial_value:0]
  optional LDWLKASnvtyType ldw_lka_snvty = 7;
  // [] [0|1] [initial_value:0]
  optional bool ifchut_interface = 8;
  // [] [0|1] [initial_value:0]
  optional bool ess_ped_resp = 9;
  // [] [0|1] [initial_value:0]
  optional bool ess_veh_resp = 10;
  // [] [0|3] [initial_value:0]
  optional LSSWarnFormRespType lss_warn_form_resp = 11;
  // [] [0|1] [initial_value:0]
  optional bool lss_resp = 12;
  // [] [0|1] [initial_value:0]
  optional bool intelligent_eva_resp = 13;
  // [] [0|3] [initial_value:0]
  optional AESStsType aes_sts = 14;
  // [] [0|3] [initial_value:0]
  optional ESSStsType ess_sts = 15;
  // [] [0|1] [initial_value:0]
  optional bool aes_err_sts = 16;
  // [] [0|1] [initial_value:0]
  optional bool ess_err_sts = 17;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc3 = 18;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc4 = 19;
  // [] [0|7] [initial_value:0]
  optional IFCHandsoffwarnType ifc_handsoffwarn = 20;
  // [] [0|3] [initial_value:0]
  optional IFCCalibrationStsType ifc_calibration_sts = 21;
  // [] [0|1] [initial_value:0]
  optional bool camera_blockage_sts = 22;
  // [] [0|3] [initial_value:0]
  optional LDWLKAWarnType ldw_lka_warn = 23;
  // [] [0|7] [initial_value:0]
  optional LSSInterventionType lss_intervention = 24;
  // [] [0|7] [initial_value:0]
  optional LSSFuncStsType lss_func_sts = 25;
  // [] [0|7] [initial_value:0]
  optional ELKMainStateType elk_main_state = 26;
  // [] [0|3] [initial_value:0]
  optional LaneAvailabilityType lane_availability = 27;
  // [] [0|3] [initial_value:0]
  optional TJAFollowStsType tja_follow_sts = 28;
  // [] [0|7] [initial_value:0]
  optional LSSErrStsType lss_err_sts = 29;
  // [] [0|3] [initial_value:0]
  optional AESinterventionType ae_sintervention = 30;
  // [] [0|3] [initial_value:0]
  optional ESSinterventionType es_sintervention = 31;
  // [] [0|3] [initial_value:0]
  optional ELKActStsType elk_act_sts = 32;
  // [] [0|3] [initial_value:0]
  optional IntelligentEvaActStsType intelligent_eva_act_sts = 33;
  // [] [0|3] [initial_value:0]
  optional LCKStsIndcrType lck_sts_indcr = 34;
  // [NoUnit] [0|15] [initial_value:0]
  optional IFCLinespecial01TypeType ifc_line_special01_type = 35;
  // [rad] [-2|2.01] [initial_value:201]
  optional double ifc_line_special02_heading_angle = 36;
  // [NoUnit] [0|15] [initial_value:0]
  optional IFCLinespecial02TypeType ifc_line_special02_type = 37;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc4 = 38;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc5 = 39;
  // [] [-0.030705|0.030705] [initial_value:2047]
  optional double ifc_lane_curve = 40;
  // [] [0|7] [initial_value:0]
  optional IFCLeLaneTypType ifc_le_lane_typ = 41;
  // [] [0|7] [initial_value:0]
  optional IFCRiLaneTypType ifc_ri_lane_typ = 42;
  // [] [0|7] [initial_value:0]
  optional IFCNextLeLaneTypType ifc_next_le_lane_typ = 43;
  // [] [0|7] [initial_value:0]
  optional IFCNextRiLaneTypType ifc_next_ri_lane_typ = 44;
  // [] [-8|7.9921875] [initial_value:2496]
  optional double ifc_le_lane_dy = 45;
  // [] [-8|7.9921875] [initial_value:2496]
  optional double ifc_ri_lane_dy = 46;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc5 = 47;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc6 = 48;
  // [] [-8|7.9921875] [initial_value:2496]
  optional double ifc_next_le_lane_dy = 49;
  // [] [-8|7.9921875] [initial_value:2496]
  optional double ifc_next_ri_lane_dy = 50;
  // [m] [-30|30.01] [initial_value:2825]
  optional double ifc_line_special01_dy = 51;
  // [NoUnit] [0|3] [initial_value:1]
  optional HandsoffTimStsType handsoff_tim_sts = 52;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc6 = 53;
  // [PerMtrSq] [-0.024|0.024] [initial_value:480000]
  optional double ifc_line_special01_curv_change = 54;
  // [m] [0|200.01] [initial_value:0]
  optional double ifc_line_special01_dx_start = 55;
  // [PerMtr] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line_special02_curv = 56;
  // [NoUnit] [0|15] [initial_value:0]
  optional IFCFeatPtTypeType ifc_feat_pt_type = 57;
  // [NoUnit] [0|63] [initial_value:0]
  optional HWATextLibType hwa_text_lib = 58;
  // [NoUnit] [0|15] [initial_value:0]
  optional RoadMarkTypType road_mark_typ = 59;
  // [m] [0|150] [initial_value:0]
  optional double road_mark_dx = 60;
  // [m] [-8|8] [initial_value:0]
  optional double road_mark_dy = 61;
  // [NoUnit] [0|15] [initial_value:0]
  optional LeRoadMarkTypType le_road_mark_typ = 62;
  // [m] [0|150] [initial_value:0]
  optional double le_road_mark_dx = 63;
  // [m] [-8|8] [initial_value:0]
  optional double le_road_mark_dy = 64;
  // [NoUnit] [0|15] [initial_value:0]
  optional RiRoadMarkTypType ri_road_mark_typ = 65;
  // [m] [0|150] [initial_value:0]
  optional double ri_road_mark_dx = 66;
  // [m] [-8|8] [initial_value:0]
  optional double ri_road_mark_dy = 67;
  // [rad] [-2|2.01] [initial_value:201]
  optional double ifc_line_special01_heading_angle = 68;
  // [PerMtr] [-0.25|0.25] [initial_value:15625]
  optional double ifc_line_special01_curv = 69;
  // [m] [0|200.01] [initial_value:0]
  optional double ifc_line_special01_dx_end = 70;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCTgtObjBarDisp02Type acc_tgt_obj_bar_disp_02 = 71;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCTgtObjBarDisp03Type acc_tgt_obj_bar_disp_03 = 72;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCLeTgtObjBarDisp02Type acc_le_tgt_obj_bar_disp_02 = 73;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCRiTgtObjBarDisp02Type acc_ri_tgt_obj_bar_disp_02 = 74;
  // [NoUnit] [0|7] [initial_value:0]
  optional ACCLeTgtObjBarDisp03Type acc_le_tgt_obj_bar_disp_03 = 75;
  // [rad] [-2|2.01] [initial_value:201]
  optional double target_path_c1 = 76;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_ifc_fd2 = 77;
}

message HUT_FD3_243 {
// Report Message
  enum HUTODObj7typeType {
    HUT_OD_OBJ7_TYPE_NO_TARGET = 0;
    HUT_OD_OBJ7_TYPE_PEDESTRIAN_OR_BIKER = 1;
    HUT_OD_OBJ7_TYPE_VEHICLE = 2;
    HUT_OD_OBJ7_TYPE_GROUND_LOCKER_OR_BARRIER = 3;
    HUT_OD_OBJ7_TYPE_UNKNOWN_TYPE = 4;
    HUT_OD_OBJ7_TYPE_INVALID_VALUES = 5;
  }
  enum HUTODWorkStsType {
    HUT_OD_WORKSTS_OFF = 0;
    HUT_OD_WORKSTS_FOD_ACTIVE = 1;
    HUT_OD_WORKSTS_ROD_ACTIVE = 2;
    HUT_OD_WORKSTS_RESERVED = 3;
  }
  enum AVM3DAngleReqType {
    AVM_3DANGLEREQ_NO_REQUEST = 0;
    AVM_3DANGLEREQ_ZERO_THREEHUNDREDANDSIXTY = 1;
    AVM_3DANGLEREQ_RESERVED = 362;
    AVM_3DANGLEREQ_INVALID = 511;
  }
  enum MapStatusType {
    MAPSTATUS_UNAUTHORIZE = 0;
    MAPSTATUS_AUTHORIZE = 1;
    MAPSTATUS_RESERVED = 2;
  }
  enum HandsoffTimSetType {
    HANDSOFFTIMSET_INITIAL = 0;
    HANDSOFFTIMSET_HANDSOFF_TIME_IS_NORMAL = 1;
    HANDSOFFTIMSET_HANDSOFF_TIME_IS_LONG = 2;
    HANDSOFFTIMSET_RESERVED = 3;
  }
  enum PrkAreaStsType {
    PRKAREASTS_NO_PARKING_AREA = 0;
    PRKAREASTS_PARKING_AREA = 1;
    PRKAREASTS_UNKNOWN = 2;
    PRKAREASTS_RESERVED = 3;
  }
  enum DeviateSpdModSwtReqType {
    DEVIATESPDMODSWTREQ_INITIAL_VALUE = 0;
    DEVIATESPDMODSWTREQ_FIXED_VALUE = 1;
    DEVIATESPDMODSWTREQ_PERCENTAGE = 2;
    DEVIATESPDMODSWTREQ_RESVERED = 3;
  }
  enum DeviateSpdFixdValReqType {
    DEVIATESPDFIXDVALREQ_INITIAL_VALUE = 0;
    DEVIATESPDFIXDVALREQ_VALID = 1;
    DEVIATESPDFIXDVALREQ_INVALID = 63;
  }
  enum DeviateSpdPreReqType {
    DEVIATESPDPREREQ_INITIAL_VALUE = 0;
    DEVIATESPDPREREQ_VALID = 1;
    DEVIATESPDPREREQ_INVALID = 63;
  }
  enum HWAFaststSwtReqType {
    HWA_FASTSTSWTREQ_INITIAL_VALUE = 0;
    HWA_FASTSTSWTREQ_OFF = 1;
    HWA_FASTSTSWTREQ_ON = 2;
    HWA_FASTSTSWTREQ_RESVERED = 3;
  }
  // [] [0|9] [initial_value:0]
  optional int32 hut_od_obj7_id = 1;
  // [] [0|7] [initial_value:0]
  optional HUTODObj7typeType hut_od_obj7_type = 2;
  // [] [0|1] [initial_value:0]
  optional double hut_od_obj7_confidence = 3;
  // [m] [0|12.6] [initial_value:0]
  optional double hut_od_obj7_width = 4;
  // [m] [0|40] [initial_value:0]
  optional double hut_od_obj7_ground_pos_x = 5;
  // [m] [-20|20] [initial_value:0]
  optional double hut_od_obj7_ground_pos_y = 6;
  // [] [0|1] [initial_value:0]
  optional bool tra_light_swt_req = 7;
  // [] [0|3] [initial_value:0]
  optional HUTODWorkStsType hut_od_work_sts = 8;
  // [ms] [0|65535] [initial_value:0]
  optional int32 hut_od_obj_timestamp = 9;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd1_distance = 10;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd2_distance = 11;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd3_distance = 12;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd4_distance = 13;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd5_distance = 14;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool one_click_aut_drvg_sts = 15;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd6_distance = 16;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd7_distance = 17;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd8_distance = 18;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd9_distance = 19;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd10_distance = 20;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd11_distance = 21;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd12_distance = 22;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd13_distance = 23;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd14_distance = 24;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd15_distance = 25;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd16_distance = 26;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd17_distance = 27;
  // [m] [0|6] [initial_value:0]
  optional double hut_fsd18_distance = 28;
  // 0:No Request, 1~361:对应角度值为 0~360°, 362:Reserved(不关注), 511:Invalid
  optional int32 avm_3_d_angle_req = 29;
  // [] [0|1] [initial_value:0]
  optional bool noh_swt_req = 30;
  // [] [0|1] [initial_value:0]
  optional bool noh_act_req = 31;
  // [] [0|1] [initial_value:0]
  optional bool privacy_sts = 32;
  // [] [0|1] [initial_value:0]
  optional bool adas_gps_auth_sys = 33;
  // [] [0|15] [initial_value:0]
  optional MapStatusType map_status = 34;
  // [NoUnit] [0|3] [initial_value:0]
  optional HandsoffTimSetType handsoff_tim_set = 35;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool unp_swt_req = 36;
  // [NoUnit] [0|3] [initial_value:0]
  optional PrkAreaStsType prk_area_sts = 37;
  // [NoUnit] [0|3] [initial_value:0]
  optional DeviateSpdModSwtReqType deviate_spd_mod_swt_req = 38;
  // [NoUnit] [0|63] [initial_value:0]
  optional int32 deviate_spd_fixd_val_req = 39;
  // [Perc] [0|63] [initial_value:0]
  optional int32 deviate_spd_pre_req = 40;
  // [NoUnit] [0|3] [initial_value:0]
  optional HWAFaststSwtReqType hwa_fastst_swt_req = 41;
}

message CSA3_244 {
// Report Message
  // [] [0|1] [initial_value:0]
  optional bool inr_malfct = 1;
}

message ABM2_245 {
// Report Message
  // [] [0|255] [initial_value:0]
  optional int32 checksum_abm2 = 1;
  // [m/s^2] [-21.592|21.592] [initial_value:22033]
  optional double veh_lgt_accel = 2;
  // [m/s^2] [-21.592|21.592] [initial_value:22033]
  optional double veh_lat_accel = 3;
  // [rad/s] [-2.093|2.093] [initial_value:8721]
  optional double veh_yaw_rate = 4;
  // [] [0|1] [initial_value:0]
  optional bool veh_lgt_accel_vld = 5;
  // [] [0|1] [initial_value:0]
  optional bool veh_lat_accel_vld = 6;
  // [] [0|1] [initial_value:0]
  optional bool veh_dyn_yaw_rate_vld = 7;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_abm2 = 8;
}

message DMS_FD1_24D {
// Report Message
  enum DistrctnLvlACCType {
    DISTRCTNLVL_ACC_NO_DISTRACTION = 0;
    DISTRCTNLVL_ACC_LOW_LEVEL_DISTRACTION = 1;
    DISTRCTNLVL_ACC_MIDDLE_LEVEL_DISTRACTION = 2;
    DISTRCTNLVL_ACC_HI_LEVEL_DISTRACTION = 3;
    DISTRCTNLVL_ACC_RESERVED = 4;
    DISTRCTNLVL_ACC_INVALID = 7;
  }
  enum DrowsnsLvlACCType {
    DROWSNSLVL_ACC_NO_DROWSINESS = 0;
    DROWSNSLVL_ACC_LOW_LEVEL_DROWSINESS = 1;
    DROWSNSLVL_ACC_MIDDLE_LEVEL_DROWSINESS = 2;
    DROWSNSLVL_ACC_HI_LEVEL_DROWSINESS = 3;
    DROWSNSLVL_ACC_RESERVED = 4;
    DROWSNSLVL_ACC_INVALID = 7;
  }
  enum DMSStsType {
    DMSSTS_BLINDNESS = 0;
    DMSSTS_ERROR = 1;
    DMSSTS_ACTIVE = 2;
    DMSSTS_NOT_ACTIVE = 3;
    DMSSTS_RESERVED = 4;
    DMSSTS_INVALID = 7;
  }
  enum DrvBehvStsType {
    DRVBEHVSTS_NO_ACTION = 0;
    DRVBEHVSTS_SMOKING = 1;
    DRVBEHVSTS_ON_THE_PHONE = 2;
    DRVBEHVSTS_DRINKING = 3;
    DRVBEHVSTS_RESERVED = 4;
    DRVBEHVSTS_INVALID = 7;
  }
  // [] [0|7] [initial_value:7]
  optional DistrctnLvlACCType distrctn_lvl_acc = 1;
  // [] [0|7] [initial_value:7]
  optional DrowsnsLvlACCType drowsns_lvl_acc = 2;
  // [] [0|7] [initial_value:7]
  optional DMSStsType dms_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool head_detn = 4;
  // [] [0|7] [initial_value:7]
  optional DrvBehvStsType drv_behv_sts = 5;
}

message CSA6_254 {
// Report Message
  enum LeSideUpRollStsType {
    LESIDEUPROLLSTS_NO_ROLLING = 0;
    LESIDEUPROLLSTS_UP1 = 1;
    LESIDEUPROLLSTS_UP2 = 2;
    LESIDEUPROLLSTS_UP3 = 3;
    LESIDEUPROLLSTS_UP4 = 4;
    LESIDEUPROLLSTS_UP5 = 5;
    LESIDEUPROLLSTS_UP6 = 6;
    LESIDEUPROLLSTS_UP7 = 7;
    LESIDEUPROLLSTS_RESERVED = 8;
  }
  enum LeSideDoRollStsType {
    LESIDEDOROLLSTS_NO_ROLLING = 0;
    LESIDEDOROLLSTS_UP1 = 1;
    LESIDEDOROLLSTS_UP2 = 2;
    LESIDEDOROLLSTS_UP3 = 3;
    LESIDEDOROLLSTS_UP4 = 4;
    LESIDEDOROLLSTS_UP5 = 5;
    LESIDEDOROLLSTS_UP6 = 6;
    LESIDEDOROLLSTS_UP7 = 7;
    LESIDEDOROLLSTS_RESERVED = 8;
  }
  // [] [0|15] [initial_value:0]
  optional LeSideUpRollStsType le_side_up_roll_sts = 1;
  // [] [0|15] [initial_value:0]
  optional LeSideDoRollStsType le_side_do_roll_sts = 2;
}

message BLE1_25C {
// Report Message
  enum BLEConnStsType {
    BLECONNSTS_INITIAL_VALUE = 0;
    BLECONNSTS_NOT_CONNECTION = 1;
    BLECONNSTS_CONNECTION = 2;
    BLECONNSTS_RESERVED = 3;
  }
  enum BLESecurityStsType {
    BLESECURITYSTS_INITIAL_VALUE = 0;
    BLESECURITYSTS_NOT_IN_SECURITY_CERTIFICATION_STATUS = 1;
    BLESECURITYSTS_IN_SECURITY_CERTIFICATION_STATUS = 2;
    BLESECURITYSTS_RESERVED = 3;
  }
  enum PrkOutModSelRmtType {
    PRKOUTMODSEL_RMT_NO_SELECT = 0;
    PRKOUTMODSEL_RMT_FRONT_VERTICAL_HEAD_PARK_OUT = 1;
    PRKOUTMODSEL_RMT_REAR_VERTICAL_TAIL_PARK_OUT = 2;
    PRKOUTMODSEL_RMT_LEFT_PARALLEL_LEFT_PARK_OUT = 3;
    PRKOUTMODSEL_RMT_RIGHT_PARALLEL_RIGHT_PARK_OUT = 4;
    PRKOUTMODSEL_RMT_SUMMON_PATH_ONE_PARK_OUT = 5;
    PRKOUTMODSEL_RMT_SUMMON_PATH_TWO_PARK_OUT = 6;
    PRKOUTMODSEL_RMT_RESERVED = 7;
  }
  enum PrkOutTypeReqRmtType {
    PRKOUTTYPEREQ_RMT_NO_SELECT = 0;
    PRKOUTTYPEREQ_RMT_PARALLEL_PARK_OUT = 1;
    PRKOUTTYPEREQ_RMT_VERTICAL_PARK_OUT = 2;
    PRKOUTTYPEREQ_RMT_INTELLIGENT_SUMMON = 3;
  }
  enum PrkOutModSelRmt1Type {
    PRKOUTMODSEL_RMT1_NO_SELECT = 0;
    PRKOUTMODSEL_RMT1_VERTICAL_FRONT_LEFT_BERTHING = 1;
    PRKOUTMODSEL_RMT1_VERTICAL_FRONT_RIGHT_BERTHING = 2;
    PRKOUTMODSEL_RMT1_VERTICAL_REAR_LEFT_BERTHING = 3;
    PRKOUTMODSEL_RMT1_VERTICAL_REAR_RIGHT_BERTHING = 4;
    PRKOUTMODSEL_RMT1_RESERVED = 5;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ble1 = 1;
  // [] [0|3] [initial_value:0]
  optional BLEConnStsType ble_conn_sts = 2;
  // [] [0|3] [initial_value:0]
  optional BLESecurityStsType ble_security_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool prk_out_veh_strt_req_rmt = 4;
  // [] [0|1] [initial_value:0]
  optional bool park_abort_req_rmt = 5;
  // [] [0|7] [initial_value:0]
  optional PrkOutModSelRmtType prk_out_mod_sel_rmt = 6;
  // [] [0|1] [initial_value:0]
  optional bool prkgslid_req_rmt = 7;
  // [] [0|3] [initial_value:0]
  optional PrkOutTypeReqRmtType prk_out_type_req_rmt = 8;
  // [] [0|1] [initial_value:0]
  optional bool prkg_sync_strt_req_rmt = 9;
  // [] [0|1] [initial_value:0]
  optional bool prk_out_veh_strt_req_rmt_valid = 10;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_choke_req_rmt = 11;
  // [NoUnit] [0|7] [initial_value:0]
  optional PrkOutModSelRmt1Type prk_out_mod_sel_rmt1 = 12;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ble1 = 13;
}

message HUT32_26F {
// Report Message
  enum ScrnOpStsType {
    SCRNOPSTS_RELEASE = 0;
    SCRNOPSTS_PRESSED = 1;
    SCRNOPSTS_HOLD = 2;
    SCRNOPSTS_RESERVED = 3;
  }
  enum PrkgCtrlModReqType {
    PRKGCTRLMODREQ_NO_ACTION = 0;
    PRKGCTRLMODREQ_PARKING_IN_CAR = 1;
    PRKGCTRLMODREQ_REMOTE_PARKING = 2;
    PRKGCTRLMODREQ_REMOTE_SEARCHING_SLOT = 3;
  }
  enum ContnPrkgReqType {
    CONTNPRKGREQ_NO_SELECT = 0;
    CONTNPRKGREQ_SELECT_CONTINUE_PARKING = 1;
    CONTNPRKGREQ_CANCLE_PARKING = 2;
    CONTNPRKGREQ_INVALID = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_hut32 = 1;
  // [] [0|3] [initial_value:0]
  optional ScrnOpStsType scrn_op_sts = 2;
  // [NoUnit] [0|4094] [initial_value:4094]
  optional int32 xlevel = 3;
  // [NoUnit] [0|4094] [initial_value:4094]
  optional int32 ylevel = 4;
  // [] [0|1] [initial_value:0]
  optional bool det_video_lost = 5;
  // [] [0|1] [initial_value:0]
  optional bool prkg_ctrl_mod_req_valid = 6;
  // [] [0|3] [initial_value:0]
  optional PrkgCtrlModReqType prkg_ctrl_mod_req = 7;
  // [] [0|1] [initial_value:0]
  optional bool contn_prkg_req_valid = 8;
  // [] [0|3] [initial_value:0]
  optional ContnPrkgReqType contn_prkg_req = 9;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_hut32 = 10;
}

message HAP_FD2_274 {
// Control Message
  enum AVMCurrStsType {
    AVM_CURRSTS_OFF = 0;
    AVM_CURRSTS_ON = 1;
    AVM_CURRSTS_AVM_NO_VIDEO_A3_A8_C0_EF_A3_A9 = 2;
    AVM_CURRSTS_INITIALLING = 3;
  }
  enum GuidOvlStsType {
    GUIDOVLSTS_AROUND_VIEW_AND_SINGLE_VIEW = 0;
    GUIDOVLSTS_ONLY_AROUND_VIEW = 1;
    GUIDOVLSTS_ONLY_SINGLE_VIEW = 2;
    GUIDOVLSTS_NO_GUIDANCE = 3;
  }
  enum LaneCalStsType {
    LANECALSTS_NO_COMMANDS = 0;
    LANECALSTS_IN_CALIBRATION_PROCESS = 1;
    LANECALSTS_CALIBRATION_SUCCESS = 2;
    LANECALSTS_CALIBRATION_FAILURE = 3;
  }
  enum RadarDispStsType {
    RADARDISPSTS_BOTH_AROUND_VIEW_AND_SINGLE_VIEW_RADAR_DISPLAY = 0;
    RADARDISPSTS_ONLY_AROUND_VIEW_RADAR_DISPLAY = 1;
    RADARDISPSTS_ONLY_SINGLE_VIEW_RADAR_DISPLAY = 2;
    RADARDISPSTS_NO_RADAR_DISPLAY = 3;
  }
  enum LaneCalFltStsType {
    LANECALFLTSTS_NO_FAULT = 0;
    LANECALFLTSTS_FRONT_VIEW_CALIBRATION_ABNORMAL = 1;
    LANECALFLTSTS_REAR_VIEW_CALIBRATION_ABNORMAL = 2;
    LANECALFLTSTS_LEFT_VIEW_CALIBRATION_ABNORMAL = 3;
    LANECALFLTSTS_RIGHT_VIEW_CALIBRATION_ABNORMAL = 4;
    LANECALFLTSTS_CALIBRATION_DATA_STORAGE_FAILED = 5;
    LANECALFLTSTS_RESERVED = 6;
  }
  enum CarMdlTrsprcyStsType {
    CARMDLTRSPRCYSTS_OPAQUE = 0;
    CARMDLTRSPRCYSTS_TRANSLUCENT = 1;
    CARMDLTRSPRCYSTS_COMPLETELY_TRANSPARENT = 2;
    CARMDLTRSPRCYSTS_FAILURE = 3;
  }
  enum MdlColrStsType {
    MDLCOLRSTS_SILVERY = 0;
    MDLCOLRSTS_RED = 1;
    MDLCOLRSTS_GRAY = 2;
    MDLCOLRSTS_WHITE = 3;
    MDLCOLRSTS_BLACK = 4;
    MDLCOLRSTS_BROWN = 5;
    MDLCOLRSTS_BLUE = 6;
    MDLCOLRSTS_ORANGE = 7;
    MDLCOLRSTS_GREEN = 8;
    MDLCOLRSTS_GOLD = 9;
    MDLCOLRSTS_PURPLE = 10;
    MDLCOLRSTS_RESERVED = 11;
  }
  enum SigViewIndcnType {
    SIGVIEWINDCN_NO_COMMANDS = 0;
    SIGVIEWINDCN_FRONT_TO_CENTER_VIEW = 1;
    SIGVIEWINDCN_REAR_TO_CENTER_VIEW = 2;
    SIGVIEWINDCN_LEFT_TO_CENTER_VIEW = 3;
    SIGVIEWINDCN_RIGHT_TO_CENTER_VIEW = 4;
    SIGVIEWINDCN_LEFT_FRONT_TO_CENTER_VIEW = 5;
    SIGVIEWINDCN_RIGHT_FRONT_TO_CENTER_VIEW = 6;
    SIGVIEWINDCN_LEFT_REAR_TO_CENTER_VIEW = 7;
    SIGVIEWINDCN_RIGHT_REAR_TO_CENTER_VIEW = 8;
    SIGVIEWINDCN_FRONT_VIEW = 9;
    SIGVIEWINDCN_REAR_VIEW = 10;
    SIGVIEWINDCN_LEFT_VIEW = 11;
    SIGVIEWINDCN_RIGHT_VIEW = 12;
    SIGVIEWINDCN_LEFT_TURN_3D_VIEW = 13;
    SIGVIEWINDCN_RIGHT_TURN_3D_VIEW = 14;
    SIGVIEWINDCN_FRONT_OVERLOOK_VIEW = 15;
    SIGVIEWINDCN_REAR_OVERLOOK_VIEW = 16;
    SIGVIEWINDCN_FRONT_180_DEG_VIEW = 17;
    SIGVIEWINDCN_REAR_180_DEG_VIEW = 18;
    SIGVIEWINDCN_FRONT_WHEEL_VIEW = 19;
    SIGVIEWINDCN_REAR_WHEEL_VIEW = 20;
    SIGVIEWINDCN_FRONT_WASH_VIEW = 21;
    SIGVIEWINDCN_REAR_WASH_VIEW = 22;
    SIGVIEWINDCN_FREE_3D_VIEW = 23;
    SIGVIEWINDCN_LEFTRIGHT_VIEW = 24;
    SIGVIEWINDCN_ENGINE_ROOM_VIEW = 25;
    SIGVIEWINDCN_CCO_VIEW = 26;
    SIGVIEWINDCN_WIRELESS_CHARGING_VIEW = 27;
    SIGVIEWINDCN_WHEEL_WARNING_VIEW = 28;
    SIGVIEWINDCN_VERTICAL_VIEW = 29;
    SIGVIEWINDCN_REAR_SINGLE_VIEW = 30;
    SIGVIEWINDCN_FRONT_SINGLE_VIEW = 31;
    SIGVIEWINDCN_RESERVED = 32;
    SIGVIEWINDCN_3D_CAR_MODEL_ROUND_VIEW = 63;
  }
  enum AVM3DAngStsType {
    AVM_3DANGSTS_NOT_ACTIVE = 0;
    AVM_3DANGSTS_0_360_A1_E3 = 1;
    AVM_3DANGSTS_RESERVED = 362;
    AVM_3DANGSTS_INVALID = 511;
  }
  enum DVRWorkStsType {
    DVRWORKSTS_DVR_STANDBY = 0;
    DVRWORKSTS_DVR_ACTIVE = 1;
    DVRWORKSTS_DVR_INTERFAILED = 2;
    DVRWORKSTS_DVR_NETFAILED = 3;
  }
  enum HAPHmiIndexType {
    HAP_HMI_INDEX_RESERVED = 0;
    HAP_HMI_INDEX_MAIN_SCREEN = 1;
    HAP_HMI_INDEX_EOL_TEST = 2;
    HAP_HMI_INDEX_P2P_SCREEN = 3;
    HAP_HMI_INDEX_ASSIST_SCREEN = 4;
    HAP_HMI_INDEX_HAVP_SCREEN = 5;
    HAP_HMI_INDEX_PAVP_SCREEN = 6;
    HAP_HMI_INDEX_RADS_SCREEN = 7;
    HAP_HMI_INDEX_FADS_SCREEN = 8;
    HAP_HMI_INDEX_RESERVED_9 = 9;
  }
  enum WheWarningType {
    WHEWARNING_NO_WARNING = 0;
    WHEWARNING_LEFT_FRONT_WARNING = 1;
    WHEWARNING_LEFT_REAR_WARING = 2;
    WHEWARNING_RIGHT_FRONT_WARING = 3;
    WHEWARNING_RIGHT_REAR_WARING = 4;
    WHEWARNING_FRONT_WARNING = 5;
    WHEWARNING_REAR_WARNING = 6;
    WHEWARNING_LEFT_WARNING = 7;
    WHEWARNING_RIGHT_WARING = 8;
    WHEWARNING_FRONT_LEFT_REAR_RIGHT_WARNING = 9;
    WHEWARNING_LEFT_REAR_FRONT_RIGHT_WARNING = 10;
    WHEWARNING_EXCLUDING_RIGHT_REAR_WARNING = 11;
    WHEWARNING_EXCLUDING_RIGHT_FRONT_WARNING = 12;
    WHEWARNING_EXCLUDING_LEFT_FRONT_WARNING = 13;
    WHEWARNING_EXCLUDING_LEFT_REAR_WARNING = 14;
    WHEWARNING_ALL_WARNING = 15;
  }
  // [] [0|1] [initial_value:0]
  optional bool auto_avm_swt_set_sts = 1;
  // [] [0|3] [initial_value:0]
  optional AVMCurrStsType avm_curr_sts = 2;
  // [] [0|3] [initial_value:0]
  optional GuidOvlStsType guid_ovl_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool frnt_cam_inp_fail_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool rear_cam_inp_fail_sts = 5;
  // [] [0|1] [initial_value:0]
  optional bool le_cam_inp_fail_sts = 6;
  // [] [0|1] [initial_value:0]
  optional bool ri_cam_inp_fail_sts = 7;
  // [] [0|1] [initial_value:0]
  optional bool eol_not_cmpltd = 8;
  // [] [0|1] [initial_value:0]
  optional bool ovl_op_fail_sts = 9;
  // [] [0|3] [initial_value:0]
  optional LaneCalStsType lane_cal_sts = 10;
  // [] [0|3] [initial_value:0]
  optional RadarDispStsType radar_disp_sts = 11;
  // [] [0|7] [initial_value:0]
  optional LaneCalFltStsType lane_cal_flt_sts = 12;
  // [] [0|1] [initial_value:0]
  optional bool auto_view_chg_func_sts = 13;
  // [] [0|3] [initial_value:0]
  optional CarMdlTrsprcyStsType car_mdl_trsprcy_sts = 14;
  // [] [0|1] [initial_value:0]
  optional bool car_mdl_disp_sts = 15;
  // [] [0|1] [initial_value:0]
  optional bool curr_cal_result_sts = 16;
  // [] [0|15] [initial_value:0]
  optional MdlColrStsType mdl_colr_sts = 17;
  // [] [0|63] [initial_value:0]
  optional SigViewIndcnType sig_view_indcn = 18;
  // [] [0|1] [initial_value:0]
  optional bool hap_disp_cmd = 19;
  // [deg] [0|360] [initial_value:0]
  optional AVM3DAngStsType avm_3_d_ang_sts = 20;
  // [] [0|1] [initial_value:0]
  optional bool mod_work_sts = 21;
  // [] [0|3] [initial_value:0]
  optional DVRWorkStsType dvr_work_sts = 22;
  // [] [0|1] [initial_value:0]
  optional bool mod_alarm_sts = 23;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool tra_ovl_sts = 24;
  // [] [0|15] [initial_value:0]
  optional HAPHmiIndexType hap_hmi_index = 25;
  // [NoUnit] [0|15] [initial_value:0]
  optional WheWarningType whe_warning = 26;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_l1 = 27;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_l2 = 28;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_l3 = 29;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_r1 = 30;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_r2 = 31;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool park_slot_disp_r3 = 32;
}

message IP2_27F {
// Report Message
  // [Km] [0|999999.9] [initial_value:0]
  optional double ip_veh_tot_distance = 1;
  // [] [0|1] [initial_value:0]
  optional bool ip_err = 2;
  // [] [0|1] [initial_value:0]
  optional bool ip_acc_err = 3;
  // [] [0|1] [initial_value:0]
  optional bool ip_veh_tot_distance_valid = 4;
  // [] [0|1] [initial_value:0]
  optional bool ip_veh_spd_unit = 5;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_ip2 = 6;
}

message HAP_FD6_289 {
// Control Message
  enum HAPRtCamWorkStsType {
    HAP_RTCAM_WORKSTS_DISABLE = 0;
    HAP_RTCAM_WORKSTS_ENABLE = 1;
    HAP_RTCAM_WORKSTS_ACTIVE = 2;
    HAP_RTCAM_WORKSTS_FAILED = 3;
  }
  enum HAPRtSideObj1typeType {
    HAP_RTSIDE_OBJ1_TYPE_NO_TARGET = 0;
    HAP_RTSIDE_OBJ1_TYPE_PEDESTRIAN = 1;
    HAP_RTSIDE_OBJ1_TYPE_BIKER = 2;
    HAP_RTSIDE_OBJ1_TYPE_VEHICLE = 3;
    HAP_RTSIDE_OBJ1_TYPE_GROUND_LOCKER = 4;
    HAP_RTSIDE_OBJ1_TYPE_BARRIER = 5;
    HAP_RTSIDE_OBJ1_TYPE_RESERVED = 6;
    HAP_RTSIDE_OBJ1_TYPE_UNKNOWN_TYPE = 7;
  }
  enum HAPRtSideObj2typeType {
    HAP_RTSIDE_OBJ2_TYPE_NO_TARGET = 0;
    HAP_RTSIDE_OBJ2_TYPE_PEDESTRIAN = 1;
    HAP_RTSIDE_OBJ2_TYPE_BIKER = 2;
    HAP_RTSIDE_OBJ2_TYPE_VEHICLE = 3;
    HAP_RTSIDE_OBJ2_TYPE_GROUND_LOCKER = 4;
    HAP_RTSIDE_OBJ2_TYPE_BARRIER = 5;
    HAP_RTSIDE_OBJ2_TYPE_RESERVED = 6;
    HAP_RTSIDE_OBJ2_TYPE_UNKNOWN_TYPE = 7;
  }
  enum HAPRtSideObj3typeType {
    HAP_RTSIDE_OBJ3_TYPE_NO_TARGET = 0;
    HAP_RTSIDE_OBJ3_TYPE_PEDESTRIAN = 1;
    HAP_RTSIDE_OBJ3_TYPE_BIKER = 2;
    HAP_RTSIDE_OBJ3_TYPE_VEHICLE = 3;
    HAP_RTSIDE_OBJ3_TYPE_GROUND_LOCKER = 4;
    HAP_RTSIDE_OBJ3_TYPE_BARRIER = 5;
    HAP_RTSIDE_OBJ3_TYPE_RESERVED = 6;
    HAP_RTSIDE_OBJ3_TYPE_UNKNOWN_TYPE = 7;
  }
  enum HAPRtSideObj4typeType {
    HAP_RTSIDE_OBJ4_TYPE_NO_TARGET = 0;
    HAP_RTSIDE_OBJ4_TYPE_PEDESTRIAN = 1;
    HAP_RTSIDE_OBJ4_TYPE_BIKER = 2;
    HAP_RTSIDE_OBJ4_TYPE_VEHICLE = 3;
    HAP_RTSIDE_OBJ4_TYPE_GROUND_LOCKER = 4;
    HAP_RTSIDE_OBJ4_TYPE_BARRIER = 5;
    HAP_RTSIDE_OBJ4_TYPE_RESERVED = 6;
    HAP_RTSIDE_OBJ4_TYPE_UNKNOWN_TYPE = 7;
  }
  enum USSFLSide1StatusType {
    USS_FLSIDE1_STATUS_DISABLE = 0;
    USS_FLSIDE1_STATUS_ENABLE = 1;
    USS_FLSIDE1_STATUS_ACTIVE = 2;
    USS_FLSIDE1_STATUS_FAILED = 3;
  }
  enum USSRLSide2StatusType {
    USS_RLSIDE2_STATUS_DISABLE = 0;
    USS_RLSIDE2_STATUS_ENABLE = 1;
    USS_RLSIDE2_STATUS_ACTIVE = 2;
    USS_RLSIDE2_STATUS_FAILED = 3;
  }
  enum USSFRSide1StatusType {
    USS_FRSIDE1_STATUS_DISABLE = 0;
    USS_FRSIDE1_STATUS_ENABLE = 1;
    USS_FRSIDE1_STATUS_ACTIVE = 2;
    USS_FRSIDE1_STATUS_FAILED = 3;
  }
  enum USSRRSide2StatusType {
    USS_RRSIDE2_STATUS_DISABLE = 0;
    USS_RRSIDE2_STATUS_ENABLE = 1;
    USS_RRSIDE2_STATUS_ACTIVE = 2;
    USS_RRSIDE2_STATUS_FAILED = 3;
  }
  enum USSFrontSysStatusType {
    USS_FRONTSYS_STATUS_DISABLE = 0;
    USS_FRONTSYS_STATUS_ENABLE = 1;
    USS_FRONTSYS_STATUS_ACTIVE = 2;
    USS_FRONTSYS_STATUS_FAILED = 3;
  }
  // [] [0|3] [initial_value:1]
  optional HAPRtCamWorkStsType hap_rt_cam_work_sts = 1;
  // [] [0|3] [initial_value:0]
  optional int32 hap_rt_side_obj1_id = 2;
  // [] [0|7] [initial_value:0]
  optional HAPRtSideObj1typeType hap_rt_side_obj1_type = 3;
  // [] [0|1] [initial_value:0]
  optional double hap_rt_side_obj1_confidence = 4;
  // [m] [0|40] [initial_value:0]
  optional double hap_rt_side_obj1_ground_pos_x = 5;
  // [m] [-20|20] [initial_value:0]
  optional double hap_rt_side_obj1_ground_pos_y = 6;
  // [] [0|3] [initial_value:0]
  optional int32 hap_rt_side_obj2_id = 7;
  // [] [0|7] [initial_value:0]
  optional HAPRtSideObj2typeType hap_rt_side_obj2_type = 8;
  // [] [0|1] [initial_value:0]
  optional double hap_rt_side_obj2_confidence = 9;
  // [m] [0|40] [initial_value:0]
  optional double hap_rt_side_obj2_ground_pos_x = 10;
  // [m] [-20|20] [initial_value:0]
  optional double hap_rt_side_obj2_ground_pos_y = 11;
  // [] [0|3] [initial_value:0]
  optional int32 hap_rt_side_obj3_id = 12;
  // [] [0|7] [initial_value:0]
  optional HAPRtSideObj3typeType hap_rt_side_obj3_type = 13;
  // [] [0|1] [initial_value:0]
  optional double hap_rt_side_obj3_confidence = 14;
  // [m] [0|40] [initial_value:0]
  optional double hap_rt_side_obj3_ground_pos_x = 15;
  // [m] [-20|20] [initial_value:0]
  optional double hap_rt_side_obj3_ground_pos_y = 16;
  // [] [0|3] [initial_value:0]
  optional int32 hap_rt_side_obj4_id = 17;
  // [] [0|7] [initial_value:0]
  optional HAPRtSideObj4typeType hap_rt_side_obj4_type = 18;
  // [] [0|1] [initial_value:0]
  optional double hap_rt_side_obj4_confidence = 19;
  // [m] [0|40] [initial_value:0]
  optional double hap_rt_side_obj4_ground_pos_x = 20;
  // [m] [-20|20] [initial_value:0]
  optional double hap_rt_side_obj4_ground_pos_y = 21;
  // [] [0|3] [initial_value:1]
  optional USSFLSide1StatusType uss_fl_side1_status = 22;
  // [m] [0|12.6] [initial_value:0]
  optional double uss_fl_side1_dist = 23;
  // [] [0|3] [initial_value:1]
  optional USSRLSide2StatusType uss_rl_side2_status = 24;
  // [m] [0|12.6] [initial_value:0]
  optional double uss_rl_side2_dist = 25;
  // [] [0|3] [initial_value:1]
  optional USSFRSide1StatusType uss_fr_side1_status = 26;
  // [m] [0|12.6] [initial_value:0]
  optional double uss_fr_side1_dist = 27;
  // [] [0|3] [initial_value:1]
  optional USSRRSide2StatusType uss_rr_side2_status = 28;
  // [m] [0|12.6] [initial_value:0]
  optional double uss_rr_side2_dist = 29;
  // [] [0|3] [initial_value:1]
  optional USSFrontSysStatusType uss_front_sys_status = 30;
  // [] [0|1] [initial_value:0]
  optional double uss_flc_near_obj_confidence = 31;
  // [m] [0|6.2] [initial_value:0]
  optional double uss_flc_near_obj_pos_x = 32;
  // [m] [-3.1|3.1] [initial_value:0]
  optional double uss_flc_near_obj_pos_y = 33;
  // [] [0|1] [initial_value:0]
  optional double uss_flm_near_obj_confidence = 34;
  // [m] [0|6.2] [initial_value:0]
  optional double uss_flm_near_obj_pos_x = 35;
  // [m] [-3.1|3.1] [initial_value:0]
  optional double uss_flm_near_obj_pos_y = 36;
  // [] [0|1] [initial_value:0]
  optional double uss_frm_near_obj_confidence = 37;
  // [m] [0|6.2] [initial_value:0]
  optional double uss_frm_near_obj_pos_x = 38;
  // [m] [-3.1|3.1] [initial_value:0]
  optional double uss_frm_near_obj_pos_y = 39;
  // [] [0|1] [initial_value:0]
  optional double uss_frc_near_obj_confidence = 40;
  // [m] [0|6.2] [initial_value:0]
  optional double uss_frc_near_obj_pos_x = 41;
  // [m] [-3.1|3.1] [initial_value:0]
  optional double uss_frc_near_obj_pos_y = 42;
}

message IP1_293 {
// Report Message
  // [km/h] [0|255] [initial_value:0]
  optional double ip_veh_spd_disp = 1;
  // [Mile per hour] [0|255] [initial_value:0]
  optional int32 ip_vel_spd_disp_mile = 2;
}

message PEPS2_295 {
// Report Message
  enum SysPowerModType {
    SYSPOWERMOD_OFF = 0;
    SYSPOWERMOD_ACC_RESERVED = 1;
    SYSPOWERMOD_ON = 2;
    SYSPOWERMOD_CRANK = 3;
  }
  // [] [0|3] [initial_value:0]
  optional SysPowerModType sys_power_mod = 1;
  // [] [0|1] [initial_value:0]
  optional bool sys_power_mod_vld = 2;
}

message HAP_FD3_298 {
// Control Message
  enum RPASNearBrrdstnType {
    RPAS_NEARBRRDSTN_VALID_VALUE = 0;
    RPAS_NEARBRRDSTN_RESERVED = 151;
    RPAS_NEARBRRDSTN_NO_DISPLAY = 255;
  }
  enum HAVPProcRaType {
    HAVP_PROCRA_0_100 = 0;
    HAVP_PROCRA_RESERVED = 101;
    HAVP_PROCRA_NO_DISPLAY = 127;
  }
  enum P2PProcBarType {
    P2P_PROCBAR_0_100 = 0;
    P2P_PROCBAR_RESERVED = 101;
    P2P_PROCBAR_NO_DISPLAY = 127;
  }
  enum APSObjRiSideAr1Type {
    APS_OBJRISIDEAR1_NO_OBJECT = 0;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJRISIDEAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJRISIDEAR1_RESERVED = 7;
  }
  enum APSObjRiSideAr2Type {
    APS_OBJRISIDEAR2_NO_OBJECT = 0;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJRISIDEAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJRISIDEAR2_RESERVED = 7;
  }
  enum APSObjRiSideAr3Type {
    APS_OBJRISIDEAR3_NO_OBJECT = 0;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJRISIDEAR3_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJRISIDEAR3_RESERVED = 7;
  }
  enum APSObjRiSideAr4Type {
    APS_OBJRISIDEAR4_NO_OBJECT = 0;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJRISIDEAR4_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJRISIDEAR4_RESERVED = 7;
  }
  enum APSObjRiSideAr5Type {
    APS_OBJRISIDEAR5_NO_OBJECT = 0;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJRISIDEAR5_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJRISIDEAR5_RESERVED = 7;
  }
  enum APSObjLeSideAr1Type {
    APS_OBJLESIDEAR1_NO_OBJECT = 0;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJLESIDEAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJLESIDEAR1_RESERVED = 7;
  }
  enum APSObjLeSideAr2Type {
    APS_OBJLESIDEAR2_NO_OBJECT = 0;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJLESIDEAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJLESIDEAR2_RESERVED = 7;
  }
  enum APSObjLeSideAr3Type {
    APS_OBJLESIDEAR3_NO_OBJECT = 0;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJLESIDEAR3_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJLESIDEAR3_RESERVED = 7;
  }
  enum APSObjLeSideAr4Type {
    APS_OBJLESIDEAR4_NO_OBJECT = 0;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJLESIDEAR4_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJLESIDEAR4_RESERVED = 7;
  }
  enum APSObjLeSideAr5Type {
    APS_OBJLESIDEAR5_NO_OBJECT = 0;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_1_0_10CM = 1;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_2_11_20CM = 2;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_3_21_30CM = 3;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_4_31_40CM = 4;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_5_41_50CM = 5;
    APS_OBJLESIDEAR5_OBJECT_IN_ZONE_6_51_60CM = 6;
    APS_OBJLESIDEAR5_RESERVED = 7;
  }
  enum SDWSoundIndcnType {
    SDW_SOUNDINDCN_TONE_0_NO_WARNING = 0;
    SDW_SOUNDINDCN_TONE_1_LONG_BEEP = 1;
    SDW_SOUNDINDCN_TONE_2_1_5_HZ = 2;
    SDW_SOUNDINDCN_TONE_3_3HZ = 3;
  }
  enum RPASObjRRCornrAr1Type {
    RPAS_OBJRRCORNRAR1_NO_OBJECT = 0;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRRCORNRAR1_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRRCORNRAR1_RESERVED = 13;
  }
  enum RPASObjRRCornrAr2Type {
    RPAS_OBJRRCORNRAR2_NO_OBJECT = 0;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRRCORNRAR2_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRRCORNRAR2_RESERVED = 13;
  }
  enum RPASObjRRMidlAr1Type {
    RPAS_OBJRRMIDLAR1_NO_OBJECT = 0;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_13_121_130CM = 13;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_14_131_140CM = 14;
    RPAS_OBJRRMIDLAR1_OBJECT_IN_ZONE_15_141_150CM = 15;
  }
  enum RPASObjRRMidlAr2Type {
    RPAS_OBJRRMIDLAR2_NO_OBJECT = 0;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_13_121_130CM = 13;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_14_131_140CM = 14;
    RPAS_OBJRRMIDLAR2_OBJECT_IN_ZONE_15_141_150CM = 15;
  }
  enum RPASObjRLMidlAr1Type {
    RPAS_OBJRLMIDLAR1_NO_OBJECT = 0;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_13_121_130CM = 13;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_14_131_140CM = 14;
    RPAS_OBJRLMIDLAR1_OBJECT_IN_ZONE_15_141_150CM = 15;
  }
  enum RPASObjRLMidlAr2Type {
    RPAS_OBJRLMIDLAR2_NO_OBJECT = 0;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_13_121_130CM = 13;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_14_131_140CM = 14;
    RPAS_OBJRLMIDLAR2_OBJECT_IN_ZONE_15_141_150CM = 15;
  }
  enum RPASObjRLCornrAr1Type {
    RPAS_OBJRLCORNRAR1_NO_OBJECT = 0;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRLCORNRAR1_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRLCORNRAR1_RESERVED = 13;
  }
  enum RPASObjRLCornrAr2Type {
    RPAS_OBJRLCORNRAR2_NO_OBJECT = 0;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_11_101_110CM = 11;
    RPAS_OBJRLCORNRAR2_OBJECT_IN_ZONE_12_111_120CM = 12;
    RPAS_OBJRLCORNRAR2_RESERVED = 13;
  }
  enum RPASWorkStsType {
    RPAS_WORKSTS_DISABLE = 0;
    RPAS_WORKSTS_ENABLE = 1;
    RPAS_WORKSTS_ACTIVE = 2;
    RPAS_WORKSTS_FAILED = 3;
  }
  enum RPASSoundIndcnType {
    RPAS_SOUNDINDCN_TONE_ZERO_NO_WARNING = 0;
    RPAS_SOUNDINDCN_TONE_ONE_LONG_BEEP = 1;
    RPAS_SOUNDINDCN_TONE_TWO_1_5_HZ = 2;
    RPAS_SOUNDINDCN_TONE_THREE_3HZ = 3;
  }
  enum MEBWorkStsType {
    MEBWORKSTS_OFF = 0;
    MEBWORKSTS_ENABLE = 1;
    MEBWORKSTS_ACTIVE = 2;
    MEBWORKSTS_FAILED = 3;
  }
  enum FPASObjFLCornrAr1Type {
    FPAS_OBJFLCORNRAR1_NO_OBJECT = 0;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFLCORNRAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFLCORNRAR1_RESERVED = 7;
  }
  enum FPASObjFLCornrAr2Type {
    FPAS_OBJFLCORNRAR2_NO_OBJECT = 0;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFLCORNRAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFLCORNRAR2_RESERVED = 7;
  }
  enum FPASObjFLMidlAr1Type {
    FPAS_OBJFLMIDLAR1_NO_OBJECT = 0;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    FPAS_OBJFLMIDLAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    FPAS_OBJFLMIDLAR1_RESERVED = 11;
  }
  enum FPASObjFLMidlAr2Type {
    FPAS_OBJFLMIDLAR2_NO_OBJECT = 0;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    FPAS_OBJFLMIDLAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    FPAS_OBJFLMIDLAR2_RESERVED = 11;
  }
  enum FPASObjFRMidlAr1Type {
    FPAS_OBJFRMIDLAR1_NO_OBJECT = 0;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_7_61_70CM = 7;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_8_71_80CM = 8;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_9_81_90CM = 9;
    FPAS_OBJFRMIDLAR1_OBJECT_IN_ZONE_10_91_100CM = 10;
    FPAS_OBJFRMIDLAR1_RESERVED = 11;
  }
  enum FPASObjFRMidlAr2Type {
    FPAS_OBJFRMIDLAR2_NO_OBJECT = 0;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_7_61_70CM = 7;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_8_71_80CM = 8;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_9_81_90CM = 9;
    FPAS_OBJFRMIDLAR2_OBJECT_IN_ZONE_10_91_100CM = 10;
    FPAS_OBJFRMIDLAR2_RESERVED = 11;
  }
  enum FPASObjFRCornrAr1Type {
    FPAS_OBJFRCORNRAR1_NO_OBJECT = 0;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFRCORNRAR1_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFRCORNRAR1_RESERVED = 7;
  }
  enum FPASObjFRCornrAr2Type {
    FPAS_OBJFRCORNRAR2_NO_OBJECT = 0;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_1_0_10CM = 1;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_2_11_20CM = 2;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_3_21_30CM = 3;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_4_31_40CM = 4;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_5_41_50CM = 5;
    FPAS_OBJFRCORNRAR2_OBJECT_IN_ZONE_6_51_60CM = 6;
    FPAS_OBJFRCORNRAR2_RESERVED = 7;
  }
  enum FPASWorkStsType {
    FPAS_WORKSTS_DISABLE = 0;
    FPAS_WORKSTS_ENABLE = 1;
    FPAS_WORKSTS_ACTIVE = 2;
    FPAS_WORKSTS_FAILED = 3;
  }
  enum FPASSoundIndcnType {
    FPAS_SOUNDINDCN_TONE_ZERO_NO_WARNING = 0;
    FPAS_SOUNDINDCN_TONE_ONE_LONG_BEEP = 1;
    FPAS_SOUNDINDCN_TONE_TWO_1_5_HZ = 2;
    FPAS_SOUNDINDCN_TONE_THREE_3HZ = 3;
  }
  enum FPASNearBrrdstnType {
    FPAS_NEARBRRDSTN_VALID_VALUE = 0;
    FPAS_NEARBRRDSTN_RESERVED = 101;
    FPAS_NEARBRRDSTN_NO_DISPLAY = 127;
  }
  enum APAMenuDispCtrlCmdType {
    APA_MENUDISPCTRLCMD_NO_DISP_AVM_MENU = 0;
    APA_MENUDISPCTRLCMD_PARK_IN_INDICATE_MENU = 1;
    APA_MENUDISPCTRLCMD_PARK_IN_SELECT_SLOT_MENU = 2;
    APA_MENUDISPCTRLCMD_PARK_IN_MODE_SELECT_MENU = 3;
    APA_MENUDISPCTRLCMD_PARK_IN_PROCESS = 4;
    APA_MENUDISPCTRLCMD_REMOTE_PARK_IN_PROCESS = 5;
    APA_MENUDISPCTRLCMD_PARK_OUT_CONFIRM_MENU = 6;
    APA_MENUDISPCTRLCMD_PARK_OUT_PROCESS = 7;
    APA_MENUDISPCTRLCMD_PARK_IN_INDICATE_MENU_NORPA = 8;
    APA_MENUDISPCTRLCMD_PARK_IN_SELECT_SLOT_MENU_NORPA = 9;
    APA_MENUDISPCTRLCMD_PARK_IN_MODE_SELECT_MENU_NORPA = 10;
    APA_MENUDISPCTRLCMD_PARK_OUT_INDICATE_MENU = 11;
    APA_MENUDISPCTRLCMD_USER_DEFINED_PARKING_SLOT_MENU = 12;
    APA_MENUDISPCTRLCMD_PARK_IN_REMOTESEARCHINGSLOT_MENU = 13;
    APA_MENUDISPCTRLCMD_PAVP_SCREEN_SECTION_A = 14;
    APA_MENUDISPCTRLCMD_REVERSED = 15;
  }
  enum APAFuncStsType {
    APA_FUNCSTS_APA_FUNCTION_STANDBY = 0;
    APA_FUNCSTS_APA_FUNCTION_ACTIVE = 1;
    APA_FUNCSTS_APA_FUNCTION_DISABLE = 2;
    APA_FUNCSTS_APA_COMING_SOON = 3;
  }
  enum P2PFuncStsType {
    P2P_FUNCSTS_P2P_FUNCTIONSTANDBY = 0;
    P2P_FUNCSTS_P2P_FUNCTION_ACTIVE = 1;
    P2P_FUNCSTS_P2P_FUNCTION_DISABLE = 2;
    P2P_FUNCSTS_P2P_COMING_SOON = 3;
  }
  enum RADSFuncStsType {
    RADS_FUNCSTS_REVERSE_ASIST_FUNCTION_STANDBY = 0;
    RADS_FUNCSTS_REVERSE_ASIST_FUNCTION_ACTIVE = 1;
    RADS_FUNCSTS_REVERSE_ASIST_FUNCTION_DISABLE = 2;
    RADS_FUNCSTS_RADS_COMING_SOON = 3;
  }
  enum FADSFuncStsType {
    FADS_FUNCSTS_FADS_FUNCTION_STANDBY = 0;
    FADS_FUNCSTS_FADS_FUNCTION_ACTIVE = 1;
    FADS_FUNCSTS_FADS_FUNCTION_DISABLE = 2;
    FADS_FUNCSTS_FADS_COMING_SOON = 3;
  }
  enum HAPTextIconDispCtrlType {
    HAP_TEXTICONDISPCTRL_NO_DISPLAY = 0;
    HAP_TEXTICONDISPCTRL_DISPLAY_ACCORDING_TO_TEXT_INFO = 1;
    HAP_TEXTICONDISPCTRL_DISPLAY_PARKING_MODE = 2;
    HAP_TEXTICONDISPCTRL_RESERVED = 3;
  }
  enum OrvmCtrlCmdType {
    ORVMCTRLCMD_NO_REQUEST = 0;
    ORVMCTRLCMD_REQUEST_TO_FOLD = 1;
    ORVMCTRLCMD_REQUEST_TO_UNFOLD = 2;
    ORVMCTRLCMD_RESERVED = 3;
  }
  enum RmtPrkgFinishReqType {
    RMTPRKGFINISHREQ_NO_REQUEST = 0;
    RMTPRKGFINISHREQ_PARK_IN_FINISH = 1;
    RMTPRKGFINISHREQ_PARK_OUT_FINISH = 2;
    RMTPRKGFINISHREQ_PARK_ERROR_EXIT = 3;
  }
  enum RmtPrkRadShieldStsType {
    RMTPRKRADSHIELDSTS_NO_SHIELDING = 0;
    RMTPRKRADSHIELDSTS_BILATERAL_ULTRASONIC_RADAR_SHIELDING = 1;
    RMTPRKRADSHIELDSTS_ALL_ULTRASONIC_RADAR_SHIELDING = 2;
    RMTPRKRADSHIELDSTS_RESERVED = 3;
  }
  // [] [0|150] [initial_value:255]
  optional RPASNearBrrdstnType rpas_near_brrdstn = 1;
  // [] [0|127] [initial_value:127]
  optional HAVPProcRaType havp_proc_ra = 2;
  // [] [0|127] [initial_value:127]
  optional P2PProcBarType p2_p_proc_bar = 3;
  // [m] [0|127] [initial_value:126]
  optional double res_tra_distance_of_cur_tra = 4;
  // [] [0|15] [initial_value:0]
  optional APSObjRiSideAr1Type aps_obj_ri_side_ar1 = 5;
  // [] [0|15] [initial_value:0]
  optional APSObjRiSideAr2Type aps_obj_ri_side_ar2 = 6;
  // [] [0|15] [initial_value:0]
  optional APSObjRiSideAr3Type aps_obj_ri_side_ar3 = 7;
  // [] [0|15] [initial_value:0]
  optional APSObjRiSideAr4Type aps_obj_ri_side_ar4 = 8;
  // [] [0|15] [initial_value:0]
  optional APSObjRiSideAr5Type aps_obj_ri_side_ar5 = 9;
  // [] [0|15] [initial_value:0]
  optional APSObjLeSideAr1Type aps_obj_le_side_ar1 = 10;
  // [] [0|15] [initial_value:0]
  optional APSObjLeSideAr2Type aps_obj_le_side_ar2 = 11;
  // [] [0|15] [initial_value:0]
  optional APSObjLeSideAr3Type aps_obj_le_side_ar3 = 12;
  // [] [0|15] [initial_value:0]
  optional APSObjLeSideAr4Type aps_obj_le_side_ar4 = 13;
  // [] [0|15] [initial_value:0]
  optional APSObjLeSideAr5Type aps_obj_le_side_ar5 = 14;
  // [] [0|1] [initial_value:0]
  optional bool aps_fr_side_snsr_fail_sts = 15;
  // [] [0|1] [initial_value:0]
  optional bool aps_fl_side_snsr_fail_sts = 16;
  // [] [0|1] [initial_value:0]
  optional bool aps_rr_side_snsr_fail_sts = 17;
  // [] [0|1] [initial_value:0]
  optional bool aps_rl_side_snsr_fail_sts = 18;
  // [] [0|3] [initial_value:0]
  optional SDWSoundIndcnType sdw_sound_indcn = 19;
  // [] [0|1] [initial_value:0]
  optional bool sdw_active_sts = 20;
  // [] [0|15] [initial_value:0]
  optional RPASObjRRCornrAr1Type rpas_obj_rr_cornr_ar1 = 21;
  // [] [0|15] [initial_value:0]
  optional RPASObjRRCornrAr2Type rpas_obj_rr_cornr_ar2 = 22;
  // [] [0|15] [initial_value:0]
  optional RPASObjRRMidlAr1Type rpas_obj_rr_midl_ar1 = 23;
  // [] [0|15] [initial_value:0]
  optional RPASObjRRMidlAr2Type rpas_obj_rr_midl_ar2 = 24;
  // [] [0|15] [initial_value:0]
  optional RPASObjRLMidlAr1Type rpas_obj_rl_midl_ar1 = 25;
  // [] [0|15] [initial_value:0]
  optional RPASObjRLMidlAr2Type rpas_obj_rl_midl_ar2 = 26;
  // [] [0|15] [initial_value:0]
  optional RPASObjRLCornrAr1Type rpas_obj_rl_cornr_ar1 = 27;
  // [] [0|15] [initial_value:0]
  optional RPASObjRLCornrAr2Type rpas_obj_rl_cornr_ar2 = 28;
  // [] [0|1] [initial_value:0]
  optional bool rpas_rl_cornr_snsr_fail_sts = 29;
  // [] [0|1] [initial_value:0]
  optional bool rpas_rl_middl_snsr_fail_sts = 30;
  // [] [0|1] [initial_value:0]
  optional bool rpas_rr_middl_snsr_fail_sts = 31;
  // [] [0|1] [initial_value:0]
  optional bool rpas_rr_cornr_snsr_fail_sts = 32;
  // [] [0|3] [initial_value:0]
  optional RPASWorkStsType rpas_work_sts = 33;
  // [] [0|3] [initial_value:0]
  optional RPASSoundIndcnType rpas_sound_indcn = 34;
  // [] [0|3] [initial_value:0]
  optional MEBWorkStsType meb_work_sts = 35;
  // [] [0|1] [initial_value:0]
  optional bool meb_enable_sts = 36;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool auto_prk_out_swt_sts = 37;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool apa_bacgrd_work_sts = 38;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool cam_folw_swt_sts = 39;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool rcta_disp_swt_sts = 40;
  // [] [0|15] [initial_value:0]
  optional FPASObjFLCornrAr1Type fpas_obj_fl_cornr_ar1 = 41;
  // [] [0|15] [initial_value:0]
  optional FPASObjFLCornrAr2Type fpas_obj_fl_cornr_ar2 = 42;
  // [] [0|15] [initial_value:0]
  optional FPASObjFLMidlAr1Type fpas_obj_fl_midl_ar1 = 43;
  // [] [0|15] [initial_value:0]
  optional FPASObjFLMidlAr2Type fpas_obj_fl_midl_ar2 = 44;
  // [] [0|15] [initial_value:0]
  optional FPASObjFRMidlAr1Type fpas_obj_fr_midl_ar1 = 45;
  // [] [0|15] [initial_value:0]
  optional FPASObjFRMidlAr2Type fpas_obj_fr_midl_ar2 = 46;
  // [] [0|15] [initial_value:0]
  optional FPASObjFRCornrAr1Type fpas_obj_fr_cornr_ar1 = 47;
  // [] [0|15] [initial_value:0]
  optional FPASObjFRCornrAr2Type fpas_obj_fr_cornr_ar2 = 48;
  // [] [0|1] [initial_value:0]
  optional bool fpas_fl_cornr_snsr_fail_sts = 49;
  // [] [0|1] [initial_value:0]
  optional bool fpas_fl_middl_snsr_fail_sts = 50;
  // [] [0|1] [initial_value:0]
  optional bool fpas_fr_middl_snsr_fail_sts = 51;
  // [] [0|1] [initial_value:0]
  optional bool fpas_fr_cornr_snsr_fail_sts = 52;
  // [] [0|3] [initial_value:0]
  optional FPASWorkStsType fpas_work_sts = 53;
  // [] [0|1] [initial_value:0]
  optional bool fpas_disp_cmd = 54;
  // [] [0|1] [initial_value:0]
  optional bool fpas_auto_mod_sts = 55;
  // [] [0|3] [initial_value:0]
  optional FPASSoundIndcnType fpas_sound_indcn = 56;
  // [] [0|100] [initial_value:127]
  optional FPASNearBrrdstnType fpas_near_brrdstn = 57;
  // [] [0|15] [initial_value:0]
  optional APAMenuDispCtrlCmdType apa_menu_disp_ctrl_cmd = 58;
  // [] [0|3] [initial_value:0]
  optional APAFuncStsType apa_func_sts = 59;
  // [] [0|3] [initial_value:0]
  optional P2PFuncStsType p2_p_func_sts = 60;
  // [] [0|3] [initial_value:0]
  optional RADSFuncStsType rads_func_sts = 61;
  // [] [0|3] [initial_value:0]
  optional FADSFuncStsType fads_func_sts = 62;
  // [] [0|3] [initial_value:0]
  optional HAPTextIconDispCtrlType hap_text_icon_disp_ctrl = 63;
  // [] [0|3] [initial_value:0]
  optional OrvmCtrlCmdType orvm_ctrl_cmd = 64;
  // [] [0|3] [initial_value:0]
  optional RmtPrkgFinishReqType rmt_prkg_finish_req = 65;
  // [] [0|1] [initial_value:0]
  optional bool rmt_prkg_sts = 66;
  // [deg] [0|359] [initial_value:0]
  optional int32 aps_coordinate_angle = 67;
  // [cm] [-500|500] [initial_value:0]
  optional double aps_coordinate_x = 68;
  // [cm] [-2000|2000] [initial_value:0]
  optional double aps_coordinate_y = 69;
  // [NoUnit] [0|3] [initial_value:0]
  optional RmtPrkRadShieldStsType rmt_prk_rad_shield_sts = 70;
  // [] [0|1] [initial_value:0]
  optional bool frnt_cam_dirty_sts = 71;
  // [] [0|1] [initial_value:0]
  optional bool le_cam_dirty_sts = 72;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool rear_cam_dirty_sts = 73;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool ri_cam_dirty_sts = 74;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool dvr_cam_dirty_sts = 75;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_hap_fd3 = 76;
}

message HAP_FD7_29B {
// Control Message
  enum HAVPLrngDstType {
    HAVP_LRNGDST_VALID_VALUES = 0;
    HAVP_LRNGDST_INVALID = 1001;
    HAVP_LRNGDST_NO_DISPLAY = 1023;
  }
  enum HAVPHUTTextDispType {
    HAVP_HUT_TEXTDISP_IDLE = 0;
    HAVP_HUT_TEXTDISP_PATH_LIST_SCREEN = 1;
    HAVP_HUT_TEXTDISP_360_CAMERA_FAILURE = 2;
    HAVP_HUT_TEXTDISP_PATH_LEARNING_INPROCESS_SET_STARTING_POINT_AFTER_STOP = 3;
    HAVP_HUT_TEXTDISP_INSUFFICIENT_BRIGHTNESS_RESET_STARTING_POINT = 4;
    HAVP_HUT_TEXTDISP_TOO_EMPTY_ENVIRONMENT_RESET_STARTING_POINT = 5;
    HAVP_HUT_TEXTDISP_FRONT_CAR_IS_TOO_CLOSE_RESET_STARTING_POINT = 6;
    HAVP_HUT_TEXTDISP_CAMERA_IS_BLOCKED_RESET_STARTING_POINT = 7;
    HAVP_HUT_TEXTDISP_ENVIRONMENT_IS_ABNORMAL_RESET_STARTING_POINT = 8;
    HAVP_HUT_TEXTDISP_PATH_LEARNING_INPROCESS_CONTINUE_DRIVING_TO_ENDING_POINT = 9;
    HAVP_HUT_TEXTDISP_PATH_LEARNING_INPROCESS_SET_ENDING_POINT_OR_CHOOSE_ENDING_LOT_AFTER_STOP = 10;
    HAVP_HUT_TEXTDISP_TOO_HIGH_SPEED_LOWER_SPEED_BELOW_15KM_H = 11;
    HAVP_HUT_TEXTDISP_TOO_HIGH_SPEED_PATH_LEARNING_QUIT = 12;
    HAVP_HUT_TEXTDISP_DISTANCE_LIMITED_SET_ENDING_POINT_AFTER_STOP = 13;
    HAVP_HUT_TEXTDISP_DISTANCE_EXCEED_PATH_LEARNING_QUIT = 14;
    HAVP_HUT_TEXTDISP_TOO_LONG_TIME_PATH_LEARNING_QUIT = 15;
    HAVP_HUT_TEXTDISP_INSUFFICIENT_BRIGHTNESS_RESET_ENDING_POINT = 16;
    HAVP_HUT_TEXTDISP_TOO_EMPTY_ENVIRONMENT_RESET_ENDING_POINT = 17;
    HAVP_HUT_TEXTDISP_FRONT_CAR_IS_TOO_CLOSE_RESET_ENDING_POINT = 18;
    HAVP_HUT_TEXTDISP_CAMERA_IS_BLOCKED_RESET_ENDING_POINT = 19;
    HAVP_HUT_TEXTDISP_ENVIRONMENT_IS_ABNORMAL_RESET_ENDING_POINT = 20;
    HAVP_HUT_TEXTDISP_DVR_FAILURE = 21;
    HAVP_HUT_TEXTDISP_RELATED_SYSTEM_FAILURE_FUNCTION_QUIT = 22;
    HAVP_HUT_TEXTDISP_PLEASE_WAIT_PATH_GENERATING = 23;
    HAVP_HUT_TEXTDISP_WHETHER_AUTO_PARKING_IN = 24;
    HAVP_HUT_TEXTDISP_SYSTEM_ERROR_FUNCTION_QUIT = 25;
    HAVP_HUT_TEXTDISP_PATH_LEARNING_SUCCEED = 26;
    HAVP_HUT_TEXTDISP_INSUFFICIENT_BRIGHTNESS_PATH_LEARNING_FAILED = 27;
    HAVP_HUT_TEXTDISP_TOO_EMPTY_ENVIRONMENT_PATH_LEARNING_FAILED = 28;
    HAVP_HUT_TEXTDISP_FRONT_CAR_IS_TOO_CLOSE_PATH_LEARNING_FAILED = 29;
    HAVP_HUT_TEXTDISP_CAMERA_IS_BLOCKED_PATH_LEARNING_FAILED = 30;
    HAVP_HUT_TEXTDISP_ENVIRONMENT_IS_ABNORMAL_PATH_LEARNING_FAILED = 31;
    HAVP_HUT_TEXTDISP_PATH_MATCHING_DRIVING_TO_LEARNED_PATH_SCOPE = 32;
    HAVP_HUT_TEXTDISP_PATH_MATCHING_FAILURE = 33;
    HAVP_HUT_TEXTDISP_PATH_MATCHED_SUCCEED_STOP_AND_APPLY_P_EPB = 34;
    HAVP_HUT_TEXTDISP_PLEASE_SELECT_HUT_OR_MOBILE_TO_ACTIVATE_HAVP = 35;
    HAVP_HUT_TEXTDISP_SHOW_HAVP_INPROCESS_PICTURE = 36;
    HAVP_HUT_TEXTDISP_PLEASE_SELECT_END_PARKING_SLOT = 37;
    HAVP_HUT_TEXTDISP_PLEASE_DROP_OFF_THE_CAR_AND_ACTIVE_HAVP_ON_MOBILE = 38;
    HAVP_HUT_TEXTDISP_PLEASE_SELECT_WHETHER_TO_ACTIVATE_HAVP = 39;
    HAVP_HUT_TEXTDISP_RESERVED = 40;
  }
  enum HAVPScFailinfoType {
    HAVP_SCFAIL_INFO_IDLE = 0;
    HAVP_SCFAIL_INFO_FRONT_WIDE_CAMERA_FAULT = 1;
    HAVP_SCFAIL_INFO_AROUND_VIEW_CAMERA_FAULT = 2;
    HAVP_SCFAIL_INFO_ULTRASONIC_FAULT = 3;
    HAVP_SCFAIL_INFO_CONTROLLER_HW_FAULT = 4;
    HAVP_SCFAIL_INFO_CONTROLLER_SW_FAULT = 5;
    HAVP_SCFAIL_INFO_RESERVED = 6;
  }
  enum PAVPScFailinfoType {
    PAVP_SCFAIL_INFO_IDLE = 0;
    PAVP_SCFAIL_INFO_CAMERA_FAULT = 1;
    PAVP_SCFAIL_INFO_ULTRASONIC_FAULT = 2;
    PAVP_SCFAIL_INFO_AVP_HW_FAULT = 3;
    PAVP_SCFAIL_INFO_AVP_SW_FAULT = 4;
    PAVP_SCFAIL_INFO_AVP_ETH1_CONNECTION_FAULT = 5;
    PAVP_SCFAIL_INFO_AVP_ETH2_CONNECTION_FAULT = 6;
    PAVP_SCFAIL_INFO_CHASIS_POWERTRAIN_ECU_FAULT = 7;
    PAVP_SCFAIL_INFO_RESERVED = 8;
  }
  // [m] [0|1023] [initial_value:1023]
  optional HAVPLrngDstType havp_lrng_dst = 1;
  // [] [0|63] [initial_value:0]
  optional HAVPHUTTextDispType havp_hut_text_disp = 2;
  // [] [0|31] [initial_value:0]
  optional HAVPScFailinfoType havp_sc_fail_info = 3;
  // [] [0|31] [initial_value:0]
  optional PAVPScFailinfoType pavp_sc_fail_info = 4;
}

message AC1_29D {
// Report Message
  // [\A1\E3C] [-40|86.5] [initial_value:254]
  optional double in_car_temp = 1;
}

message BMS_FD2_29E {
// Report Message
  enum BMSCC2LineStsType {
    BMS_CC2LINESTS_NOT_CONNECT = 0;
    BMS_CC2LINESTS_CONNECT = 1;
    BMS_CC2LINESTS_ERROR = 2;
    BMS_CC2LINESTS_RESERVED = 3;
  }
  // [NoUnit] [0|3] [initial_value:0]
  optional BMSCC2LineStsType bms_cc2_line_sts = 1;
}

message BCM8_29F {
// Report Message
  enum WiprReqFPBoxType {
    WIPRREQ_F_PBOX_WIPER_OFF = 0;
    WIPRREQ_F_PBOX_WIPER_ACTION_ONCE = 1;
    WIPRREQ_F_PBOX_SPEED1_LO = 2;
    WIPRREQ_F_PBOX_SPEED2_HI = 3;
    WIPRREQ_F_PBOX_RESERVED = 4;
    WIPRREQ_F_PBOX_INVALID = 7;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_bcm8 = 1;
  // [] [0|1] [initial_value:0]
  optional bool pas_work_cmd = 2;
  // [] [0|7] [initial_value:0]
  optional WiprReqFPBoxType wipr_req_f_p_box = 3;
  // [] [0|1] [initial_value:0]
  optional bool kl30_b_relay_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool r_view_mirror_sts = 5;
  // [1] [0|1] [initial_value:0]
  optional bool remote_mod_sts = 6;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_bcm8 = 7;
}

message IFC_FD7_2A2 {
// Control Message
  // [] [0|255] [initial_value:0]
  optional int32 rp_sync_id = 1;
  // [] [0|1] [initial_value:0]
  optional bool rp_valid = 2;
  // [] [0|1] [initial_value:0]
  optional bool rp_new_measurment = 3;
  // [m/s] [-10|100] [initial_value:2200]
  optional double rp_used_speed = 4;
  // [m] [0|10] [initial_value:0]
  optional double rp_start_point_distance = 5;
  // [] [0|1] [initial_value:0]
  optional double rp_shock_confidence_r = 6;
  // [] [0|1] [initial_value:0]
  optional bool rp_shock_detected_r = 7;
  // [] [0|1] [initial_value:0]
  optional bool rp_shock_detected_l = 8;
  // [m] [-0.3|0.3] [initial_value:300]
  optional double rp_shock_height_l = 9;
  // [] [0|1] [initial_value:0]
  optional double rp_shock_confidence_l = 10;
  // [m] [0|20] [initial_value:0]
  optional double rp_shock_dist_l = 11;
  // [m] [-0.3|0.3] [initial_value:300]
  optional double rp_shock_height_r = 12;
  // [m] [0|20] [initial_value:0]
  optional double rp_shock_dist_r = 13;
  // [] [0|1200] [initial_value:0]
  optional int32 rp_num_points_per_tube = 14;
}

message ACC_FD2_2AB {
// Control Message
  enum ACCLeTgt02TypType {
    ACC_LETGT_02_TYP_UNKOWN = 0;
    ACC_LETGT_02_TYP_SEDAN = 1;
    ACC_LETGT_02_TYP_TRUCK = 2;
    ACC_LETGT_02_TYP_MOTORCYCLE = 3;
    ACC_LETGT_02_TYP_PEDESTRIAN = 4;
    ACC_LETGT_02_TYP_BICYCLE = 5;
    ACC_LETGT_02_TYP_TRAFFIC_CONE = 6;
    ACC_LETGT_02_TYP_OBSTACLE = 7;
  }
  enum ACCLeTargrt02DetnType {
    ACC_LETARGRT_02_DETN_NOT_DECTECTED = 0;
    ACC_LETARGRT_02_DETN_DECTECTED = 1;
    ACC_LETARGRT_02_DETN_RESERVED = 2;
    ACC_LETARGRT_02_DETN_INVALID = 3;
  }
  enum ACCRiTargrt02DetnType {
    ACC_RITARGRT_02_DETN_NOT_DECTECTED = 0;
    ACC_RITARGRT_02_DETN_DECTECTED = 1;
    ACC_RITARGRT_02_DETN_RESERVED = 2;
    ACC_RITARGRT_02_DETN_INVALID = 3;
  }
  enum ACCRiTgt02TypType {
    ACC_RITGT_02_TYP_UNKOWN = 0;
    ACC_RITGT_02_TYP_SEDAN = 1;
    ACC_RITGT_02_TYP_TRUCK = 2;
    ACC_RITGT_02_TYP_MOTORCYCLE = 3;
    ACC_RITGT_02_TYP_PEDESTRIAN = 4;
    ACC_RITGT_02_TYP_BICYCLE = 5;
    ACC_RITGT_02_TYP_TRAFFIC_CONE = 6;
    ACC_RITGT_02_TYP_OBSTACLE = 7;
  }
  enum ACCLeTgtObjBarDispType {
    ACC_LETGTOBJBARDISP_NO_DISPLAY = 0;
    ACC_LETGTOBJBARDISP_DISTANCE_1_ONE_BAR = 1;
    ACC_LETGTOBJBARDISP_DISTANCE_2_TWO_BARS = 2;
    ACC_LETGTOBJBARDISP_DISTANCE_3_THREE_BARS = 3;
    ACC_LETGTOBJBARDISP_DISTANCE_4_FOUR_BARS = 4;
    ACC_LETGTOBJBARDISP_RESVERED = 5;
  }
  enum ACCRiTgtObjBarDispType {
    ACC_RITGTOBJBARDISP_NO_DISPLAY = 0;
    ACC_RITGTOBJBARDISP_DISTANCE_1_ONE_BAR = 1;
    ACC_RITGTOBJBARDISP_DISTANCE_2_TWO_BARS = 2;
    ACC_RITGTOBJBARDISP_DISTANCE_3_THREE_BARS = 3;
    ACC_RITGTOBJBARDISP_DISTANCE_4_FOUR_BARS = 4;
    ACC_RITGTOBJBARDISP_RESVERED = 5;
  }
  enum HWASnvtySetRespType {
    HWA_SNVTYSET_RESP_NORMAL = 0;
    HWA_SNVTYSET_RESP_HIGH_SENSITIVITY = 1;
    HWA_SNVTYSET_RESP_LOW_SENSITIVITY = 2;
    HWA_SNVTYSET_RESP_RESERVED = 3;
  }
  enum ALCInterSysInfoDispType {
    ALC_INTERSYSINFODISP_NO_DISPLAY = 0;
    ALC_INTERSYSINFODISP_AUTOMATIC_LANE_CHANGE_FUNCTION_FAILURE = 1;
    ALC_INTERSYSINFODISP_RESERVE = 2;
  }
  enum HWAwarningType {
    HWA_WARNING_NO_WARNING = 0;
    HWA_WARNING_WARNING_LEVEL_1 = 1;
    HWA_WARNING_WARNING_LEVEL_2 = 2;
    HWA_WARNING_WARNING_LEVEL_3 = 3;
  }
  enum HWAModDispType {
    HWA_MODDISP_OFF = 0;
    HWA_MODDISP_SAFSTOP = 1;
    HWA_MODDISP_PASSIVE = 2;
    HWA_MODDISP_READY = 3;
    HWA_MODDISP_ACTIVE = 4;
    HWA_MODDISP_OVERRIDE = 5;
    HWA_MODDISP_SLOW_RETREAT = 6;
    HWA_MODDISP_STANDSTILL = 7;
    HWA_MODDISP_LONG_OVERRIDE = 8;
    HWA_MODDISP_DEGRADE = 9;
    HWA_MODDISP_RESERVED = 10;
    HWA_MODDISP_FAULT = 15;
  }
  enum HWAInterSysInfoDispType {
    HWA_INTERSYSINFODISP_NO_DISPLAY = 0;
    HWA_INTERSYSINFODISP_LEFT_OVERTAKING = 1;
    HWA_INTERSYSINFODISP_RIGHT_OVERTAKING = 2;
    HWA_INTERSYSINFODISP_TURN_OFF_THE_TURN_LIGHT = 3;
    HWA_INTERSYSINFODISP_RAINMODE_ACTIVATED = 4;
    HWA_INTERSYSINFODISP_OVERTIME_FOR_LANE_CHANGE = 5;
    HWA_INTERSYSINFODISP_LANE_CHANGE_IS_CANCELLED = 6;
    HWA_INTERSYSINFODISP_RAINMODE_DEACTIVATED = 7;
    HWA_INTERSYSINFODISP_SPEED_IS_TOO_LOW_TO_SUPPORT_LANE_CHANGE = 8;
    HWA_INTERSYSINFODISP_HWA_NEED_TO_RESTART_THE_ENGINE = 9;
    HWA_INTERSYSINFODISP_TAKE_OVER_REQUEST = 10;
    HWA_INTERSYSINFODISP_PLEASE_KEEP_ATTENTION = 11;
    HWA_INTERSYSINFODISP_TO_CROSS_THE_TUNNEL_PLEASE_TAKE_OVER_IN_TIME = 12;
    HWA_INTERSYSINFODISP_HAVE_A_BRANCH_ROAD_AHEAD_PLEASE_TAKE_OVER_IN_TIME = 13;
    HWA_INTERSYSINFODISP_SHARP_BEND_AHEAD_PLEASE_TAKE_OVER_IN_TIME = 14;
    HWA_INTERSYSINFODISP_SPEED_IS_TOO_HIGH_TO_SUPPORT_LANE_CHANGE = 15;
  }
  enum HWALaneChangeReqType {
    HWA_LANECHANGEREQ_NO_DISPLAY = 0;
    HWA_LANECHANGEREQ_LEFT_SATISFIED = 1;
    HWA_LANECHANGEREQ_RIGHT_SATISFIED = 2;
    HWA_LANECHANGEREQ_LEFT_NOT_SATISFIED = 3;
    HWA_LANECHANGEREQ_RIGHT_NOT_SATISFIED = 4;
    HWA_LANECHANGEREQ_LEFT_SOLID_LINE = 5;
    HWA_LANECHANGEREQ_RIGHT_SOLID_LINE = 6;
    HWA_LANECHANGEREQ_RESERVED = 7;
  }
  enum HWALaneChangeType {
    HWA_LANECHANGE_NO_DISPLAY = 0;
    HWA_LANECHANGE_LEFT = 1;
    HWA_LANECHANGE_RIGHT = 2;
    HWA_LANECHANGE_RETURN = 3;
  }
  enum ADASDriverInloopMonitorType {
    ADAS_DRIVERINLOOP_MONITOR_NO_DISPLAY = 0;
    ADAS_DRIVERINLOOP_MONITOR_HOD_LEVEL_1 = 1;
    ADAS_DRIVERINLOOP_MONITOR_HOD_LEVEL_2 = 2;
    ADAS_DRIVERINLOOP_MONITOR_DAM_WARNING = 3;
    ADAS_DRIVERINLOOP_MONITOR_RESVERED = 4;
  }
  enum HAVPRecReqType {
    HAVP_RECREQ_OFF = 0;
    HAVP_RECREQ_EVENT_TYPE_APA = 1;
    HAVP_RECREQ_EVENT_TYPE_SVP = 2;
    HAVP_RECREQ_EVENT_TYPE_HAVP = 3;
    HAVP_RECREQ_EVENT_TYPE_RPA = 4;
    HAVP_RECREQ_EVENT_TYPE_MEB = 5;
    HAVP_RECREQ_RESERVED = 6;
  }
  enum DeviateSpdPreRespType {
    DEVIATESPDPRERESP_INITIAL_VALUE = 0;
    DEVIATESPDPRERESP_VALID = 1;
    DEVIATESPDPRERESP_INVALID = 63;
  }
  enum ACCInterSysInfoDispType {
    ACC_INTERSYSINFODISP_NO_DISPLAY = 0;
    ACC_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_ACC_DUE_TO_SPEED_NOT_ACHIVE = 1;
    ACC_INTERSYSINFODISP_ACC_ACTIVE_CONTROL_IS_CANCELLED = 2;
    ACC_INTERSYSINFODISP_RESVERED = 3;
    ACC_INTERSYSINFODISP_ACC_CLOSE_DUE_TO_ASL_OPEN = 4;
    ACC_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_ACC_DUE_TO_OTHER_LIMITATION_EXCEPT_SPEED_NOT_ACHIVE = 5;
    ACC_INTERSYSINFODISP_ASL_CLOSE_DUE_TO_ACC_OPEN = 6;
    ACC_INTERSYSINFODISP_ACC_TEMPORARY_UNAVAILABLE = 7;
    ACC_INTERSYSINFODISP_ACC_IS_ACTIVE = 8;
    ACC_INTERSYSINFODISP_BLINDNESS = 9;
    ACC_INTERSYSINFODISP_ACC_FORBIDDEN_DUE_TO_ASL_ACTIVE = 10;
    ACC_INTERSYSINFODISP_ACC_SETSPEED_HAS_BEEN_CHANGED_TO_ISL = 11;
    ACC_INTERSYSINFODISP_SETSPEED_REACH_UPPER_LIMIT = 12;
    ACC_INTERSYSINFODISP_SETSPEDD_REACH_LOWER_LIMIT = 13;
    ACC_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_TJA_ICA_DUE_TO_DMS_UNA = 14;
    ACC_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_ACC_DUE_TO_LOWPOWER = 15;
    ACC_INTERSYSINFODISP_SENSOR_OCCLUSION = 16;
    ACC_INTERSYSINFODISP_HIGH_SPEED = 17;
    ACC_INTERSYSINFODISP_CLOSE_PARK = 18;
    ACC_INTERSYSINFODISP_BAD_WEATHER = 19;
    ACC_INTERSYSINFODISP_SWITCHING_ACC = 20;
    ACC_INTERSYSINFODISP_LIDAR_OCCLUSION = 21;
  }
  enum ACCModDispType {
    ACC_MODDISP_OFF_MODE = 0;
    ACC_MODDISP_PASSIVE_MODE = 1;
    ACC_MODDISP_STANDBY_MODE = 2;
    ACC_MODDISP_ACTIVE_CONTROL_MODE = 3;
    ACC_MODDISP_BRAKE_ONLY_MODE = 4;
    ACC_MODDISP_OVERRIDE = 5;
    ACC_MODDISP_RESERVED = 6;
    ACC_MODDISP_ACC_AND_PEBS_FAILURE_MODE = 7;
    ACC_MODDISP_ACC_FAILURE_MODE = 8;
    ACC_MODDISP_RESERVED_9 = 9;
  }
  enum ACCFctStsType {
    ACC_FCTSTS_FUCTION_NOT_AVAILABLE = 0;
    ACC_FCTSTS_FUCTION_AVAILABLE = 1;
    ACC_FCTSTS_PERFORMANCE_DEGRADATION = 2;
    ACC_FCTSTS_RESERVED = 3;
  }
  enum ACCTgtObjBarDispType {
    ACC_TGTOBJBARDISP_NO_DISPLAY = 0;
    ACC_TGTOBJBARDISP_DISTANCE_1_ONE_BAR = 1;
    ACC_TGTOBJBARDISP_DISTANCE_2_TWO_BARS = 2;
    ACC_TGTOBJBARDISP_DISTANCE_3_THREE_BARS = 3;
    ACC_TGTOBJBARDISP_DISTANCE_4_FOUR_BARS = 4;
    ACC_TGTOBJBARDISP_RESVERED = 5;
  }
  enum ACCTimeGapSetType {
    ACC_TIMEGAPSET_NO_DISPLAY = 0;
    ACC_TIMEGAPSET_TIMEGAP_1_1S = 1;
    ACC_TIMEGAPSET_TIMEGAP_2_1_4S = 2;
    ACC_TIMEGAPSET_TIMEGAP_3_1_8S = 3;
    ACC_TIMEGAPSET_TIMEGAP_4_2_1S = 4;
    ACC_TIMEGAPSET_RESERVED = 5;
  }
  enum LongctrlHazActvType {
    LONGCTRL_HAZACTV_NOT_ACTIVE = 0;
    LONGCTRL_HAZACTV_AEB_ACTIVE = 1;
    LONGCTRL_HAZACTV_SAFESTOP_ACTIVE = 2;
    LONGCTRL_HAZACTV_RESERVED = 3;
  }
  enum TJAACCSelStsType {
    TJA_ACC_SELSTS_OFF = 0;
    TJA_ACC_SELSTS_ACC_SELECTED = 1;
    TJA_ACC_SELSTS_TJA_ICA_SELECTED = 2;
    TJA_ACC_SELSTS_OFF_SELECTED = 3;
    TJA_ACC_SELSTS_ACC_ON = 4;
    TJA_ACC_SELSTS_TJA_ICA_ON = 5;
    TJA_ACC_SELSTS_RESERVED = 6;
  }
  enum TJAICAModDispType {
    TJA_ICA_MODDISP_OFF_MODE = 0;
    TJA_ICA_MODDISP_PASSIVE_MODE = 1;
    TJA_ICA_MODDISP_ACTIVE_MODE = 2;
    TJA_ICA_MODDISP_READY_MODE = 3;
    TJA_ICA_MODDISP_FAILURE = 4;
    TJA_ICA_MODDISP_RECOVERABLE_MODE = 5;
    TJA_ICA_MODDISP_RESERVED = 6;
  }
  enum TJAICAInterSysInfoDispType {
    TJA_ICA_INTERSYSINFODISP_NO_DISPLAY = 0;
    TJA_ICA_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_TJA_ICA_DUE_TO_OTHER_LIMITATION = 1;
    TJA_ICA_INTERSYSINFODISP_TJA_ICA_ACTIVE_CONTROL_IS_CANCELLED = 2;
    TJA_ICA_INTERSYSINFODISP_TJA_ICA_TEMPORARILY_UNAVAILABLE = 3;
    TJA_ICA_INTERSYSINFODISP_CURRENT_IGN_CYCLE_FUNCTION_INHIBIT = 4;
    TJA_ICA_INTERSYSINFODISP_TJA_IS_ACTIVE = 5;
    TJA_ICA_INTERSYSINFODISP_RESTART_OR_P_GEAR_TO_ACTIVATE = 6;
    TJA_ICA_INTERSYSINFODISP_TJA_IS_RECOVERED = 7;
  }
  enum ISLInterSysInfoDispType {
    ISL_INTERSYSINFODISP_NO_DISPLAY = 0;
    ISL_INTERSYSINFODISP_SPEED_LIMIT_CONFIRM_REQUEST = 1;
    ISL_INTERSYSINFODISP_SPEED_LIMIT_TAKE_OVER_REQUEST = 2;
    ISL_INTERSYSINFODISP_REDUCE_SPEED_ON_A_CURVE = 3;
  }
  enum CruChaModDispType {
    CRUCHA_MODDISP_OFF_MODE = 0;
    CRUCHA_MODDISP_IDLE_MODE = 1;
    CRUCHA_MODDISP_READY_MODE = 2;
    CRUCHA_MODDISP_ACTIVE = 3;
    CRUCHA_MODDISP_RESERVED = 4;
    CRUCHA_MODDISP_WARNING_LEVEL_1 = 5;
    CRUCHA_MODDISP_WARNING_LEVEL_2 = 6;
    CRUCHA_MODDISP_WARNING_LEVEL_3 = 7;
    CRUCHA_MODDISP_RESERVED_8 = 8;
    CRUCHA_MODDISP_FAILURE = 9;
    CRUCHA_MODDISP_RESERVED_10 = 10;
  }
  enum CruCharInterSysInfoDispType {
    CRUCHAR_INTERSYSINFODISP_NO_DISPLAY = 0;
    CRUCHAR_INTERSYSINFODISP_UNABLE_TO_ACTIVATE_CRUISECHAUFFER_DUE_TO_OTHER_LIMITATION = 1;
    CRUCHAR_INTERSYSINFODISP_CRUISECHAUFFER_ACTIVE_CONTROL_IS_CANCELLED = 2;
    CRUCHAR_INTERSYSINFODISP_CRUISECHAUFFER_TEMPORARILY_UNAVAILABLE = 3;
    CRUCHAR_INTERSYSINFODISP_TAKE_OVER_REQUEST = 4;
    CRUCHAR_INTERSYSINFODISP_CURRENT_IGN_CYCLE_FUNCTION_INHIBIT = 5;
    CRUCHAR_INTERSYSINFODISP_PLEASE_KEEP_ATTENTION = 6;
    CRUCHAR_INTERSYSINFODISP_TO_CROSS_THE_TUNNEL_PLEASE_TAKE_OVER_IN_TIME = 7;
    CRUCHAR_INTERSYSINFODISP_HAVE_A_BRANCH_ROAD_AHEAD_PLEASE_TAKE_OVER_IN_TIME = 8;
    CRUCHAR_INTERSYSINFODISP_HANDS_OFF_WARNING = 9;
    CRUCHAR_INTERSYSINFODISP_RESERVED = 10;
  }
  enum ACCRlvTgtDetnType {
    ACC_RLVTGTDETN_NOT_DECTECTED = 0;
    ACC_RLVTGTDETN_DECTECTED = 1;
    ACC_RLVTGTDETN_RESERVED = 2;
    ACC_RLVTGTDETN_INVALID = 3;
  }
  enum ACCEgoStatusType {
    ACC_EGOSTATUS_NO_DISPLAY = 0;
    ACC_EGOSTATUS_OVERRIDE = 1;
    ACC_EGOSTATUS_DECELERATION = 2;
    ACC_EGOSTATUS_RESERVED = 3;
  }
  enum ACCRlvTgtTypType {
    ACC_RLVTGTTYP_UNKOWN = 0;
    ACC_RLVTGTTYP_SEDAN = 1;
    ACC_RLVTGTTYP_TRUCK = 2;
    ACC_RLVTGTTYP_MOTORCYCLE = 3;
    ACC_RLVTGTTYP_PEDESTRIAN = 4;
    ACC_RLVTGTTYP_BICYCLE = 5;
    ACC_RLVTGTTYP_TRAFFIC_CONE = 6;
    ACC_RLVTGTTYP_OBSTACLE = 7;
  }
  enum ACCPotentialTgtDetnType {
    ACC_POTENTIALTGTDETN_NOT_DECTECTED = 0;
    ACC_POTENTIALTGTDETN_DECTECTED = 1;
    ACC_POTENTIALTGTDETN_RESERVED = 2;
    ACC_POTENTIALTGTDETN_INVALID = 3;
  }
  enum ACCPotentialTgtTypType {
    ACC_POTENTIALTGTTYP_UNKOWN = 0;
    ACC_POTENTIALTGTTYP_SEDAN = 1;
    ACC_POTENTIALTGTTYP_TRUCK = 2;
    ACC_POTENTIALTGTTYP_MOTORCYCLE = 3;
    ACC_POTENTIALTGTTYP_PEDESTRIAN = 4;
    ACC_POTENTIALTGTTYP_BICYCLE = 5;
    ACC_POTENTIALTGTTYP_TRAFFIC_CONE = 6;
    ACC_POTENTIALTGTTYP_OBSTACLE = 7;
  }
  enum RequestdriveoffType {
    REQUEST_DRIVEOFF_NO_REQUEST = 0;
    REQUEST_DRIVEOFF_THE_FRONT_CAR_HAS_STARTED = 1;
    REQUEST_DRIVEOFF_THE_FRONT_CAR_HAS_STARTED_PLEASE_PAY_ATTENTION_TO_THE_ROAD_ENVIRONMENT_AHEAD = 2;
    REQUEST_DRIVEOFF_THE_FRONT_CAR_HAS_STARTED_PLEASE_KEEP_YOUR_ATTENTION = 3;
    REQUEST_DRIVEOFF_THE_FRONT_CAR_HAS_STARTED_PLEASE_TAKE_OVER_THE_CAR = 4;
    REQUEST_DRIVEOFF_PLEASE_PRESSED_THE_ACCELERATOR = 5;
    REQUEST_DRIVEOFF_SLOW_TO_A_STOP = 6;
    REQUEST_DRIVEOFF_SLOW_DOWN_FOLLOWING_CAR = 7;
  }
  enum ACCLeTgtTypType {
    ACC_LETGTTYP_UNKOWN = 0;
    ACC_LETGTTYP_SEDAN = 1;
    ACC_LETGTTYP_TRUCK = 2;
    ACC_LETGTTYP_MOTORCYCLE = 3;
    ACC_LETGTTYP_PEDESTRIAN = 4;
    ACC_LETGTTYP_BICYCLE = 5;
    ACC_LETGTTYP_TRAFFIC_CONE = 6;
    ACC_LETGTTYP_OBSTACLE = 7;
  }
  enum ACCLeTargrtDectionType {
    ACC_LETARGRTDECTION_NOT_DECTECTED = 0;
    ACC_LETARGRTDECTION_DECTECTED = 1;
    ACC_LETARGRTDECTION_RESERVED = 2;
    ACC_LETARGRTDECTION_INVALID = 3;
  }
  enum ACCRiTargrtDetnType {
    ACC_RITARGRTDETN_NOT_DECTECTED = 0;
    ACC_RITARGRTDETN_DECTECTED = 1;
    ACC_RITARGRTDETN_RESERVED = 2;
    ACC_RITARGRTDETN_INVALID = 3;
  }
  enum ACCRiTgtTypType {
    ACC_RITGTTYP_UNKOWN = 0;
    ACC_RITGTTYP_SEDAN = 1;
    ACC_RITGTTYP_TRUCK = 2;
    ACC_RITGTTYP_MOTORCYCLE = 3;
    ACC_RITGTTYP_PEDESTRIAN = 4;
    ACC_RITGTTYP_BICYCLE = 5;
    ACC_RITGTTYP_TRAFFIC_CONE = 6;
    ACC_RITGTTYP_OBSTACLE = 7;
  }
  enum DeviateSpdModSwtRespType {
    DEVIATESPDMODSWTRESP_INITIAL_VALUE = 0;
    DEVIATESPDMODSWTRESP_FIXED_VALUE = 1;
    DEVIATESPDMODSWTRESP_PERCENTAGE = 2;
    DEVIATESPDMODSWTRESP_RESVERED = 3;
  }
  enum DeviateSpdFixdValRespType {
    DEVIATESPDFIXDVALRESP_INITIAL_VALUE = 0;
    DEVIATESPDFIXDVALRESP_VALID = 1;
    DEVIATESPDFIXDVALRESP_INVALID = 63;
  }
  enum HWAFaststSwtRespType {
    HWA_FASTSTSWTRESP_INITIAL_VALUE = 0;
    HWA_FASTSTSWTRESP_OFF = 1;
    HWA_FASTSTSWTRESP_ON = 2;
    HWA_FASTSTSWTRESP_RESVERED = 3;
  }
  enum NPInterSysInfoDispType {
    NP_INTERSYSINFODISP_DEFAULT = 0;
    NP_INTERSYSINFODISP_IN_NOH_GEOFENCE = 1;
    NP_INTERSYSINFODISP_NOH_IS_ACTIVATED = 2;
    NP_INTERSYSINFODISP_PLEASE_CONFIRM_L_LANE_CHANGE = 3;
    NP_INTERSYSINFODISP_PLEASE_CONFIRM_R_LANE_CHANGE = 4;
    NP_INTERSYSINFODISP_TO_OVERTAKE_FROM_LEFT = 5;
    NP_INTERSYSINFODISP_TO_OVERTAKE_FROM_RIGHT = 6;
    NP_INTERSYSINFODISP_LANE_CHANGE_WAITING = 7;
    NP_INTERSYSINFODISP_TO_MERGE_IN_MAINROAD = 8;
    NP_INTERSYSINFODISP_NOH_TO_BE_DEACTIVATED = 9;
    NP_INTERSYSINFODISP_NOH_CANCELLED = 10;
    NP_INTERSYSINFODISP_NOH_DEACTIVATED = 11;
    NP_INTERSYSINFODISP_CRUISE_SPEED_HAS_BEEN_SET_BY_NOH = 12;
    NP_INTERSYSINFODISP_SPEED_IS_TOO_HIGH_TO_SUPPORT_NOH = 13;
    NP_INTERSYSINFODISP_TO_MERGE_IN_FAST_ROUTE = 14;
    NP_INTERSYSINFODISP_TO_MERGE_IN_RAMP = 15;
    NP_INTERSYSINFODISP_RESERVED = 16;
  }
  enum HandsfreeStsType {
    HANDSFREESTS_OFF_MODE = 0;
    HANDSFREESTS_LDLE_MODE = 1;
    HANDSFREESTS_READY_MODE = 2;
    HANDSFREESTS_ACTIVE = 3;
    HANDSFREESTS_WARNINGLEVEL1 = 4;
    HANDSFREESTS_WARNINGLEVEL2 = 5;
    HANDSFREESTS_WARNINGLEVEL3 = 6;
    HANDSFREESTS_RESERVED = 7;
    HANDSFREESTS_FAILURE = 8;
    HANDSFREESTS_RESERVED_9 = 9;
  }
  enum HandsfreeInterSysInfoDispType {
    HANDSFREEINTERSYSINFODISP_NO_DISPLAY = 0;
    HANDSFREEINTERSYSINFODISP_HANDS_FREE_ACTIVE_CONTROL_IS_CANCELLED = 1;
    HANDSFREEINTERSYSINFODISP_IN_HANDS_FREE_GEOFENCE = 2;
    HANDSFREEINTERSYSINFODISP_HANDS_OFF_IS_ACTIVE = 3;
    HANDSFREEINTERSYSINFODISP_RESERVED = 4;
  }
  // [m] [-8|0] [initial_value:0]
  optional double acc_le_tgt_02_dy = 1;
  // [m] [0|150] [initial_value:0]
  optional double acc_le_tgt_02_dx = 2;
  // [] [0|7] [initial_value:0]
  optional ACCLeTgt02TypType acc_le_tgt_02_typ = 3;
  // [] [0|3] [initial_value:0]
  optional ACCLeTargrt02DetnType acc_le_targrt_02_detn = 4;
  // [] [0|3] [initial_value:0]
  optional ACCRiTargrt02DetnType acc_ri_targrt_02_detn = 5;
  // [m] [0|8] [initial_value:0]
  optional double acc_ri_tgt_02_dy = 6;
  // [m] [0|150] [initial_value:0]
  optional double acc_ri_tgt_02_dx = 7;
  // [] [0|7] [initial_value:0]
  optional ACCRiTgt02TypType acc_ri_tgt_02_typ = 8;
  // [] [0|7] [initial_value:0]
  optional ACCLeTgtObjBarDispType acc_le_tgt_obj_bar_disp = 9;
  // [] [0|7] [initial_value:0]
  optional ACCRiTgtObjBarDispType acc_ri_tgt_obj_bar_disp = 10;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc3 = 11;
  // [] [0|1] [initial_value:0]
  optional bool isl_resp = 12;
  // [] [0|3] [initial_value:0]
  optional HWASnvtySetRespType hwa_snvty_set_resp = 13;
  // [] [0|1] [initial_value:0]
  optional bool hwa_resp = 14;
  // [] [0|1] [initial_value:0]
  optional bool alc_resp = 15;
  // [] [0|3] [initial_value:0]
  optional ALCInterSysInfoDispType alc_inter_sys_info_disp = 16;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc3 = 17;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc8 = 18;
  // [] [0|3] [initial_value:0]
  optional HWAwarningType hwa_warning = 19;
  // [] [0|15] [initial_value:0]
  optional HWAModDispType hwa_mod_disp = 20;
  // [] [0|15] [initial_value:0]
  optional HWAInterSysInfoDispType hwa_inter_sys_info_disp = 21;
  // [] [0|7] [initial_value:0]
  optional HWALaneChangeReqType hwa_lane_change_req = 22;
  // [] [0|3] [initial_value:0]
  optional HWALaneChangeType hwa_lane_change = 23;
  // [] [0|7] [initial_value:0]
  optional ADASDriverInloopMonitorType adas_driver_inloop_monitor = 24;
  // description DVR\B9\A4\D7\F7\BF\AA\B9\D8״̬ [NoUnit] [0|7] [initial_value:0]
  optional HAVPRecReqType havp_rec_req = 25;
  // [Perc] [0|63] [initial_value:0]
  optional DeviateSpdPreRespType deviate_spd_pre_resp = 26;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc8 = 27;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc4 = 28;
  // [] [0|1] [initial_value:0]
  optional bool acc_obj_detecte = 29;
  // [] [0|1] [initial_value:0]
  optional bool acc_take_over_req = 30;
  // [] [0|31] [initial_value:0]
  optional ACCInterSysInfoDispType acc_inter_sys_info_disp = 31;
  // [] [0|31] [initial_value:0]
  optional ACCModDispType acc_mod_disp = 32;
  // [] [0|3] [initial_value:0]
  optional ACCFctStsType acc_fct_sts = 33;
  // [] [0|7] [initial_value:0]
  optional ACCTgtObjBarDispType acc_tgt_obj_bar_disp = 34;
  // [] [0|7] [initial_value:0]
  optional ACCTimeGapSetType acc_time_gap_set = 35;
  // [kph/mph] [0|255] [initial_value:255]
  optional int32 acc_spd_set_value = 36;
  // [] [0|3] [initial_value:0]
  optional LongctrlHazActvType longctrl_haz_actv = 37;
  // [] [0|7] [initial_value:0]
  optional TJAACCSelStsType tja_acc_sel_sts = 38;
  // [] [0|7] [initial_value:0]
  optional TJAICAModDispType tja_ica_mod_disp = 39;
  // [] [0|7] [initial_value:0]
  optional TJAICAInterSysInfoDispType tja_ica_inter_sys_info_disp = 40;
  // [] [0|3] [initial_value:0]
  optional ISLInterSysInfoDispType isl_inter_sys_info_disp = 41;
  // [] [0|15] [initial_value:0]
  optional CruChaModDispType cru_cha_mod_disp = 42;
  // [] [0|15] [initial_value:0]
  optional CruCharInterSysInfoDispType cru_char_inter_sys_info_disp = 43;
  // [] [0|1] [initial_value:0]
  optional bool acc_req_avm = 44;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc4 = 45;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc6 = 46;
  // [m] [-8|8] [initial_value:0]
  optional double acc_potential_tgt_dy = 47;
  // [m] [0|150] [initial_value:0]
  optional double acc_potential_tgt_dx = 48;
  // [] [0|3] [initial_value:0]
  optional ACCRlvTgtDetnType acc_rlv_tgt_detn = 49;
  // [] [0|3] [initial_value:0]
  optional ACCEgoStatusType acc_ego_status = 50;
  // [] [0|7] [initial_value:0]
  optional ACCRlvTgtTypType acc_rlv_tgt_typ = 51;
  // [m] [-8|8] [initial_value:0]
  optional double acc_rlv_tgt_dy = 52;
  // [m] [0|150] [initial_value:0]
  optional double acc_rlv_tgt_dx = 53;
  // [] [0|3] [initial_value:0]
  optional ACCPotentialTgtDetnType acc_potential_tgt_detn = 54;
  // [] [0|7] [initial_value:0]
  optional ACCPotentialTgtTypType acc_potential_tgt_typ = 55;
  // [] [0|1] [initial_value:0]
  optional bool intelligent_curve_act = 56;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc6 = 57;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_acc7 = 58;
  // [] [0|7] [initial_value:0]
  optional RequestdriveoffType request_driveoff = 59;
  // [m] [-8|8] [initial_value:0]
  optional double acc_le_tgt_dy = 60;
  // [m] [0|150] [initial_value:0]
  optional double acc_le_tgt_dx = 61;
  // [] [0|7] [initial_value:0]
  optional ACCLeTgtTypType acc_le_tgt_typ = 62;
  // [] [0|3] [initial_value:0]
  optional ACCLeTargrtDectionType acc_le_targrt_dection = 63;
  // [] [0|3] [initial_value:0]
  optional ACCRiTargrtDetnType acc_ri_targrt_detn = 64;
  // [m] [-8|8] [initial_value:0]
  optional double acc_ri_tgt_dy = 65;
  // [m] [0|150] [initial_value:0]
  optional double acc_ri_tgt_dx = 66;
  // [] [0|7] [initial_value:0]
  optional ACCRiTgtTypType acc_ri_tgt_typ = 67;
  // [] [0|1] [initial_value:0]
  optional bool acc_sts = 68;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_acc7 = 69;
  // [] [0|1] [initial_value:0]
  optional bool acc_rlv_tgtfusion = 70;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_rlv_tgt_heading = 71;
  // [] [0|1] [initial_value:0]
  optional bool acc_le_tgtfusion = 72;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_le_tgt_heading = 73;
  // [] [0|1] [initial_value:0]
  optional bool acc_ri_tgtfusion = 74;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_ri_tgt_heading = 75;
  // [] [0|1] [initial_value:0]
  optional bool acc_potential_tgtfusion = 76;
  // [rad] [-1.5|1.5] [initial_value:30]
  optional double acc_potential_tgt_heading = 77;
  // [] [0|1] [initial_value:0]
  optional double target_cutin_pro = 78;
  // [] [0|1] [initial_value:0]
  optional double target_cutout_pro = 79;
  // [NoUnit] [0|3] [initial_value:0]
  optional DeviateSpdModSwtRespType deviate_spd_mod_swt_resp = 80;
  // [NoUnit] [0|63] [initial_value:0]
  optional DeviateSpdFixdValRespType deviate_spd_fixd_val_resp = 81;
  // [NoUnit] [0|3] [initial_value:0]
  optional HWAFaststSwtRespType hwa_fastst_swt_resp = 82;
  // [NoUnit] [0|31] [initial_value:0]
  optional NPInterSysInfoDispType np_inter_sys_info_disp = 83;
  // [NoUnit] [0|15] [initial_value:0]
  optional HandsfreeStsType handsfree_sts = 84;
  // [NoUnit] [0|15] [initial_value:0]
  optional HandsfreeInterSysInfoDispType handsfree_inter_sys_info_disp = 85;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_acc_fd2 = 86;
}

message DRDCM1_2B0 {
// Report Message
  enum DrvRearDoorWinPosnStsPercType {
    DRVREARDOORWINPOSNSTS_PERC_UNKNOWN = 0;
    DRVREARDOORWINPOSNSTS_PERC_CLOSE = 1;
    DRVREARDOORWINPOSNSTS_PERC_OPEN = 2;
    DRVREARDOORWINPOSNSTS_PERC_5_OPEN = 3;
    DRVREARDOORWINPOSNSTS_PERC_10_OPEN = 4;
    DRVREARDOORWINPOSNSTS_PERC_15_OPEN = 5;
    DRVREARDOORWINPOSNSTS_PERC_20_OPEN = 6;
    DRVREARDOORWINPOSNSTS_PERC_25_OPEN = 7;
    DRVREARDOORWINPOSNSTS_PERC_30_OPEN = 8;
    DRVREARDOORWINPOSNSTS_PERC_35_OPEN = 9;
    DRVREARDOORWINPOSNSTS_PERC_40_OPEN = 10;
    DRVREARDOORWINPOSNSTS_PERC_45_OPEN = 11;
    DRVREARDOORWINPOSNSTS_PERC_50_OPEN = 12;
    DRVREARDOORWINPOSNSTS_PERC_55_OPEN = 13;
    DRVREARDOORWINPOSNSTS_PERC_60_OPEN = 14;
    DRVREARDOORWINPOSNSTS_PERC_65_OPEN = 15;
    DRVREARDOORWINPOSNSTS_PERC_70_OPEN = 16;
    DRVREARDOORWINPOSNSTS_PERC_75_OPEN = 17;
    DRVREARDOORWINPOSNSTS_PERC_80_OPEN = 18;
    DRVREARDOORWINPOSNSTS_PERC_85_OPEN = 19;
    DRVREARDOORWINPOSNSTS_PERC_90_OPEN = 20;
    DRVREARDOORWINPOSNSTS_PERC_95_OPEN = 21;
    DRVREARDOORWINPOSNSTS_PERC_RESERVED = 22;
  }
  // [NoUnit] [0|31] [initial_value:0]
  optional DrvRearDoorWinPosnStsPercType drv_rear_door_win_posn_sts_perc = 1;
}

message ACC_FD3_2B4 {
// Control Message
  enum AEBLePedDetnType {
    AEB_LEPEDDETN_NOT_DECTECTED = 0;
    AEB_LEPEDDETN_DECTECTED = 1;
    AEB_LEPEDDETN_RESERVED = 2;
    AEB_LEPEDDETN_INVALID = 3;
  }
  enum AEBLePedTypType {
    AEB_LEPEDTYP_UNKOWN = 0;
    AEB_LEPEDTYP_SEDAN = 1;
    AEB_LEPEDTYP_TRUCK = 2;
    AEB_LEPEDTYP_MOTORCYCLE = 3;
    AEB_LEPEDTYP_PEDESTRIAN = 4;
    AEB_LEPEDTYP_BICYCLE = 5;
    AEB_LEPEDTYP_RESERVED = 6;
    AEB_LEPEDTYP_INVALID = 7;
  }
  enum AEBRiPedDetnType {
    AEB_RIPEDDETN_NOT_DECTECTED = 0;
    AEB_RIPEDDETN_DECTECTED = 1;
    AEB_RIPEDDETN_RESERVED = 2;
    AEB_RIPEDDETN_INVALID = 3;
  }
  enum AEBRiPedTypType {
    AEB_RIPEDTYP_UNKOWN = 0;
    AEB_RIPEDTYP_SEDAN = 1;
    AEB_RIPEDTYP_TRUCK = 2;
    AEB_RIPEDTYP_MOTORCYCLE = 3;
    AEB_RIPEDTYP_PEDESTRIAN = 4;
    AEB_RIPEDTYP_BICYCLE = 5;
    AEB_RIPEDTYP_RESERVED = 6;
    AEB_RIPEDTYP_INVALID = 7;
  }
  enum AEBJAObjStateType {
    AEBJAOBJSTATE_UNDEFINED = 0;
    AEBJAOBJSTATE_PRECEDING = 1;
    AEBJAOBJSTATE_STATIONARY = 2;
    AEBJAOBJSTATE_STOPPED = 3;
    AEBJAOBJSTATE_ONCOMING = 4;
    AEBJAOBJSTATE_RESERVED = 5;
  }
  enum JAPedObjStateType {
    JAPEDOBJSTATE_UNDEFINED = 0;
    JAPEDOBJSTATE_PRECEDING = 1;
    JAPEDOBJSTATE_STATIONARY = 2;
    JAPEDOBJSTATE_STOPPED = 3;
    JAPEDOBJSTATE_ONCOMING = 4;
    JAPEDOBJSTATE_RESERVED = 5;
  }
  enum NOHStsType {
    NOH_STS_OFF = 0;
    NOH_STS_PASSIVE = 1;
    NOH_STS_STANDBY = 2;
    NOH_STS_ACTIVE = 3;
    NOH_STS_OVERRIDE = 4;
    NOH_STS_SLOW_RETREAT = 5;
    NOH_STS_STANDSTILL = 6;
    NOH_STS_INHIBIT = 7;
    NOH_STS_URBAN_ACTIVE = 8;
    NOH_STS_URBAN_OVERRIDE = 9;
    NOH_STS_URBAN_SLOW_RETREAT = 10;
    NOH_STS_URBAN_STANDSTILL = 11;
    NOH_STS_NO_DISPLAY = 12;
    NOH_STS_RESERVED = 13;
    NOH_STS_FAIL = 15;
  }
  enum NOHSysInfoDisplayType {
    NOH_SYSINFODISPLAY_DEFAULT = 0;
    NOH_SYSINFODISPLAY_IN_NOH_GEOFENCE = 1;
    NOH_SYSINFODISPLAY_NOH_IS_ACTIVATED = 2;
    NOH_SYSINFODISPLAY_PLEASE_CONFIRM_L_LANE_CHANGE = 3;
    NOH_SYSINFODISPLAY_PLEASE_CONFIRM_R_LANE_CHANGE = 4;
    NOH_SYSINFODISPLAY_TO_OVERTAKE_FROM_LEFT = 5;
    NOH_SYSINFODISPLAY_TO_OVERTAKE_FROM_RIGHT = 6;
    NOH_SYSINFODISPLAY_LANE_CHANGE_WAITING = 7;
    NOH_SYSINFODISPLAY_TO_MERGE_IN_MAINROAD = 8;
    NOH_SYSINFODISPLAY_NOH_TO_BE_DEACTIVATED = 9;
    NOH_SYSINFODISPLAY_NOH_CANCELLED = 10;
    NOH_SYSINFODISPLAY_NOH_DEACTIVATED = 11;
    NOH_SYSINFODISPLAY_CRUISE_SPEED_HAS_BEEN_SET_BY_NOH = 12;
    NOH_SYSINFODISPLAY_SPEED_IS_TOO_HIGH_TO_SUPPORT_NOH = 13;
    NOH_SYSINFODISPLAY_TO_MERGE_IN_FAST_ROUTE = 14;
    NOH_SYSINFODISPLAY_TO_MERGE_IN_RAMP = 15;
  }
  enum AEBLeCyclistDetnType {
    AEB_LECYCLISTDETN_NOT_DECTECTED = 0;
    AEB_LECYCLISTDETN_DECTECTED = 1;
    AEB_LECYCLISTDETN_RESERVED = 2;
    AEB_LECYCLISTDETN_INVALID = 3;
  }
  enum AEBLeCyclistTypType {
    AEB_LECYCLISTTYP_UNKOWN = 0;
    AEB_LECYCLISTTYP_SEDAN = 1;
    AEB_LECYCLISTTYP_TRUCK = 2;
    AEB_LECYCLISTTYP_MOTORCYCLE = 3;
    AEB_LECYCLISTTYP_PEDESTRIAN = 4;
    AEB_LECYCLISTTYP_BICYCLE = 5;
    AEB_LECYCLISTTYP_RESERVED = 6;
    AEB_LECYCLISTTYP_INVALID = 7;
  }
  enum AEBRiCyclistDetnType {
    AEB_RICYCLISTDETN_NOT_DECTECTED = 0;
    AEB_RICYCLISTDETN_DECTECTED = 1;
    AEB_RICYCLISTDETN_RESERVED = 2;
    AEB_RICYCLISTDETN_INVALID = 3;
  }
  enum AEBRiCyclistTypType {
    AEB_RICYCLISTTYP_UNKOWN = 0;
    AEB_RICYCLISTTYP_SEDAN = 1;
    AEB_RICYCLISTTYP_TRUCK = 2;
    AEB_RICYCLISTTYP_MOTORCYCLE = 3;
    AEB_RICYCLISTTYP_PEDESTRIAN = 4;
    AEB_RICYCLISTTYP_BICYCLE = 5;
    AEB_RICYCLISTTYP_RESERVED = 6;
    AEB_RICYCLISTTYP_INVALID = 7;
  }
  // [] [0|3] [initial_value:0]
  optional AEBLePedDetnType aeb_le_ped_detn = 1;
  // [m] [-8|8] [initial_value:0]
  optional double aeb_le_ped_dy = 2;
  // [] [0|7] [initial_value:0]
  optional AEBLePedTypType aeb_le_ped_typ = 3;
  // [] [0|1] [initial_value:0]
  optional bool aeb_le_ped_collisionprob = 4;
  // [] [0|3] [initial_value:0]
  optional AEBRiPedDetnType aeb_ri_ped_detn = 5;
  // [m] [-8|8] [initial_value:0]
  optional double aeb_ri_ped_dy = 6;
  // [] [0|7] [initial_value:0]
  optional AEBRiPedTypType aeb_ri_ped_typ = 7;
  // [] [0|1] [initial_value:0]
  optional bool aeb_ri_ped_collisionprob = 8;
  // [m] [-8|8] [initial_value:0]
  optional double aeb_le_cyclist_dy = 9;
  // [] [0|1] [initial_value:0]
  optional bool aeb_le_cyclist_collisionprob = 10;
  // [m] [-8|8] [initial_value:0]
  optional double aeb_ri_cyclist_dy = 11;
  // [] [0|1] [initial_value:0]
  optional bool aeb_ri_cyclist_collisionprob = 12;
  // [%] [0|0.984375] [initial_value:0]
  optional double ja_ped_obj_exist = 13;
  // [] [0|255] [initial_value:255]
  optional int32 aebja_obj_id = 14;
  // [%] [0|0.984375] [initial_value:0]
  optional double aebja_obj_exist = 15;
  // [] [0|255] [initial_value:255]
  optional int32 ja_ped_obj_id = 16;
  // [] [0|1] [initial_value:0]
  optional bool aebja_obj_video_cfmd = 17;
  // [m/s] [-128|127] [initial_value:0]
  optional double aebja_obj_vx = 18;
  // [m] [0|255.9375] [initial_value:0]
  optional double aebja_obj_dx = 19;
  // [] [0|1] [initial_value:0]
  optional bool ja_ped_obj_video_cfmd = 20;
  // [] [0|7] [initial_value:0]
  optional AEBJAObjStateType aebja_obj_state = 21;
  // [] [0|65535] [initial_value:0]
  optional double aebja_obj_cnt_alive = 22;
  // [m] [-128|127.875] [initial_value:0]
  optional double aebja_obj_dy = 23;
  // [] [0|7] [initial_value:0]
  optional JAPedObjStateType ja_ped_obj_state = 24;
  // [%] [0|0.96875] [initial_value:0]
  optional double aebja_obj_obstcl = 25;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double aebja_obj_vy = 26;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double aebja_obj_ax = 27;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double aebja_obj_ay = 28;
  // [%] [0|0.96875] [initial_value:0]
  optional double ja_ped_obj_obstcl = 29;
  // [m] [-128|127.875] [initial_value:0]
  optional double ja_ped_obj_dy = 30;
  // [m] [0|255.9375] [initial_value:0]
  optional double ja_ped_obj_dx = 31;
  // [s] [0|6.3] [initial_value:0]
  optional double fcwaeb_jattc = 32;
  // [m/s] [-128|127] [initial_value:0]
  optional double ja_ped_obj_vx = 33;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double ja_ped_obj_vy = 34;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double ja_ped_obj_ay = 35;
  // [] [0|65535] [initial_value:0]
  optional double ja_ped_obj_cnt_alive = 36;
  // [s] [0|6.3] [initial_value:0]
  optional double fcwaeb_long_ttc = 37;
  // [s] [0|6.3] [initial_value:0]
  optional double fcwaeb_crossing_ttc = 38;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double ja_ped_obj_ax = 39;
  // [] [0|15] [initial_value:0]
  optional NOHStsType noh_sts = 40;
  // [] [0|1] [initial_value:0]
  optional bool noh_swt_resp = 41;
  // [] [0|1] [initial_value:0]
  optional bool noh_act_resp = 42;
  // [] [0|15] [initial_value:0]
  optional NOHSysInfoDisplayType noh_sys_info_display = 43;
  // [m] [0|4095] [initial_value:0]
  optional int32 noh_dist_to_exit = 44;
  // [m] [0|150] [initial_value:0]
  optional double aeb_le_ped_dx = 45;
  // [m] [0|150] [initial_value:0]
  optional double aeb_ri_ped_dx = 46;
  // [] [0|3] [initial_value:0]
  optional AEBLeCyclistDetnType aeb_le_cyclist_detn = 47;
  // [m] [0|150] [initial_value:0]
  optional double aeb_le_cyclist_dx = 48;
  // [] [0|7] [initial_value:0]
  optional AEBLeCyclistTypType aeb_le_cyclist_typ = 49;
  // [] [0|3] [initial_value:0]
  optional AEBRiCyclistDetnType aeb_ri_cyclist_detn = 50;
  // [m] [0|150] [initial_value:0]
  optional double aeb_ri_cyclist_dx = 51;
  // [] [0|7] [initial_value:0]
  optional AEBRiCyclistTypType aeb_ri_cyclist_typ = 52;
}

message ACC_FD4_2B8 {
// Control Message
  enum AEBObjStateType {
    AEBOBJSTATE_UNDEFINED = 0;
    AEBOBJSTATE_PRECEDING = 1;
    AEBOBJSTATE_STATIONARY = 2;
    AEBOBJSTATE_STOPPED = 3;
    AEBOBJSTATE_RESERVED = 4;
  }
  enum CyclistObjStateType {
    CYCLISTOBJSTATE_UNDEFINED = 0;
    CYCLISTOBJSTATE_PRECEDING = 1;
    CYCLISTOBJSTATE_STATIONARY = 2;
    CYCLISTOBJSTATE_STOPPED = 3;
    CYCLISTOBJSTATE_RESERVED = 4;
  }
  enum ACCTgtObjStateType {
    ACCTGTOBJSTATE_UNDEFINED = 0;
    ACCTGTOBJSTATE_PRECEDING = 1;
    ACCTGTOBJSTATE_STATIONARY = 2;
    ACCTGTOBJSTATE_STOPPED = 3;
    ACCTGTOBJSTATE_RESERVED = 4;
  }
  enum HandsoffDetdType {
    HANDSOFFDETD_UNKNOWN = 0;
    HANDSOFFDETD_HANDS_OFF = 1;
    HANDSOFFDETD_NOT_HANDS_OFF = 2;
    HANDSOFFDETD_RESERVED = 3;
  }
  enum DrvgOnHiWayType {
    DRVGONHIWAY_UNKNOWN = 0;
    DRVGONHIWAY_ON_HIGNWAY = 1;
    DRVGONHIWAY_NOT_ON_HGH_WAY = 2;
    DRVGONHIWAY_RESERVED = 3;
  }
  enum SpdLimitType {
    SPDLIMIT_NO_SPL = 0;
    SPDLIMIT_SPL_5 = 1;
    SPDLIMIT_SPL_10 = 2;
    SPDLIMIT_SPL_15 = 3;
    SPDLIMIT_SPL_20 = 4;
    SPDLIMIT_SPL_25 = 5;
    SPDLIMIT_SPL_30 = 6;
    SPDLIMIT_SPL_35 = 7;
    SPDLIMIT_SPL_40 = 8;
    SPDLIMIT_SPL_45 = 9;
    SPDLIMIT_SPL_50 = 10;
    SPDLIMIT_SPL_55 = 11;
    SPDLIMIT_SPL_60 = 12;
    SPDLIMIT_SPL_65 = 13;
    SPDLIMIT_SPL_70 = 14;
    SPDLIMIT_SPL_75 = 15;
    SPDLIMIT_SPL_80 = 16;
    SPDLIMIT_SPL_85 = 17;
    SPDLIMIT_SPL_90 = 18;
    SPDLIMIT_SPL_95 = 19;
    SPDLIMIT_SPL_100 = 20;
    SPDLIMIT_SPL_105 = 21;
    SPDLIMIT_SPL_110 = 22;
    SPDLIMIT_SPL_115 = 23;
    SPDLIMIT_SPL_120 = 24;
    SPDLIMIT_SPL_120_25 = 25;
    SPDLIMIT_SPL_130 = 26;
    SPDLIMIT_SPL_135 = 27;
    SPDLIMIT_SPL_140 = 28;
    SPDLIMIT_SPL_145 = 29;
    SPDLIMIT_SPL_150 = 30;
    SPDLIMIT_RESERVED = 31;
    SPDLIMIT_INVALID = 63;
  }
  enum NavSpdLimUnitType {
    NAVSPDLIMUNIT_UNKNOWN = 0;
    NAVSPDLIMUNIT_KMH = 1;
    NAVSPDLIMUNIT_MPH = 2;
    NAVSPDLIMUNIT_RESERVED = 3;
  }
  enum DrvgOnRiLaneType {
    DRVGONRILANE_UNKNOWN = 0;
    DRVGONRILANE_ON_RIGHT_MOST_LANE = 1;
    DRVGONRILANE_NOT_ON_RIGHT_MOST_LANE = 2;
    DRVGONRILANE_RESERVED = 3;
  }
  enum ACCDataRecReqType {
    ACC_DATARECREQ_NO_DEMAND = 0;
    ACC_DATARECREQ_SCENARIO01 = 1;
    ACC_DATARECREQ_SCENARIO02 = 2;
    ACC_DATARECREQ_SCENARIO03 = 3;
    ACC_DATARECREQ_SCENARIO04 = 4;
    ACC_DATARECREQ_SCENARIO05 = 5;
    ACC_DATARECREQ_SCENARIO06 = 6;
    ACC_DATARECREQ_SCENARIO07 = 7;
    ACC_DATARECREQ_SCENARIO08 = 8;
    ACC_DATARECREQ_SCENARIO09 = 9;
    ACC_DATARECREQ_SCENARIO10 = 10;
    ACC_DATARECREQ_SCENARIO11 = 11;
    ACC_DATARECREQ_SCENARIO12 = 12;
    ACC_DATARECREQ_SCENARIO13 = 13;
    ACC_DATARECREQ_SCENARIO14 = 14;
    ACC_DATARECREQ_SCENARIO15 = 15;
    ACC_DATARECREQ_SCENARIO16 = 16;
    ACC_DATARECREQ_SCENARIO17 = 17;
    ACC_DATARECREQ_SCENARIO18 = 18;
    ACC_DATARECREQ_SCENARIO19 = 19;
    ACC_DATARECREQ_SCENARIO20 = 20;
    ACC_DATARECREQ_SCENARIO21 = 21;
    ACC_DATARECREQ_SCENARIO22 = 22;
    ACC_DATARECREQ_SCENARIO23 = 23;
    ACC_DATARECREQ_SCENARIO24 = 24;
    ACC_DATARECREQ_SCENARIO25 = 25;
    ACC_DATARECREQ_SCENARIO26 = 26;
    ACC_DATARECREQ_SCENARIO27 = 27;
    ACC_DATARECREQ_SCENARIO28 = 28;
    ACC_DATARECREQ_SCENARIO29 = 29;
    ACC_DATARECREQ_SCENARIO30 = 30;
    ACC_DATARECREQ_SCENARIO31 = 31;
    ACC_DATARECREQ_SCENARIO32 = 32;
    ACC_DATARECREQ_SCENARIO33 = 33;
    ACC_DATARECREQ_SCENARIO34 = 34;
    ACC_DATARECREQ_SCENARIO35 = 35;
    ACC_DATARECREQ_SCENARIO36 = 36;
    ACC_DATARECREQ_SCENARIO37 = 37;
    ACC_DATARECREQ_SCENARIO38 = 38;
    ACC_DATARECREQ_SCENARIO39 = 39;
    ACC_DATARECREQ_SCENARIO40 = 40;
    ACC_DATARECREQ_SCENARIO41 = 41;
    ACC_DATARECREQ_SCENARIO42 = 42;
    ACC_DATARECREQ_SCENARIO43 = 43;
    ACC_DATARECREQ_SCENARIO44 = 44;
    ACC_DATARECREQ_SCENARIO45 = 45;
    ACC_DATARECREQ_SCENARIO46 = 46;
    ACC_DATARECREQ_SCENARIO47 = 47;
    ACC_DATARECREQ_SCENARIO48 = 48;
    ACC_DATARECREQ_SCENARIO49 = 49;
    ACC_DATARECREQ_SCENARIO50 = 50;
    ACC_DATARECREQ_RESERVED = 51;
  }
  enum ACCSOSReqType {
    ACC_SOSREQ_NO_REQUEST = 0;
    ACC_SOSREQ_REQUEST = 1;
    ACC_SOSREQ_RESERVED = 2;
    ACC_SOSREQ_INVALID = 3;
  }
  enum PedObjStateType {
    PEDOBJSTATE_UNDEFINED = 0;
    PEDOBJSTATE_PRECEDING = 1;
    PEDOBJSTATE_STATIONARY = 2;
    PEDOBJSTATE_STOPPED = 3;
    PEDOBJSTATE_RESERVED = 4;
  }
  enum DrvrStsType {
    DRVRSTS_GOOD = 0;
    DRVRSTS_UNBELIEVABLE = 1;
    DRVRSTS_LOW = 2;
    DRVRSTS_MIDDLE = 3;
    DRVRSTS_HIGH_LEVEL_1 = 4;
    DRVRSTS_HIGH_LEVEL_2 = 5;
    DRVRSTS_RESERVED = 6;
  }
  enum AEBObjOverlapType {
    AEBOBJOVERLAP_VALID = 0;
    AEBOBJOVERLAP_RESERVED = 21;
    AEBOBJOVERLAP_INVALID = 31;
  }
  enum AEBObjDrvbyType {
    AEBOBJDRVBY_VALID = 0;
    AEBOBJDRVBY_RESERVED = 21;
    AEBOBJDRVBY_INVALID = 31;
  }
  // [] [0|255] [initial_value:255]
  optional int32 aeb_obj_id = 1;
  // [%] [0|0.984375] [initial_value:0]
  optional double aeb_obj_exist = 2;
  // [] [0|1] [initial_value:0]
  optional bool aeb_obj_video_cfmd = 3;
  // [m/s] [-128|127] [initial_value:0]
  optional double aeb_obj_vx = 4;
  // [m] [0|255.9375] [initial_value:0]
  optional double aeb_obj_dx = 5;
  // [] [0|3855] [initial_value:0]
  optional int32 aeb_obj_cnt_alive = 6;
  // [m] [-128|127.875] [initial_value:0]
  optional double aeb_obj_dy = 7;
  // [] [0|7] [initial_value:0]
  optional AEBObjStateType aeb_obj_state = 8;
  // [%] [0|0.96875] [initial_value:0]
  optional double aeb_obj_obstcl = 9;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double aeb_obj_vy = 10;
  // [%] [0|0.984375] [initial_value:0]
  optional double cyclist_obj_exist = 11;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double aeb_obj_ax = 12;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double aeb_obj_ay = 13;
  // [m] [0|255.9375] [initial_value:0]
  optional double cyclist_obj_dx = 14;
  // [] [0|255] [initial_value:255]
  optional int32 cyclist_obj_id = 15;
  // [%] [0|0.96875] [initial_value:0]
  optional double cyclist_obj_obstcl = 16;
  // [m] [-128|127.875] [initial_value:0]
  optional double cyclist_obj_dy = 17;
  // [m/s] [-128|127] [initial_value:0]
  optional double cyclist_obj_vx = 18;
  // [] [0|3855] [initial_value:0]
  optional int32 cyclist_obj_cnt_alive = 19;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double cyclist_obj_vy = 20;
  // [] [0|7] [initial_value:0]
  optional CyclistObjStateType cyclist_obj_state = 21;
  // [] [0|1] [initial_value:0]
  optional bool cyclist_obj_video_cfmd = 22;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double cyclist_obj_ax = 23;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double cyclist_obj_ay = 24;
  // [%] [0|0.984375] [initial_value:0]
  optional double ped_obj_exist = 25;
  // [] [0|255] [initial_value:255]
  optional int32 ped_obj_id = 26;
  // [%] [0|0.96875] [initial_value:0]
  optional double ped_obj_obstcl = 27;
  // [m] [-128|127.875] [initial_value:0]
  optional double ped_obj_dy = 28;
  // [m] [0|255.9375] [initial_value:0]
  optional double ped_obj_dx = 29;
  // [m/s] [-128|127] [initial_value:0]
  optional double ped_obj_vx = 30;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double ped_obj_vy = 31;
  // [] [0|1] [initial_value:0]
  optional bool ped_obj_video_cfmd = 32;
  // [m] [0|255.9375] [initial_value:0]
  optional double acc_tgt_obj_dx = 33;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double ped_obj_ay = 34;
  // [] [0|3855] [initial_value:0]
  optional int32 ped_obj_cnt_alive = 35;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double ped_obj_ax = 36;
  // [] [0|255] [initial_value:255]
  optional int32 acc_tgt_obj_id = 37;
  // [%] [0|0.96875] [initial_value:0]
  optional double acc_tgt_obj_obstcl = 38;
  // [m] [-128|127.875] [initial_value:0]
  optional double acc_tgt_obj_dy = 39;
  // [%] [0|0.984375] [initial_value:0]
  optional double acc_tgt_obj_exist = 40;
  // [m/s] [-64|63.875] [initial_value:0]
  optional double acc_tgt_obj_vy = 41;
  // [m/s] [-128|127] [initial_value:0]
  optional double acc_tgt_obj_vx = 42;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double acc_tgt_obj_ax = 43;
  // [m/s2] [-16|15.96875] [initial_value:0]
  optional double acc_tgt_obj_ay = 44;
  // [] [0|7] [initial_value:0]
  optional ACCTgtObjStateType acc_tgt_obj_state = 45;
  // [] [0|1] [initial_value:0]
  optional bool acc_tgt_obj_video_cfmd = 46;
  // [] [0|3855] [initial_value:0]
  optional int32 acc_tgt_obj_cnt_alive = 47;
  // [rad] [-0.2048|0.2047] [initial_value:0]
  optional double radar_hor_misalign = 48;
  // [] [0|3] [initial_value:0]
  optional HandsoffDetdType handsoff_detd = 49;
  // [] [0|3] [initial_value:0]
  optional DrvgOnHiWayType drvg_on_hi_way = 50;
  // [] [-0.16352|0.16352] [initial_value:511]
  optional double road_crvt = 51;
  // [m] [0|600] [initial_value:63]
  optional double spd_limit_dst = 52;
  // [] [-0.16352|0.16352] [initial_value:511]
  optional double road_crvt_far = 53;
  // [m] [0|600] [initial_value:63]
  optional double ramp_dst = 54;
  // [] [0|255] [initial_value:254]
  optional SpdLimitType spd_limit = 55;
  // [m] [0|600] [initial_value:63]
  optional double curve_dst = 56;
  // [] [0|3] [initial_value:0]
  optional NavSpdLimUnitType nav_spd_lim_unit = 57;
  // [m] [0|600] [initial_value:63]
  optional double tunnel_dst = 58;
  // [] [0|3] [initial_value:0]
  optional DrvgOnRiLaneType drvg_on_ri_lane = 59;
  // [m] [0|600] [initial_value:63]
  optional double brdg_dst = 60;
  // [] [0|1] [initial_value:0]
  optional bool radar_drvr_attentive = 61;
  // [] [0|255] [initial_value:0]
  optional ACCDataRecReqType acc_data_rec_req = 62;
  // [] [0|3] [initial_value:0]
  optional ACCSOSReqType acc_sos_req = 63;
  // [] [0|7] [initial_value:0]
  optional PedObjStateType ped_obj_state = 64;
  // [] [0|1] [initial_value:0]
  optional bool map_data_valid = 65;
  // [] [0|7] [initial_value:0]
  optional DrvrStsType drvr_sts = 66;
  // [] [0|31] [initial_value:0]
  optional AEBObjOverlapType aeb_obj_overlap = 67;
  // [] [0|31] [initial_value:0]
  optional AEBObjDrvbyType aeb_obj_drvby = 68;
}

message GW_FD1_2BB {
// Report Message
  enum OTAUpgrdModStsType {
    OTA_UPGRDMODSTS_UPGRADE_MODE_OFF = 0;
    OTA_UPGRDMODSTS_REMOTE_UPGRADE_MODE_ON_POWERTRAIN_OFF = 1;
    OTA_UPGRDMODSTS_NORMAL_UPGRADE_MODE_ON_POWERTRAIN_OFF = 2;
    OTA_UPGRDMODSTS_RESERVED = 3;
  }
  // description / [NoUnit] [0|3] [initial_value:0]
  optional OTAUpgrdModStsType ota_upgrd_mod_sts = 1;
}

message HUT_FD1_2C3 {
// Report Message
  enum IFCSnvtySetType {
    IFC_SNVTYSET_NORMAL = 0;
    IFC_SNVTYSET_HIGH_SENSITIVITY = 1;
    IFC_SNVTYSET_LOW_SENSITIVITY = 2;
    IFC_SNVTYSET_RESERVED = 3;
  }
  enum FCWSnvtySetType {
    FCW_SNVTYSET_NORMAL = 0;
    FCW_SNVTYSET_HIGH_SENSITIVITY = 1;
    FCW_SNVTYSET_LOW_SENSITIVITY = 2;
    FCW_SNVTYSET_RESERVED = 3;
  }
  enum LSSWarnFormSwtReqType {
    LSSWARNFORMSWTREQ_VOICE = 0;
    LSSWARNFORMSWTREQ_HAPTIC = 1;
    LSSWARNFORMSWTREQ_VOICE_HAPTIC = 2;
    LSSWARNFORMSWTREQ_RESERVED = 3;
  }
  enum NavigationRoadTypeType {
    NAVIGATIONROADTYPE_UNKNOWN = 0;
    NAVIGATIONROADTYPE_EXPRESSWAY = 1;
    NAVIGATIONROADTYPE_CITY_EXPRESSWAY = 2;
    NAVIGATIONROADTYPE_NATIONAL_RODE = 3;
    NAVIGATIONROADTYPE_PROVINCIAL_RODE = 4;
    NAVIGATIONROADTYPE_COUNTY_RODE = 5;
    NAVIGATIONROADTYPE_TOWNSHIP_ROAD = 6;
    NAVIGATIONROADTYPE_RAMP = 7;
    NAVIGATIONROADTYPE_TUNNEL = 8;
    NAVIGATIONROADTYPE_SIDE_RODE = 9;
    NAVIGATIONROADTYPE_CHANNEL = 10;
    NAVIGATIONROADTYPE_MAJOR_ROAD = 11;
    NAVIGATIONROADTYPE_ORDINARY_ROAD = 12;
    NAVIGATIONROADTYPE_VILLAGE_ROAD = 13;
    NAVIGATIONROADTYPE_RESERVED = 14;
  }
  enum NavSpdLimTypeType {
    NAVSPDLIMTYPE_UNKNOWN = 0;
    NAVSPDLIMTYPE_TRAFFIC_SIGN = 1;
    NAVSPDLIMTYPE_ELECTRONIC_EYE = 2;
    NAVSPDLIMTYPE_RODE_DEFAULT = 3;
  }
  enum NavSpdLimValueType {
    NAVSPDLIMVALUE_NO_DISPLAY = 0;
    NAVSPDLIMVALUE_SPL_5 = 1;
    NAVSPDLIMVALUE_SPL_10 = 2;
    NAVSPDLIMVALUE_SPL_15 = 3;
    NAVSPDLIMVALUE_SPL_20 = 4;
    NAVSPDLIMVALUE_SPL_25 = 5;
    NAVSPDLIMVALUE_SPL_30 = 6;
    NAVSPDLIMVALUE_SPL_35 = 7;
    NAVSPDLIMVALUE_SPL_40 = 8;
    NAVSPDLIMVALUE_SPL_45 = 9;
    NAVSPDLIMVALUE_SPL_50 = 10;
    NAVSPDLIMVALUE_SPL_55 = 11;
    NAVSPDLIMVALUE_SPL_60 = 12;
    NAVSPDLIMVALUE_SPL_65 = 13;
    NAVSPDLIMVALUE_SPL_70 = 14;
    NAVSPDLIMVALUE_SPL_75 = 15;
    NAVSPDLIMVALUE_SPL_80 = 16;
    NAVSPDLIMVALUE_SPL_85 = 17;
    NAVSPDLIMVALUE_SPL_90 = 18;
    NAVSPDLIMVALUE_SPL_95 = 19;
    NAVSPDLIMVALUE_SPL_100 = 20;
    NAVSPDLIMVALUE_SPL_105 = 21;
    NAVSPDLIMVALUE_SPL_110 = 22;
    NAVSPDLIMVALUE_SPL_115 = 23;
    NAVSPDLIMVALUE_SPL_120 = 24;
    NAVSPDLIMVALUE_SPL_125 = 25;
    NAVSPDLIMVALUE_SPL_130 = 26;
    NAVSPDLIMVALUE_SPL_135 = 27;
    NAVSPDLIMVALUE_SPL_140 = 28;
    NAVSPDLIMVALUE_SPL_145 = 29;
    NAVSPDLIMVALUE_SPL_150 = 30;
    NAVSPDLIMVALUE_RESERVED = 31;
    NAVSPDLIMVALUE_INVALID = 63;
  }
  enum NavSpdLimUnitHUTType {
    NAVSPDLIMUNIT_HUT_UNKNOWN = 0;
    NAVSPDLIMUNIT_HUT_KMH = 1;
    NAVSPDLIMUNIT_HUT_MPH = 2;
    NAVSPDLIMUNIT_HUT_RESERVED = 3;
  }
  enum NavCtryTypeType {
    NAVCTRYTYPE_RESERVED = 0;
    NAVCTRYTYPE_CHINA = 156;
    NAVCTRYTYPE_RESERVED_157 = 157;
  }
  enum NavSpdLimSignStsType {
    NAVSPDLIMSIGNSTS_UNKNOW = 0;
    NAVSPDLIMSIGNSTS_EXIST_SPL_SIGN = 1;
    NAVSPDLIMSIGNSTS_EXIST_TRAFFIC_SIGN = 2;
    NAVSPDLIMSIGNSTS_EXIST_SPL_AND_TRAFFIC_SIGN = 3;
    NAVSPDLIMSIGNSTS_NONE = 4;
    NAVSPDLIMSIGNSTS_PLURAL = 5;
    NAVSPDLIMSIGNSTS_RESERVED = 6;
  }
  enum NavTSRTrafSignType {
    NAVTSRTRAFSIGN_NONE = 0;
    NAVTSRTRAFSIGN_SNOW = 1;
    NAVTSRTRAFSIGN_FOG = 2;
    NAVTSRTRAFSIGN_THUNDERSTORM = 3;
    NAVTSRTRAFSIGN_RAIN = 4;
    NAVTSRTRAFSIGN_FWAY = 5;
    NAVTSRTRAFSIGN_EFWAY = 6;
    NAVTSRTRAFSIGN_STRAIGHT = 7;
    NAVTSRTRAFSIGN_FRONTAGELEFT = 8;
    NAVTSRTRAFSIGN_TURNLEFTRIGHT = 9;
    NAVTSRTRAFSIGN_FRONTAGERIGHT = 10;
    NAVTSRTRAFSIGN_RIGHT = 11;
    NAVTSRTRAFSIGN_TURNRIGHT = 12;
    NAVTSRTRAFSIGN_LEFT = 13;
    NAVTSRTRAFSIGN_TURNLEFT = 14;
    NAVTSRTRAFSIGN_RELIEVENOOVERTAKE = 15;
    NAVTSRTRAFSIGN_NOOVERTAKE = 16;
    NAVTSRTRAFSIGN_STOPGIVEWAY = 17;
    NAVTSRTRAFSIGN_GIVEWAY = 18;
    NAVTSRTRAFSIGN_NOPASS = 19;
    NAVTSRTRAFSIGN_NOENTRY = 20;
    NAVTSRTRAFSIGN_NOCAR = 21;
    NAVTSRTRAFSIGN_NOTRUCK = 22;
    NAVTSRTRAFSIGN_NOBUS = 23;
    NAVTSRTRAFSIGN_NOTURNAROUND = 24;
    NAVTSRTRAFSIGN_NOSTRAIGHTLEFTTURN = 25;
    NAVTSRTRAFSIGN_NOSTRAIGHTRIGHTTURN = 26;
    NAVTSRTRAFSIGN_NOLEFTRIGHTTURN = 27;
    NAVTSRTRAFSIGN_NOLEFTTURN = 28;
    NAVTSRTRAFSIGN_NORIGHTTURN = 29;
    NAVTSRTRAFSIGN_NOSTRAIGHT = 30;
    NAVTSRTRAFSIGN_WARNKIDS = 31;
    NAVTSRTRAFSIGN_WARNPED = 32;
    NAVTSRTRAFSIGN_WARNBIKE = 33;
    NAVTSRTRAFSIGN_WARNWORKS = 34;
    NAVTSRTRAFSIGN_WARNWILDANIMAL = 35;
    NAVTSRTRAFSIGN_WARNCATTLE = 36;
    NAVTSRTRAFSIGN_ACCIDENTPRONESECTION = 37;
    NAVTSRTRAFSIGN_TOWN = 38;
    NAVTSRTRAFSIGN_SLOW = 39;
    NAVTSRTRAFSIGN_SHARPTURNR = 40;
    NAVTSRTRAFSIGN_SHARPTURNL = 41;
    NAVTSRTRAFSIGN_REVERSECURVEL = 42;
    NAVTSRTRAFSIGN_REVERSECURVER = 43;
    NAVTSRTRAFSIGN_CONTINUOUSBEND = 44;
    NAVTSRTRAFSIGN_CROSSROADS = 45;
    NAVTSRTRAFSIGN_CONFLUENCER = 46;
    NAVTSRTRAFSIGN_CONFLUENCEL = 47;
    NAVTSRTRAFSIGN_RESERVED = 48;
    NAVTSRTRAFSIGN_INVALID = 255;
  }
  enum HWASnvtySetType {
    HWA_SNVTYSET_NORMAL = 0;
    HWA_SNVTYSET_HIGH_SENSITIVITY = 1;
    HWA_SNVTYSET_LOW_SENSITIVITY = 2;
    HWA_SNVTYSET_RESERVED = 3;
  }
  enum ADASPosnMsgTyeType {
    ADAS_POSN_MSGTYE_SYSTEM_SPECIFIC = 0;
    ADAS_POSN_MSGTYE_POSITION = 1;
    ADAS_POSN_MSGTYE_SEGMENT = 2;
    ADAS_POSN_MSGTYE_STUB = 3;
    ADAS_POSN_MSGTYE_PROFILE_SHORT = 4;
    ADAS_POSN_MSGTYE_PROFILE_LONG = 5;
    ADAS_POSN_MSGTYE_META_DATA = 6;
    ADAS_POSN_MSGTYE_RESERVED = 7;
  }
  enum ADASPosnCurLaneType {
    ADAS_POSN_CURLANE_UNKNOWN = 0;
    ADAS_POSN_CURLANE_EMERGENCY_LANE = 1;
    ADAS_POSN_CURLANE_SINGLE_LANE_ROAD = 2;
    ADAS_POSN_CURLANE_LEFT_MOST_LANE = 3;
    ADAS_POSN_CURLANE_RIGHT_MOST_LANE = 4;
    ADAS_POSN_CURLANE_ONE_OF_MIDDLE_LANES_ON_ROAD_WITH_THREE_OR_MORE_LANES = 5;
    ADAS_POSN_CURLANE_RESERVED = 6;
    ADAS_POSN_CURLANE_INVALID_DATA = 7;
  }
  // description / [] [0|1] [initial_value:0]
  optional bool intellgntcurve_req = 1;
  // [] [0|1] [initial_value:0]
  optional bool noh_btn_sts = 2;
  // [] [0|1] [initial_value:0]
  optional bool dr_calibrated_sts = 3;
  // [] [0|3] [initial_value:0]
  optional IFCSnvtySetType ifc_snvty_set = 4;
  // [] [0|1] [initial_value:1]
  optional bool aeb_ped_swt_req = 5;
  // [] [0|3] [initial_value:0]
  optional FCWSnvtySetType fcw_snvty_set = 6;
  // [] [0|1] [initial_value:1]
  optional bool aeb_veh_swt_req = 7;
  // [] [0|1] [initial_value:0]
  optional bool lka_swt_req = 8;
  // [] [0|1] [initial_value:0]
  optional bool lck_swt_req = 9;
  // [] [0|1] [initial_value:0]
  optional bool elk_swt_req = 10;
  // [] [0|1] [initial_value:0]
  optional bool ess_ped_swt_req = 11;
  // [] [0|1] [initial_value:0]
  optional bool ess_veh_swt_req = 12;
  // [] [0|1] [initial_value:0]
  optional bool ldw_swt_req = 13;
  // [] [0|1] [initial_value:0]
  optional bool tsr_swt_req = 14;
  // [] [0|1] [initial_value:0]
  optional bool tsr_warn_swt_req = 15;
  // [] [0|1] [initial_value:0]
  optional bool intelligent_eva_swt_req = 16;
  // [] [0|1] [initial_value:0]
  optional bool isl_swt_req = 17;
  // [km/h] [-15|15] [initial_value:15]
  optional int32 tsr_snvty_set = 18;
  // [] [0|1] [initial_value:0]
  optional bool lss_swt_req = 19;
  // [] [0|3] [initial_value:0]
  optional LSSWarnFormSwtReqType lss_warn_form_swt_req = 20;
  // [] [0|1] [initial_value:0]
  optional bool dow_swt_req = 21;
  // [] [0|1] [initial_value:0]
  optional bool lca_swt_req = 22;
  // [] [0|1] [initial_value:0]
  optional bool rcta_swt_req = 23;
  // [] [0|1] [initial_value:0]
  optional bool rcta_brk_swt_req = 24;
  // [] [0|1] [initial_value:0]
  optional bool rcw_swt_req = 25;
  // [] [0|1023] [initial_value:0]
  optional int32 nav_veh_to_traf_eye_dist = 26;
  // [] [0|15] [initial_value:0]
  optional NavigationRoadTypeType navigation_road_type = 27;
  // [] [0|3] [initial_value:0]
  optional NavSpdLimTypeType nav_spd_lim_type = 28;
  // [] [0|63] [initial_value:0]
  optional NavSpdLimValueType nav_spd_lim_value = 29;
  // [] [0|3] [initial_value:1]
  optional NavSpdLimUnitHUTType nav_spd_lim_unit_hut = 30;
  // [] [0|1023] [initial_value:156]
  optional NavCtryTypeType nav_ctry_type = 31;
  // [] [0|7] [initial_value:0]
  optional NavSpdLimSignStsType nav_spd_lim_sign_sts = 32;
  // [] [0|255] [initial_value:0]
  optional NavTSRTrafSignType nav_tsr_traf_sign = 33;
  // [] [0|1] [initial_value:0]
  optional bool aeb_ja_swt_req = 34;
  // [] [0|1] [initial_value:0]
  optional bool aes_swt_req = 35;
  // [] [0|1] [initial_value:1]
  optional bool hwa_swt_req = 36;
  // [] [0|3] [initial_value:0]
  optional HWASnvtySetType hwa_snvty_set = 37;
  // [] [0|1] [initial_value:0]
  optional bool alc_swt_req = 38;
  // [] [0|1] [initial_value:0]
  optional bool fcta_brk_swt_req = 39;
  // [] [0|1] [initial_value:0]
  optional bool fcta_swt_req = 40;
  // [] [0|7] [initial_value:0]
  optional ADASPosnMsgTyeType adas_posn_msg_tye = 41;
  // [m] [0|8190] [initial_value:8191]
  optional int32 adas_posn_offset = 42;
  // [] [0|3] [initial_value:0]
  optional int32 adas_posn_cyc_cnt = 43;
  // [] [0|63] [initial_value:0]
  optional int32 adas_posn_pathldx = 44;
  // [m] [0|3] [initial_value:0]
  optional int32 adas_posn_idx = 45;
  // [] [0|100] [initial_value:0]
  optional double adas_posn_pos_probb = 46;
  // [ms] [0|2545] [initial_value:0]
  optional double adas_posn_age = 47;
  // [] [0|7] [initial_value:0]
  optional int32 adas_posn_pos_confdc = 48;
  // [] [0|7] [initial_value:0]
  optional ADASPosnCurLaneType adas_posn_cur_lane = 49;
  // [m/s] [-12.8|89.2] [initial_value:511]
  optional double adas_posn_spd = 50;
  // [] [0|358.583] [initial_value:0]
  optional double adas_posn_rehead = 51;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_hut_fd1 = 52;
}

message DDCM1_2CA {
// Report Message
  enum DrvWinPosnStsPercType {
    DRVWINPOSNSTS_PERC_UNKNOWN = 0;
    DRVWINPOSNSTS_PERC_CLOSE = 1;
    DRVWINPOSNSTS_PERC_OPEN = 2;
    DRVWINPOSNSTS_PERC_5_OPEN = 3;
    DRVWINPOSNSTS_PERC_10_OPEN = 4;
    DRVWINPOSNSTS_PERC_15_OPEN = 5;
    DRVWINPOSNSTS_PERC_20_OPEN = 6;
    DRVWINPOSNSTS_PERC_25_OPEN = 7;
    DRVWINPOSNSTS_PERC_30_OPEN = 8;
    DRVWINPOSNSTS_PERC_35_OPEN = 9;
    DRVWINPOSNSTS_PERC_40_OPEN = 10;
    DRVWINPOSNSTS_PERC_45_OPEN = 11;
    DRVWINPOSNSTS_PERC_50_OPEN = 12;
    DRVWINPOSNSTS_PERC_55_OPEN = 13;
    DRVWINPOSNSTS_PERC_60_OPEN = 14;
    DRVWINPOSNSTS_PERC_65_OPEN = 15;
    DRVWINPOSNSTS_PERC_70_OPEN = 16;
    DRVWINPOSNSTS_PERC_75_OPEN = 17;
    DRVWINPOSNSTS_PERC_80_OPEN = 18;
    DRVWINPOSNSTS_PERC_85_OPEN = 19;
    DRVWINPOSNSTS_PERC_90_OPEN = 20;
    DRVWINPOSNSTS_PERC_95_OPEN = 21;
    DRVWINPOSNSTS_PERC_RESERVED = 22;
  }
  // [] [0|31] [initial_value:0]
  optional DrvWinPosnStsPercType drv_win_posn_sts_perc = 1;
  // [] [0|1] [initial_value:0]
  optional bool rsds_driver_led_sts = 2;
}

message PDCM1_2CD {
// Report Message
  enum PassWinPosnStsPercType {
    PASSWINPOSNSTS_PERC_UNKNOWN = 0;
    PASSWINPOSNSTS_PERC_CLOSE = 1;
    PASSWINPOSNSTS_PERC_OPEN = 2;
    PASSWINPOSNSTS_PERC_5_OPEN = 3;
    PASSWINPOSNSTS_PERC_10_OPEN = 4;
    PASSWINPOSNSTS_PERC_15_OPEN = 5;
    PASSWINPOSNSTS_PERC_20_OPEN = 6;
    PASSWINPOSNSTS_PERC_25_OPEN = 7;
    PASSWINPOSNSTS_PERC_30_OPEN = 8;
    PASSWINPOSNSTS_PERC_35_OPEN = 9;
    PASSWINPOSNSTS_PERC_40_OPEN = 10;
    PASSWINPOSNSTS_PERC_45_OPEN = 11;
    PASSWINPOSNSTS_PERC_50_OPEN = 12;
    PASSWINPOSNSTS_PERC_55_OPEN = 13;
    PASSWINPOSNSTS_PERC_60_OPEN = 14;
    PASSWINPOSNSTS_PERC_65_OPEN = 15;
    PASSWINPOSNSTS_PERC_70_OPEN = 16;
    PASSWINPOSNSTS_PERC_75_OPEN = 17;
    PASSWINPOSNSTS_PERC_80_OPEN = 18;
    PASSWINPOSNSTS_PERC_85_OPEN = 19;
    PASSWINPOSNSTS_PERC_90_OPEN = 20;
    PASSWINPOSNSTS_PERC_95_OPEN = 21;
    PASSWINPOSNSTS_PERC_RESERVED = 22;
  }
  // [] [0|31] [initial_value:0]
  optional PassWinPosnStsPercType pass_win_posn_sts_perc = 1;
  // [] [0|1] [initial_value:0]
  optional bool rsds_pass_led_sts = 2;
}

message IFC_FD3_2CF {
// Control Message
  enum TSRSpdLimUnitType {
    TSRSPDLIMUNIT_UNKNOWN = 0;
    TSRSPDLIMUNIT_KMH = 1;
    TSRSPDLIMUNIT_MPH = 2;
    TSRSPDLIMUNIT_RESERVED = 3;
  }
  enum TSRSpdLimType {
    TSRSPDLIM_NO_DISPLAY = 0;
    TSRSPDLIM_SPL_5 = 1;
    TSRSPDLIM_SPL_10 = 2;
    TSRSPDLIM_SPL_15 = 3;
    TSRSPDLIM_SPL_20 = 4;
    TSRSPDLIM_SPL_25 = 5;
    TSRSPDLIM_SPL_30 = 6;
    TSRSPDLIM_SPL_35 = 7;
    TSRSPDLIM_SPL_40 = 8;
    TSRSPDLIM_SPL_45 = 9;
    TSRSPDLIM_SPL_50 = 10;
    TSRSPDLIM_SPL_55 = 11;
    TSRSPDLIM_SPL_60 = 12;
    TSRSPDLIM_SPL_65 = 13;
    TSRSPDLIM_SPL_70 = 14;
    TSRSPDLIM_SPL_75 = 15;
    TSRSPDLIM_SPL_80 = 16;
    TSRSPDLIM_SPL_85 = 17;
    TSRSPDLIM_SPL_90 = 18;
    TSRSPDLIM_SPL_95 = 19;
    TSRSPDLIM_SPL_100 = 20;
    TSRSPDLIM_SPL_105 = 21;
    TSRSPDLIM_SPL_110 = 22;
    TSRSPDLIM_SPL_115 = 23;
    TSRSPDLIM_SPL_120 = 24;
    TSRSPDLIM_SPL_125 = 25;
    TSRSPDLIM_SPL_130 = 26;
    TSRSPDLIM_SPL_135 = 27;
    TSRSPDLIM_SPL_140 = 28;
    TSRSPDLIM_SPL_145 = 29;
    TSRSPDLIM_SPL_150 = 30;
    TSRSPDLIM_RESERVED = 31;
    TSRSPDLIM_INVALID = 63;
  }
  enum TSRSpdLimCfdcType {
    TSRSPDLIMCFDC_NULL = 0;
    TSRSPDLIMCFDC_IMPLICIT = 1;
    TSRSPDLIMCFDC_EXPLICIT = 2;
    TSRSPDLIMCFDC_RESERVED = 3;
  }
  enum TSRStsType {
    TSRSTS_OFF = 0;
    TSRSTS_FUSION_MODE = 1;
    TSRSTS_VISION_MODE = 2;
    TSRSTS_NAVIGATION_ONLY_MODE = 3;
    TSRSTS_FAILED = 4;
    TSRSTS_ISA_FAILED = 5;
    TSRSTS_ISA_OFF = 6;
    TSRSTS_RESERVED = 7;
  }
  enum TSRSpdLimStsType {
    TSRSPDLIMSTS_NO_SPEED_LIMIT = 0;
    TSRSPDLIMSTS_SPEED_LIMIT = 1;
    TSRSPDLIMSTS_SPEED_LIMIT_CANCEL = 2;
    TSRSPDLIMSTS_RESERVED = 3;
  }
  enum TSRTrfcSignValCfdcType {
    TSRTRFCSIGNVALCFDC_NULL = 0;
    TSRTRFCSIGNVALCFDC_IMPLICIT = 1;
    TSRTRFCSIGNVALCFDC_EXPLICIT = 2;
    TSRTRFCSIGNVALCFDC_RESERVED = 3;
  }
  enum TSRTrfcSignValType {
    TSRTRFCSIGNVAL_NONE = 0;
    TSRTRFCSIGNVAL_SNOW = 1;
    TSRTRFCSIGNVAL_FOG = 2;
    TSRTRFCSIGNVAL_THUNDERSTORM = 3;
    TSRTRFCSIGNVAL_RAIN = 4;
    TSRTRFCSIGNVAL_FWAY = 5;
    TSRTRFCSIGNVAL_EFWAY = 6;
    TSRTRFCSIGNVAL_STRAIGHT = 7;
    TSRTRFCSIGNVAL_FRONTAGELEFT = 8;
    TSRTRFCSIGNVAL_TURNLEFTRIGHT = 9;
    TSRTRFCSIGNVAL_FRONTAGERIGHT = 10;
    TSRTRFCSIGNVAL_RIGHT = 11;
    TSRTRFCSIGNVAL_TURNRIGHT = 12;
    TSRTRFCSIGNVAL_LEFT = 13;
    TSRTRFCSIGNVAL_TURNLEFT = 14;
    TSRTRFCSIGNVAL_RELIEVENOOVERTAKE = 15;
    TSRTRFCSIGNVAL_NOOVERTAKE = 16;
    TSRTRFCSIGNVAL_STOPGIVEWAY = 17;
    TSRTRFCSIGNVAL_GIVEWAY = 18;
    TSRTRFCSIGNVAL_NOPASS = 19;
    TSRTRFCSIGNVAL_NOENTRY = 20;
    TSRTRFCSIGNVAL_NOCAR = 21;
    TSRTRFCSIGNVAL_NOTRUCK = 22;
    TSRTRFCSIGNVAL_NOBUS = 23;
    TSRTRFCSIGNVAL_NOTURNAROUND = 24;
    TSRTRFCSIGNVAL_NOSTRAIGHTLEFTTURN = 25;
    TSRTRFCSIGNVAL_NOSTRAIGHTRIGHTTURN = 26;
    TSRTRFCSIGNVAL_NOLEFTRIGHTTURN = 27;
    TSRTRFCSIGNVAL_NOLEFTTURN = 28;
    TSRTRFCSIGNVAL_NORIGHTTURN = 29;
    TSRTRFCSIGNVAL_NOSTRAIGHT = 30;
    TSRTRFCSIGNVAL_WARNKIDS = 31;
    TSRTRFCSIGNVAL_WARNPED = 32;
    TSRTRFCSIGNVAL_WARNBIKE = 33;
    TSRTRFCSIGNVAL_WARNWORKS = 34;
    TSRTRFCSIGNVAL_WARNWILDANIMAL = 35;
    TSRTRFCSIGNVAL_WARNCATTLE = 36;
    TSRTRFCSIGNVAL_ACCIDENTPRONESECTION = 37;
    TSRTRFCSIGNVAL_TOWN = 38;
    TSRTRFCSIGNVAL_SLOW = 39;
    TSRTRFCSIGNVAL_SHARPTURNR = 40;
    TSRTRFCSIGNVAL_SHARPTURNL = 41;
    TSRTRFCSIGNVAL_REVERSECURVEL = 42;
    TSRTRFCSIGNVAL_REVERSECURVER = 43;
    TSRTRFCSIGNVAL_CONTINUOUSBEND = 44;
    TSRTRFCSIGNVAL_CROSSROADS = 45;
    TSRTRFCSIGNVAL_CONFLUENCER = 46;
    TSRTRFCSIGNVAL_CONFLUENCEL = 47;
    TSRTRFCSIGNVAL_RAIN_WETNESS = 48;
    TSRTRFCSIGNVAL_SNOW_ICY = 49;
    TSRTRFCSIGNVAL_TIME = 50;
    TSRTRFCSIGNVAL_DISTANCE_FOR = 51;
    TSRTRFCSIGNVAL_DISTANCE_IN = 52;
    TSRTRFCSIGNVAL_STRAIGHT_53 = 53;
    TSRTRFCSIGNVAL_FRONTAGELEFT_54 = 54;
    TSRTRFCSIGNVAL_TURNLEFTRIGHT_55 = 55;
    TSRTRFCSIGNVAL_FRONTAGERIGHT_56 = 56;
    TSRTRFCSIGNVAL_RIGHT_57 = 57;
    TSRTRFCSIGNVAL_TURN_RIGHT = 58;
    TSRTRFCSIGNVAL_LEFT_59 = 59;
    TSRTRFCSIGNVAL_TURN_LEFT = 60;
    TSRTRFCSIGNVAL_HIGHWAY_START = 61;
    TSRTRFCSIGNVAL_HIGHWAY_END = 62;
    TSRTRFCSIGNVAL_MORTORWAY_START = 63;
    TSRTRFCSIGNVAL_MORTORWAY_END = 64;
    TSRTRFCSIGNVAL_CITY_ENTRY = 65;
    TSRTRFCSIGNVAL_CITY_EXIT = 66;
    TSRTRFCSIGNVAL_RESIDENTIAL_ZONES_ENTRY = 67;
    TSRTRFCSIGNVAL_RESIDENTIAL_ZONES_EXIT = 68;
    TSRTRFCSIGNVAL_TRAILER = 69;
    TSRTRFCSIGNVAL_GIVEWAY_TO_ONCOMING_VEHICLES = 70;
    TSRTRFCSIGNVAL_NO_STOPPING = 71;
    TSRTRFCSIGNVAL_NO_PARKING = 72;
    TSRTRFCSIGNVAL_DRIVE_WITH_CAUTION = 73;
    TSRTRFCSIGNVAL_ROAD_NARROW = 74;
    TSRTRFCSIGNVAL_BEWARE_OF_CROSSWIND = 75;
    TSRTRFCSIGNVAL_SLIPPERY_SURFACE = 76;
    TSRTRFCSIGNVAL_BUMPY_ROAD_AHEAD = 77;
    TSRTRFCSIGNVAL_LOW_WATER_CROSSING = 78;
    TSRTRFCSIGNVAL_RESERVED = 79;
    TSRTRFCSIGNVAL_INVALID = 255;
  }
  enum HMAStsType {
    HMA_STS_OFF = 0;
    HMA_STS_PASSIVE = 1;
    HMA_STS_ACTIVE = 2;
    HMA_STS_TEMPORARY_FAULT = 3;
    HMA_STS_PERMANENTLY_FAULT = 4;
    HMA_STS_SUPPRESSION_CONDITION = 5;
    HMA_STS_LOWTRANSFORM_SUPPRESSION = 6;
    HMA_STS_HIGHTRANSFORM_SUPPRESSION = 7;
  }
  enum HMAHibeamReqType {
    HMA_HIBEAMREQ_HIGH_BEAM_REQUEST_OFF = 0;
    HMA_HIBEAMREQ_HIGH_BEAM_REQUEST_ON = 1;
    HMA_HIBEAMREQ_HIGH_BEAM_AND_LASER_BEAM_REQUEST_ON = 2;
    HMA_HIBEAMREQ_RESERVED = 3;
  }
  enum IDCL2TurnLightReqType {
    IDC_L2_TURNLIGHTREQ_TURN_OFF = 0;
    IDC_L2_TURNLIGHTREQ_TURN_LEFT_ON = 1;
    IDC_L2_TURNLIGHTREQ_TURN_RIGHT_ON = 2;
    IDC_L2_TURNLIGHTREQ_TURN_OFF_LIGHT_SW = 3;
  }
  enum IDCL2FWiperReqType {
    IDC_L2_FWIPERREQ_WIPER_OFF = 0;
    IDC_L2_FWIPERREQ_WIPER_AUTO_OR_INTERMITTENT = 1;
    IDC_L2_FWIPERREQ_LOW_SPEED = 2;
    IDC_L2_FWIPERREQ_HIGH_SPEED = 3;
    IDC_L2_FWIPERREQ_RESERVED = 4;
    IDC_L2_FWIPERREQ_ERROR = 7;
  }
  enum IDCL2FWshrReqType {
    IDC_L2_FWSHRREQ_OFF = 0;
    IDC_L2_FWSHRREQ_WIPER_WASH = 1;
    IDC_L2_FWSHRREQ_WASH_IN_AUTO_MODE = 2;
    IDC_L2_FWSHRREQ_RESERVED = 3;
  }
  enum HWATakOverReqType {
    HWA_TAKOVER_REQ_DEFAULT = 0;
    HWA_TAKOVER_REQ_STEER_WHEEL_REQ = 1;
    HWA_TAKOVER_REQ_TAKEOVER_REQ = 2;
    HWA_TAKOVER_REQ_RESERVED = 3;
  }
  enum HWATakOverTrigType {
    HWA_TAKOVER_TRIG_DEFAULT = 0;
    HWA_TAKOVER_TRIG_OFF_RAMP_FAIL = 1;
    HWA_TAKOVER_TRIG_TO_MAINROAD_FAIL = 2;
    HWA_TAKOVER_TRIG_TURNING_FAIL = 3;
    HWA_TAKOVER_TRIG_TOLL_GATE_FAIL = 4;
    HWA_TAKOVER_TRIG_ROUNDABOUT_FAIL = 5;
    HWA_TAKOVER_TRIG_TO_SLOW_STOP = 6;
    HWA_TAKOVER_TRIG_RESERVED = 7;
  }
  enum HWAWarnInfoDispType {
    HWA_WARNINFODISP_NO_DISPLAY = 0;
    HWA_WARNINFODISP_CONSTRUCTION_SITE_AHEAD = 1;
    HWA_WARNINFODISP_LARGE_VEHICLES_WARN = 2;
    HWA_WARNINFODISP_NIGHT_WARNING = 3;
    HWA_WARNINFODISP_ADVERSE_WEATHER_WARNING = 4;
    HWA_WARNINFODISP_RESTART_TO_ACTIVATE_HIPILOT = 5;
    HWA_WARNINFODISP_CAMERA_BLOCKED = 6;
    HWA_WARNINFODISP_AUTO_STORE_TO_DEBUG_HIPILOT = 7;
    HWA_WARNINFODISP_CONFIRM_LEFT_LANE_CHANGE = 8;
    HWA_WARNINFODISP_CONFIRM_RIGHT_LANE_CHANGE = 9;
    HWA_WARNINFODISP_TRY_ALC_LATER = 10;
    HWA_WARNINFODISP_LANE_CHANGE_IS_CANCELLED_BECAUSE_OF_RISK = 11;
    HWA_WARNINFODISP_LANE_CHANGE_ABORTED = 12;
    HWA_WARNINFODISP_PLEASE_KEEP_ATTENTION_DISTRACTION = 13;
    HWA_WARNINFODISP_FASTEN_COPILOT_SEATBELT = 14;
    HWA_WARNINFODISP_CURVE_DRIVING_PLEASE_HOLD_THE_STEERING_WHEEL = 15;
  }
  enum NOHWarnInfoDispType {
    NOH_WARNINFODISP_DEFAULT = 0;
    NOH_WARNINFODISP_AUTO_STORE_TO_DEBUG_NOH = 1;
    NOH_WARNINFODISP_RESTART_TO_ACTIVATE_NOH = 2;
    NOH_WARNINFODISP_OFF_RAMP_FAIL = 3;
    NOH_WARNINFODISP_TO_MAINROAD_FAIL = 4;
    NOH_WARNINFODISP_TURNING_FAIL = 5;
    NOH_WARNINFODISP_TOLL_GATE_FAIL = 6;
    NOH_WARNINFODISP_ROUNDABOUT_FAIL = 7;
    NOH_WARNINFODISP_REROUTE = 8;
    NOH_WARNINFODISP_LANE_CHANGE_MANUALLY = 9;
    NOH_WARNINFODISP_NOH_RESUMED = 10;
    NOH_WARNINFODISP_VEH_MERGE_WARN = 11;
    NOH_WARNINFODISP_RAMP_OFF_WARN = 12;
    NOH_WARNINFODISP_GPS_FAIL = 13;
    NOH_WARNINFODISP_LANE_MARK_BLURRED = 14;
    NOH_WARNINFODISP_TO_DRIVE_OUT_OF_MAIN_ROAD = 15;
  }
  enum HWAALCTrigType {
    HWA_ALC_TRIG_NO_DISPLAY = 0;
    HWA_ALC_TRIG_SLOW_VEHICLE_AHEAD = 1;
    HWA_ALC_TRIG_RESERVED = 2;
    HWA_ALC_TRIG_RESERVED_3 = 3;
  }
  enum MRCInterSysInfoDispType {
    MRC_INTERSYSINFODISP_DEFAULT = 0;
    MRC_INTERSYSINFODISP_L_CR_FAIL = 1;
    MRC_INTERSYSINFODISP_R_CR_FAIL = 2;
    MRC_INTERSYSINFODISP_L_RSDS_FAIL = 3;
    MRC_INTERSYSINFODISP_R_RSDS_FAIL = 4;
    MRC_INTERSYSINFODISP_RADAR_OR_CAM_FAIL = 5;
    MRC_INTERSYSINFODISP_RESERVED = 96;
  }
  enum MRCTakOverReqType {
    MRC_TAKOVER_REQ_NO_DISPLAY = 0;
    MRC_TAKOVER_REQ_SLOW_STOP = 1;
    MRC_TAKOVER_REQ_ROADSIDE_STOP = 2;
    MRC_TAKOVER_REQ_EMERGENT_STOP = 3;
  }
  enum HWAInhibitWarnType {
    HWA_INHIBITWARN_DEFAULT = 0;
    HWA_INHIBITWARN_ENVIRONMENT_INHIBIT = 1;
    HWA_INHIBITWARN_ATTENTION = 2;
    HWA_INHIBITWARN_HANDSON = 3;
    HWA_INHIBITWARN_SEATBELT = 4;
    HWA_INHIBITWARN_CLOSE_DOOR = 5;
    HWA_INHIBITWARN_DMS_OFF = 6;
    HWA_INHIBITWARN_RESERVE = 7;
  }
  enum TSRTrafficLightPosnType {
    TSR_TRAFFIC_LIGHT_POSN_NO_DISPLAY = 0;
    TSR_TRAFFIC_LIGHT_POSN_FRONT = 1;
    TSR_TRAFFIC_LIGHT_POSN_LEFT = 2;
    TSR_TRAFFIC_LIGHT_POSN_RIGHT = 3;
  }
  enum StopMarkDetnType {
    STOP_MARK_DETN_NOT_DECTECTED = 0;
    STOP_MARK_DETN_DECTECTED = 1;
    STOP_MARK_DETN_RESERVED = 2;
    STOP_MARK_DETN_INVALID = 3;
  }
  enum ZebraMarkDetnType {
    ZEBRA_MARK_DETN_NOT_DECTECTED = 0;
    ZEBRA_MARK_DETN_DECTECTED = 1;
    ZEBRA_MARK_DETN_RESERVED = 2;
    ZEBRA_MARK_DETN_INVALID = 3;
  }
  enum GridMarkDetnType {
    GRID_MARK_DETN_NOT_DECTECTED = 0;
    GRID_MARK_DETN_DECTECTED = 1;
    GRID_MARK_DETN_RESERVED = 2;
    GRID_MARK_DETN_INVALID = 3;
  }
  enum UsrManStsRespType {
    USRMANSTS_RESP_OFF = 0;
    USRMANSTS_RESP_PAGE_1 = 1;
    USRMANSTS_RESP_PAGE_2 = 2;
    USRMANSTS_RESP_PAGE_3 = 3;
    USRMANSTS_RESP_PAGE_4 = 4;
    USRMANSTS_RESP_PAGE_5 = 5;
    USRMANSTS_RESP_PAGE_6 = 6;
    USRMANSTS_RESP_PAGE_7 = 7;
    USRMANSTS_RESP_PAGE_8 = 8;
    USRMANSTS_RESP_PAGE_9 = 9;
    USRMANSTS_RESP_RESERVED = 160;
  }
  enum TSRTrafficLightType {
    TSR_TRAFFIC_LIGHT_NO_DISPLAY = 0;
    TSR_TRAFFIC_LIGHT_GREENLIGHT = 1;
    TSR_TRAFFIC_LIGHT_REDLIGHT = 2;
    TSR_TRAFFIC_LIGHT_YELLOWLIGHT = 3;
  }
  enum TSRTrafficLightThroughType {
    TSR_TRAFFIC_LIGHT_THROUGH_NO_DISPLAY = 0;
    TSR_TRAFFIC_LIGHT_THROUGH_GREENLIGHT = 1;
    TSR_TRAFFIC_LIGHT_THROUGH_REDLIGHT = 2;
    TSR_TRAFFIC_LIGHT_THROUGH_YELLOWLIGHT = 3;
  }
  enum TSRTrafficLightLeType {
    TSR_TRAFFIC_LIGHT_LE_NO_DISPLAY = 0;
    TSR_TRAFFIC_LIGHT_LE_GREENLIGHT = 1;
    TSR_TRAFFIC_LIGHT_LE_REDLIGHT = 2;
    TSR_TRAFFIC_LIGHT_LE_YELLOWLIGHT = 3;
  }
  enum TSRTrafficLightRiType {
    TSR_TRAFFIC_LIGHT_RI_NO_DISPLAY = 0;
    TSR_TRAFFIC_LIGHT_RI_GREENLIGHT = 1;
    TSR_TRAFFIC_LIGHT_RI_REDLIGHT = 2;
    TSR_TRAFFIC_LIGHT_RI_YELLOWLIGHT = 3;
  }
  enum LCModeSwtRespType {
    LCMODE_SWT_RESP_NORMAL = 0;
    LCMODE_SWT_RESP_SPORT = 1;
    LCMODE_SWT_RESP_COMFORT = 2;
    LCMODE_SWT_RESP_RESERVE = 3;
  }
  enum TSRTFL1LightTypeType {
    TSR_TFL1_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL1_LIGHTTYPE_S = 1;
    TSR_TFL1_LIGHTTYPE_LT = 2;
    TSR_TFL1_LIGHTTYPE_RT = 3;
    TSR_TFL1_LIGHTTYPE_TA = 4;
    TSR_TFL1_LIGHTTYPE_SLT = 5;
    TSR_TFL1_LIGHTTYPE_SRT = 6;
    TSR_TFL1_LIGHTTYPE_SLRT = 7;
    TSR_TFL1_LIGHTTYPE_STA = 8;
    TSR_TFL1_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL1_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL1_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRConSpdLimType {
    TSRCONSPDLIM_NO_DISPLAY = 0;
    TSRCONSPDLIM_SPL_5 = 1;
    TSRCONSPDLIM_SPL_10 = 2;
    TSRCONSPDLIM_SPL_15 = 3;
    TSRCONSPDLIM_SPL_20 = 4;
    TSRCONSPDLIM_SPL_25 = 5;
    TSRCONSPDLIM_SPL_30 = 6;
    TSRCONSPDLIM_SPL_35 = 7;
    TSRCONSPDLIM_SPL_40 = 8;
    TSRCONSPDLIM_SPL_45 = 9;
    TSRCONSPDLIM_SPL_50 = 10;
    TSRCONSPDLIM_SPL_55 = 11;
    TSRCONSPDLIM_SPL_60 = 12;
    TSRCONSPDLIM_SPL_65 = 13;
    TSRCONSPDLIM_SPL_70 = 14;
    TSRCONSPDLIM_SPL_75 = 15;
    TSRCONSPDLIM_SPL_80 = 16;
    TSRCONSPDLIM_SPL_85 = 17;
    TSRCONSPDLIM_SPL_90 = 18;
    TSRCONSPDLIM_SPL_95 = 19;
    TSRCONSPDLIM_SPL_100 = 20;
    TSRCONSPDLIM_SPL_105 = 21;
    TSRCONSPDLIM_SPL_110 = 22;
    TSRCONSPDLIM_SPL_115 = 23;
    TSRCONSPDLIM_SPL_120 = 24;
    TSRCONSPDLIM_SPL_125 = 25;
    TSRCONSPDLIM_SPL_130 = 26;
    TSRCONSPDLIM_SPL_135 = 27;
    TSRCONSPDLIM_SPL_140 = 28;
    TSRCONSPDLIM_SPL_145 = 29;
    TSRCONSPDLIM_SPL_150 = 30;
    TSRCONSPDLIM_RESERVED = 31;
    TSRCONSPDLIM_INVALID = 63;
  }
  enum TSRTFL2LightColorType {
    TSR_TFL2_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL2_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL2_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL2_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL2_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL2_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL2_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL2_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL2LightTypeType {
    TSR_TFL2_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL2_LIGHTTYPE_S = 1;
    TSR_TFL2_LIGHTTYPE_LT = 2;
    TSR_TFL2_LIGHTTYPE_RT = 3;
    TSR_TFL2_LIGHTTYPE_TA = 4;
    TSR_TFL2_LIGHTTYPE_SLT = 5;
    TSR_TFL2_LIGHTTYPE_SRT = 6;
    TSR_TFL2_LIGHTTYPE_SLRT = 7;
    TSR_TFL2_LIGHTTYPE_STA = 8;
    TSR_TFL2_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL2_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL2_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRTFL3LightColorType {
    TSR_TFL3_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL3_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL3_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL3_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL3_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL3_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL3_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL3_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL3LightTypeType {
    TSR_TFL3_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL3_LIGHTTYPE_S = 1;
    TSR_TFL3_LIGHTTYPE_LT = 2;
    TSR_TFL3_LIGHTTYPE_RT = 3;
    TSR_TFL3_LIGHTTYPE_TA = 4;
    TSR_TFL3_LIGHTTYPE_SLT = 5;
    TSR_TFL3_LIGHTTYPE_SRT = 6;
    TSR_TFL3_LIGHTTYPE_SLRT = 7;
    TSR_TFL3_LIGHTTYPE_STA = 8;
    TSR_TFL3_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL3_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL3_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRTFL4LightColorType {
    TSR_TFL4_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL4_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL4_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL4_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL4_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL4_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL4_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL4_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL4LightTypeType {
    TSR_TFL4_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL4_LIGHTTYPE_S = 1;
    TSR_TFL4_LIGHTTYPE_LT = 2;
    TSR_TFL4_LIGHTTYPE_RT = 3;
    TSR_TFL4_LIGHTTYPE_TA = 4;
    TSR_TFL4_LIGHTTYPE_SLT = 5;
    TSR_TFL4_LIGHTTYPE_SRT = 6;
    TSR_TFL4_LIGHTTYPE_SLRT = 7;
    TSR_TFL4_LIGHTTYPE_STA = 8;
    TSR_TFL4_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL4_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL4_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRTFL5LightColorType {
    TSR_TFL5_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL5_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL5_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL5_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL5_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL5_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL5_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL5_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL5LightTypeType {
    TSR_TFL5_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL5_LIGHTTYPE_S = 1;
    TSR_TFL5_LIGHTTYPE_LT = 2;
    TSR_TFL5_LIGHTTYPE_RT = 3;
    TSR_TFL5_LIGHTTYPE_TA = 4;
    TSR_TFL5_LIGHTTYPE_SLT = 5;
    TSR_TFL5_LIGHTTYPE_SRT = 6;
    TSR_TFL5_LIGHTTYPE_SLRT = 7;
    TSR_TFL5_LIGHTTYPE_STA = 8;
    TSR_TFL5_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL5_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL5_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRTFL6LightTypeType {
    TSR_TFL6_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL6_LIGHTTYPE_S = 1;
    TSR_TFL6_LIGHTTYPE_LT = 2;
    TSR_TFL6_LIGHTTYPE_RT = 3;
    TSR_TFL6_LIGHTTYPE_TA = 4;
    TSR_TFL6_LIGHTTYPE_SLT = 5;
    TSR_TFL6_LIGHTTYPE_SRT = 6;
    TSR_TFL6_LIGHTTYPE_SLRT = 7;
    TSR_TFL6_LIGHTTYPE_STA = 8;
    TSR_TFL6_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL6_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL6_LIGHTTYPE_RESERVED = 11;
  }
  enum TSRTFL1LightColorType {
    TSR_TFL1_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL1_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL1_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL1_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL1_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL1_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL1_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL1_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL7LightColorType {
    TSR_TFL7_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL7_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL7_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL7_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL7_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL7_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL7_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL7_LIGHTCOLOR_RESERVED = 7;
  }
  enum TSRTFL7LightTypeType {
    TSR_TFL7_LIGHTTYPE_NO_WARNING = 0;
    TSR_TFL7_LIGHTTYPE_S = 1;
    TSR_TFL7_LIGHTTYPE_LT = 2;
    TSR_TFL7_LIGHTTYPE_RT = 3;
    TSR_TFL7_LIGHTTYPE_TA = 4;
    TSR_TFL7_LIGHTTYPE_SLT = 5;
    TSR_TFL7_LIGHTTYPE_SRT = 6;
    TSR_TFL7_LIGHTTYPE_SLRT = 7;
    TSR_TFL7_LIGHTTYPE_STA = 8;
    TSR_TFL7_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_LEFT = 9;
    TSR_TFL7_LIGHTTYPE_RED_LIGHT_ALLOW_TO_TURN_RIGHT = 10;
    TSR_TFL7_LIGHTTYPE_RESERVED = 11;
  }
  enum TraLightWarnInfoDispType {
    TRALIGHTWARNINFODISP_NO_TRAFFIC_LIGHT = 0;
    TRALIGHTWARNINFODISP_LEFT_TURN = 1;
    TRALIGHTWARNINFODISP_STRAIGHT = 2;
    TRALIGHTWARNINFODISP_RIGHT_TURN = 3;
    TRALIGHTWARNINFODISP_WARNING = 4;
    TRALIGHTWARNINFODISP_TURN_ROUND_WARNING = 5;
    TRALIGHTWARNINFODISP_RUN_ARED_LIGHT = 6;
    TRALIGHTWARNINFODISP_STOP_ABOUT_TO_CROSS_THE_LINE = 7;
    TRALIGHTWARNINFODISP_SLOW_DOWN_THE_YELLOW_LIGHT = 8;
    TRALIGHTWARNINFODISP_ATTENTION_YELLOW_LIGHT = 9;
    TRALIGHTWARNINFODISP_TRAFFIC_LIGHT_OBSERVATION_LIMITED = 10;
    TRALIGHTWARNINFODISP_ROUNDABOUT = 11;
  }
  enum TraLightRmdInfoDispType {
    TRALIGHTRMDINFODISP_NO_DISPLAY = 0;
    TRALIGHTRMDINFODISP_TLC_ACTIVATED = 1;
    TRALIGHTRMDINFODISP_TLC_DEACTIVATED = 2;
    TRALIGHTRMDINFODISP_STOPPING_BY_YELLOW_LIGHT = 3;
    TRALIGHTRMDINFODISP_STOPPING_BY_RED_LIGHT = 4;
    TRALIGHTRMDINFODISP_LAUNCHING_BY_GREEN_LIGHT = 5;
    TRALIGHTRMDINFODISP_STEPPING_INTO_WAITING_AREA = 6;
    TRALIGHTRMDINFODISP_GOING_STRAIGHT = 7;
    TRALIGHTRMDINFODISP_GOING_STRAIGHT_PAY_ATTENTION_PLS = 8;
    TRALIGHTRMDINFODISP_LEFT_TURNING = 9;
    TRALIGHTRMDINFODISP_LEFT_TURNING_PAY_ATTENTION_PLS = 10;
    TRALIGHTRMDINFODISP_RIGHT_TURNING = 11;
    TRALIGHTRMDINFODISP_RIGHT_TURNING_PAY_ATTENTION_PLS = 12;
    TRALIGHTRMDINFODISP_TURNING_AROUND_PAY_ATTENTION_PLS = 13;
    TRALIGHTRMDINFODISP_WARNING = 14;
    TRALIGHTRMDINFODISP_TAKE_OVER_PLS = 15;
    TRALIGHTRMDINFODISP_RESERVED = 16;
  }
  enum TSRTFLEgoVehType {
    TSR_TFL_EGOVEH_NO_WARNING = 0;
    TSR_TFL_EGOVEH_ONE = 1;
    TSR_TFL_EGOVEH_TWO = 2;
    TSR_TFL_EGOVEH_THREE = 3;
    TSR_TFL_EGOVEH_FOUR = 4;
    TSR_TFL_EGOVEH_FIVE = 5;
    TSR_TFL_EGOVEH_SIX = 6;
    TSR_TFL_EGOVEH_SEVEN = 7;
    TSR_TFL_EGOVEH_EIGHT = 8;
    TSR_TFL_EGOVEH_RESERVED = 9;
  }
  enum TSRTFL6LightColorType {
    TSR_TFL6_LIGHTCOLOR_NO_WARNING = 0;
    TSR_TFL6_LIGHTCOLOR_RED_LIGHT = 1;
    TSR_TFL6_LIGHTCOLOR_GREEN_LIGHT = 2;
    TSR_TFL6_LIGHTCOLOR_YELLOW_LIGHT = 3;
    TSR_TFL6_LIGHTCOLOR_RED_LIGHT_FLASH = 4;
    TSR_TFL6_LIGHTCOLOR_GREEN_LIGHT_FLASH = 5;
    TSR_TFL6_LIGHTCOLOR_YELLOW_LIGHT_FLASH = 6;
    TSR_TFL6_LIGHTCOLOR_RESERVED = 7;
  }
  enum NavDecActiveType {
    NAVDECACTIVE_INACTIVE = 0;
    NAVDECACTIVE_NAVDEC_ACTIVE = 1;
    NAVDECACTIVE_NAVDEC_TAKE_OVER_WARNING = 2;
    NAVDECACTIVE_SPL_60_NAVDEC_TAKE_OVER_WARNING = 3;
    NAVDECACTIVE_SPL_70_NAVDEC_TAKE_OVER_WARNING = 4;
    NAVDECACTIVE_SPL_80_NAVDEC_TAKE_OVER_WARNING = 5;
    NAVDECACTIVE_SPL_90_NAVDEC_TAKE_OVER_WARNING = 6;
    NAVDECACTIVE_SPL_100_NAVDEC_TAKE_OVER_WARNING = 7;
    NAVDECACTIVE_SPL_110_NAVDEC_TAKE_OVER_WARNING = 8;
    NAVDECACTIVE_SPL_120_NAVDEC_TAKE_OVER_WARNING = 9;
    NAVDECACTIVE_SET_SPEED_OVER_NAVDEC = 10;
    NAVDECACTIVE_RESERVED = 11;
  }
  enum LRSideCamaErrStsType {
    LR_SIDECAMAERRSTS_NO_ERROR = 0;
    LR_SIDECAMAERRSTS_ERROR = 1;
    LR_SIDECAMAERRSTS_BLINDNESS = 2;
    LR_SIDECAMAERRSTS_RESERVED = 3;
  }
  enum RRSideCamaErrStsType {
    RR_SIDECAMAERRSTS_NO_ERROR = 0;
    RR_SIDECAMAERRSTS_ERROR = 1;
    RR_SIDECAMAERRSTS_BLINDNESS = 2;
    RR_SIDECAMAERRSTS_RESERVED = 3;
  }
  enum TraLightCtrlStsType {
    TRALIGHTCTRLSTS_NO_DISPLAY = 0;
    TRALIGHTCTRLSTS_OFF = 1;
    TRALIGHTCTRLSTS_PASSIVE = 2;
    TRALIGHTCTRLSTS_STANDBY = 3;
    TRALIGHTCTRLSTS_ACTIVE = 4;
    TRALIGHTCTRLSTS_FAULT = 5;
    TRALIGHTCTRLSTS_RESERVED = 6;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_ifc7 = 1;
  // [] [0|1] [initial_value:0]
  optional bool tsr_resp = 2;
  // [] [0|3] [initial_value:1]
  optional TSRSpdLimUnitType tsr_spd_lim_unit = 3;
  // [] [0|63] [initial_value:0]
  optional TSRSpdLimType tsr_spd_lim = 4;
  // [] [0|3] [initial_value:0]
  optional TSRSpdLimCfdcType tsr_spd_lim_cfdc = 5;
  // [] [0|7] [initial_value:0]
  optional TSRStsType tsr_sts = 6;
  // [] [0|3] [initial_value:0]
  optional TSRSpdLimStsType tsr_spd_lim_sts = 7;
  // [] [0|3] [initial_value:0]
  optional TSRTrfcSignValCfdcType tsr_trfc_sign_val_cfdc = 8;
  // [] [0|255] [initial_value:0]
  optional TSRTrfcSignValType tsr_trfc_sign_val = 9;
  // [] [0|1] [initial_value:0]
  optional bool tsr_spd_lim_warn = 10;
  // [] [0|1] [initial_value:0]
  optional bool tsr_warn_resp = 11;
  // [km/h] [-15|15] [initial_value:15]
  optional int32 tsr_snvty = 12;
  // [] [0|7] [initial_value:0]
  optional HMAStsType hma_sts = 13;
  // [] [0|3] [initial_value:0]
  optional HMAHibeamReqType hma_hibeam_req = 14;
  // [] [0|1] [initial_value:0]
  optional bool idc_l2_emgy_light_req = 15;
  // [] [0|3] [initial_value:0]
  optional IDCL2TurnLightReqType idc_l2_turn_light_req = 16;
  // [] [0|7] [initial_value:0]
  optional IDCL2FWiperReqType idc_l2_f_wiper_req = 17;
  // [] [0|3] [initial_value:0]
  optional IDCL2FWshrReqType idc_l2_f_wshr_req = 18;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool tsr_spd_lim_warn_au = 19;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_ifc7 = 20;
  // [] [0|3] [initial_value:0]
  optional HWATakOverReqType hwa_tak_over_req = 21;
  // [] [0|7] [initial_value:0]
  optional HWATakOverTrigType hwa_tak_over_trig = 22;
  // [] [0|15] [initial_value:0]
  optional HWAWarnInfoDispType hwa_warn_info_disp = 23;
  // [] [0|1] [initial_value:0]
  optional bool ifc_line01_source = 24;
  // [] [0|1] [initial_value:0]
  optional bool ifc_line02_source = 25;
  // [] [0|15] [initial_value:0]
  optional NOHWarnInfoDispType noh_warn_info_disp = 26;
  // [] [0|1] [initial_value:0]
  optional bool alc_swt_req_resp = 27;
  // [] [0|3] [initial_value:0]
  optional HWAALCTrigType hwa_alc_trig = 28;
  // [] [0|15] [initial_value:0]
  optional MRCInterSysInfoDispType mrc_inter_sys_info_disp = 29;
  // [] [0|3] [initial_value:0]
  optional MRCTakOverReqType mrc_tak_over_req = 30;
  // [] [0|7] [initial_value:0]
  optional HWAInhibitWarnType hwa_inhibit_warn = 31;
  // [] [0|3] [initial_value:0]
  optional TSRTrafficLightPosnType tsr_traffic_light_posn = 32;
  // [] [0|3] [initial_value:0]
  optional StopMarkDetnType stop_mark_detn = 33;
  // [m] [0|150] [initial_value:0]
  optional double stop_mark_dx = 34;
  // [] [0|3] [initial_value:0]
  optional ZebraMarkDetnType zebra_mark_detn = 35;
  // [] [0|3] [initial_value:0]
  optional GridMarkDetnType grid_mark_detn = 36;
  // [] [0|1] [initial_value:0]
  optional bool lane_chng_cfm_swt_resp = 37;
  // [] [0|1] [initial_value:0]
  optional bool auto_spd_set_swt_resp = 38;
  // [] [0|1] [initial_value:0]
  optional bool voice_brdc_swt_resp = 39;
  // [] [0|15] [initial_value:0]
  optional UsrManStsRespType usr_man_sts_resp = 40;
  // [m] [0|150] [initial_value:0]
  optional double grid_mark_dx = 41;
  // [m] [0|150] [initial_value:0]
  optional double zebra_mark_dx = 42;
  // [] [0|3] [initial_value:0]
  optional TSRTrafficLightType tsr_traffic_light = 43;
  // [] [0|3] [initial_value:0]
  optional TSRTrafficLightThroughType tsr_traffic_light_through = 44;
  // [] [0|3] [initial_value:0]
  optional TSRTrafficLightLeType tsr_traffic_light_le = 45;
  // [] [0|3] [initial_value:0]
  optional TSRTrafficLightRiType tsr_traffic_light_ri = 46;
  // [] [0|1] [initial_value:0]
  optional bool rain_mode_swt_resp = 47;
  // [] [0|3] [initial_value:0]
  optional LCModeSwtRespType lc_mode_swt_resp = 48;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl1_light_time = 49;
  // [] [0|15] [initial_value:0]
  optional TSRTFL1LightTypeType tsr_tfl1_light_type = 50;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl2_light_time = 51;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl3_light_time = 52;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl4_light_time = 53;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl5_light_time = 54;
  // [] [0|63] [initial_value:0]
  optional TSRConSpdLimType tsr_con_spd_lim = 55;
  // [] [0|1] [initial_value:0]
  optional bool tsr_con_spd_lim_warn = 56;
  // [] [0|8] [initial_value:0]
  optional TSRTFL2LightColorType tsr_tfl2_light_color = 57;
  // [] [0|15] [initial_value:0]
  optional TSRTFL2LightTypeType tsr_tfl2_light_type = 58;
  // [] [0|7] [initial_value:0]
  optional TSRTFL3LightColorType tsr_tfl3_light_color = 59;
  // [] [0|15] [initial_value:0]
  optional TSRTFL3LightTypeType tsr_tfl3_light_type = 60;
  // [] [0|7] [initial_value:0]
  optional TSRTFL4LightColorType tsr_tfl4_light_color = 61;
  // [] [0|15] [initial_value:0]
  optional TSRTFL4LightTypeType tsr_tfl4_light_type = 62;
  // [] [0|7] [initial_value:0]
  optional TSRTFL5LightColorType tsr_tfl5_light_color = 63;
  // [] [0|15] [initial_value:0]
  optional TSRTFL5LightTypeType tsr_tfl5_light_type = 64;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl6_light_time = 65;
  // [] [0|15] [initial_value:0]
  optional TSRTFL6LightTypeType tsr_tfl6_light_type = 66;
  // [] [0|7] [initial_value:0]
  optional TSRTFL1LightColorType tsr_tfl1_light_color = 67;
  // [s] [0|255] [initial_value:0]
  optional int32 tsr_tfl7_light_time = 68;
  // [] [0|7] [initial_value:0]
  optional TSRTFL7LightColorType tsr_tfl7_light_color = 69;
  // [] [0|15] [initial_value:0]
  optional TSRTFL7LightTypeType tsr_tfl7_light_type = 70;
  // [] [0|15] [initial_value:0]
  optional TraLightWarnInfoDispType tra_light_warn_info_disp = 71;
  // [] [0|31] [initial_value:0]
  optional TraLightRmdInfoDispType tra_light_rmd_info_disp = 72;
  // [] [0|15] [initial_value:0]
  optional TSRTFLEgoVehType tsr_tfl_ego_veh = 73;
  // [] [0|1] [initial_value:0]
  optional bool tra_light_swt_resp = 74;
  // [] [0|7] [initial_value:0]
  optional TSRTFL6LightColorType tsr_tfl6_light_color = 75;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool nav_dec_swt_resp = 76;
  // [NoUnit] [0|15] [initial_value:0]
  optional NavDecActiveType nav_dec_active = 77;
  // [NoUnit] [0|7] [initial_value:0]
  optional LRSideCamaErrStsType lr_side_cama_err_sts = 78;
  // [NoUnit] [0|7] [initial_value:0]
  optional RRSideCamaErrStsType rr_side_cama_err_sts = 79;
  // [NoUnit] [0|7] [initial_value:0]
  optional TraLightCtrlStsType tra_light_ctrl_sts = 80;
}

message PRDCM1_2D5 {
// Report Message
  enum PassRearDoorWinPosnStsPercType {
    PASSREARDOORWINPOSNSTS_PERC_UNKNOWN = 0;
    PASSREARDOORWINPOSNSTS_PERC_CLOSE = 1;
    PASSREARDOORWINPOSNSTS_PERC_OPEN = 2;
    PASSREARDOORWINPOSNSTS_PERC_5_OPEN = 3;
    PASSREARDOORWINPOSNSTS_PERC_10_OPEN = 4;
    PASSREARDOORWINPOSNSTS_PERC_15_OPEN = 5;
    PASSREARDOORWINPOSNSTS_PERC_20_OPEN = 6;
    PASSREARDOORWINPOSNSTS_PERC_25_OPEN = 7;
    PASSREARDOORWINPOSNSTS_PERC_30_OPEN = 8;
    PASSREARDOORWINPOSNSTS_PERC_35_OPEN = 9;
    PASSREARDOORWINPOSNSTS_PERC_40_OPEN = 10;
    PASSREARDOORWINPOSNSTS_PERC_45_OPEN = 11;
    PASSREARDOORWINPOSNSTS_PERC_50_OPEN = 12;
    PASSREARDOORWINPOSNSTS_PERC_55_OPEN = 13;
    PASSREARDOORWINPOSNSTS_PERC_60_OPEN = 14;
    PASSREARDOORWINPOSNSTS_PERC_65_OPEN = 15;
    PASSREARDOORWINPOSNSTS_PERC_70_OPEN = 16;
    PASSREARDOORWINPOSNSTS_PERC_75_OPEN = 17;
    PASSREARDOORWINPOSNSTS_PERC_80_OPEN = 18;
    PASSREARDOORWINPOSNSTS_PERC_85_OPEN = 19;
    PASSREARDOORWINPOSNSTS_PERC_90_OPEN = 20;
    PASSREARDOORWINPOSNSTS_PERC_95_OPEN = 21;
    PASSREARDOORWINPOSNSTS_PERC_RESERVED = 22;
  }
  // [NoUnit] [0|31] [initial_value:0]
  optional PassRearDoorWinPosnStsPercType pass_rear_door_win_posn_sts_perc = 1;
}

message VCU_FD4_2D6 {
// Report Message
  enum VCURdyLEDStsType {
    VCU_RDYLEDSTS_OFF = 0;
    VCU_RDYLEDSTS_ON = 1;
    VCU_RDYLEDSTS_FLASHING = 2;
    VCU_RDYLEDSTS_RESERVED = 3;
  }
  // [NoUnit] [0|1] [initial_value:0]
  optional bool wtr_air_coolr_pmp_flt_sts = 1;
  // description / [No Unit] [0|3] [initial_value:0]
  optional VCURdyLEDStsType vcu_rdy_led_sts = 2;
  // description / [DegCgrd] [-40|130] [initial_value:0]
  optional int32 vcu_lt_radr_outl_wtr_temp = 3;
  // description / [Perc] [0|100] [initial_value:0]
  optional int32 vcu_wtr_pmp_open_rate_ctrl = 4;
  // description / [Km] [0|999] [initial_value:0]
  optional int32 vcu_resr_drv_rng = 5;
}

message HUT23_2D8 {
// Report Message
  enum ADASStubMsgTypType {
    ADAS_STUB_MSGTYP_SYSTEM_SPECIFIC = 0;
    ADAS_STUB_MSGTYP_POSITION = 1;
    ADAS_STUB_MSGTYP_SEGMENT = 2;
    ADAS_STUB_MSGTYP_STUB = 3;
    ADAS_STUB_MSGTYP_PROFILE_SHORT = 4;
    ADAS_STUB_MSGTYP_PROFILE_LONG = 5;
    ADAS_STUB_MSGTYP_META_DATA = 6;
    ADAS_STUB_MSGTYP_RESERVED = 7;
  }
  enum ADASStubFuncRoadClassType {
    ADAS_STUB_FUNCROADCLASS_UNKOWN = 0;
    ADAS_STUB_FUNCROADCLASS_EXPRESSWAY = 1;
    ADAS_STUB_FUNCROADCLASS_CITY_EXPRESSWAY = 2;
    ADAS_STUB_FUNCROADCLASS_NATIONAL_ROAD = 3;
    ADAS_STUB_FUNCROADCLASS_PROVINCIAL_RODE_OR_COUNTY_ROAD = 4;
    ADAS_STUB_FUNCROADCLASS_CITY_OR_VILLAGE_ROAD = 5;
    ADAS_STUB_FUNCROADCLASS_OTHER_ROAD = 6;
    ADAS_STUB_FUNCROADCLASS_INVALID = 7;
  }
  enum ADASStubPartOfCalcRouteType {
    ADAS_STUB_PARTOFCALCROUTE_NEW_PATH_FROM_THIS_POINT_ON_IS_NOT_PART_OF_CALCULATED_ROUTE = 0;
    ADAS_STUB_PARTOFCALCROUTE_NEW_PATH_FROM_THIS_POINT_ON_IS_PART_OF_CALCULATED_ROUTE = 1;
    ADAS_STUB_PARTOFCALCROUTE_UNKNOWN = 2;
    ADAS_STUB_PARTOFCALCROUTE_INVALID = 3;
  }
  enum ADASStubCmplxInsctType {
    ADAS_STUB_CMPLXINSCT_NEW_PATH_IS_NOT_PART_OF_COMPLEX_INTERSECTION = 0;
    ADAS_STUB_CMPLXINSCT_NEW_PATH_IS_PART_OF_A_COMPLEX_INTERSECTION = 1;
    ADAS_STUB_CMPLXINSCT_UNKNOWN = 2;
    ADAS_STUB_CMPLXINSCT_INVALID = 3;
  }
  enum ADASStubFormOfWayType {
    ADAS_STUB_FORMOFWAY_UNKOWN = 0;
    ADAS_STUB_FORMOFWAY_FREEWAY_OR_CONTROLLED_ACCESS_ROAD_THAT_IS_NOT_A_SLIP_ROAD_RAMP = 1;
    ADAS_STUB_FORMOFWAY_MULTIPLE_CARRIAGEWAY_OR_MULTIPLY_DIGITIZED_ROAD = 2;
    ADAS_STUB_FORMOFWAY_SINGLE_CARRIAGEWAY_DEFAULT = 3;
    ADAS_STUB_FORMOFWAY_ROUNDABOUT_CIRCLE = 4;
    ADAS_STUB_FORMOFWAY_TRAFFIC_SQUARE_SPECIAL_TRAFFIC_FIGURE = 5;
    ADAS_STUB_FORMOFWAY_RESERVED = 6;
    ADAS_STUB_FORMOFWAY_PARALLEL_ROAD_AS_SPECIAL_TYPE_OF_A_SLIP_ROAD_RAMP = 8;
    ADAS_STUB_FORMOFWAY_SLIP_ROAD_RAMP_ON_A_FREEWAY_OR_CONTROLLED_ACCESS_ROAD = 9;
    ADAS_STUB_FORMOFWAY_SLIP_ROAD_RAMP_NOT_ON_A_FREEWAY_OR_CONTROLLED_ACCESS_ROAD = 10;
    ADAS_STUB_FORMOFWAY_SERVICE_ROAD_OR_FRONTAGE_ROAD = 11;
    ADAS_STUB_FORMOFWAY_ENTRANCE_TO_OR_EXIT_OF_A_CAR_PARK = 12;
    ADAS_STUB_FORMOFWAY_ENTRANCE_TO_OR_EXIT_TO_SERVICE = 13;
    ADAS_STUB_FORMOFWAY_PEDESTRIAN_ZONE = 14;
    ADAS_STUB_FORMOFWAY_UNKNOW = 15;
  }
  enum ADASStubRtOfWayType {
    ADAS_STUB_RTOFWAY_DRVG_TOWARDS_THIS_STUB_HAS_PRIORITY_OVER_ALL_CONFLICTING_VEHICLE_MOVEMENTS = 0;
    ADAS_STUB_RTOFWAY_DRVG_TOWARDS_THIS_STUB_MIGHT_REQUIRE_YIELDING_TO_CONFLICTING_VEHICLE_MOVEMENTS = 1;
    ADAS_STUB_RTOFWAY_UNKNOWN = 2;
    ADAS_STUB_RTOFWAY_INVALID = 3;
  }
  enum ADASStubNumOfLaneOppDirType {
    ADAS_STUB_NUMOFLANEOPPDIR_ONE_DIRECTION_ROAD = 0;
    ADAS_STUB_NUMOFLANEOPPDIR_ONE_LANE_ONLY = 1;
    ADAS_STUB_NUMOFLANEOPPDIR_INDICATES_2_OR_MORE_LANES = 2;
    ADAS_STUB_NUMOFLANEOPPDIR_INVALID = 3;
  }
  enum ADASStubNumOfLaneDrvDirType {
    ADAS_STUB_NUMOFLANEDRVDIR_ONE_DIRECTION_ROAD_DRIVEN_IN_WRONG_DIRECTION = 0;
    ADAS_STUB_NUMOFLANEDRVDIR_VERBATIM_VALUES = 1;
    ADAS_STUB_NUMOFLANEDRVDIR_6_OR_MORE_LANES = 6;
    ADAS_STUB_NUMOFLANEDRVDIR_INVALID = 7;
  }
  // [] [0|7] [initial_value:0]
  optional ADASStubMsgTypType adas_stub_msg_typ = 1;
  // [m] [0|8190] [initial_value:0]
  optional int32 adas_stub_offset = 2;
  // [] [0|3] [initial_value:0]
  optional int32 adas_stub_cyc_cnt = 3;
  // [] [0|63] [initial_value:0]
  optional int32 adas_stub_path_idx = 4;
  // [] [0|63] [initial_value:0]
  optional int32 adas_stub_stub_path_idx = 5;
  // [] [0|1] [initial_value:0]
  optional bool adas_stub_retr = 6;
  // [] [0|1] [initial_value:0]
  optional bool adas_stub_update = 7;
  // [] [0|100] [initial_value:0]
  optional double adas_stub_rel_probb = 8;
  // [] [0|7] [initial_value:0]
  optional ADASStubFuncRoadClassType adas_stub_func_road_class = 9;
  // [] [0|3] [initial_value:0]
  optional ADASStubPartOfCalcRouteType adas_stub_part_of_calc_route = 10;
  // [] [0|3] [initial_value:0]
  optional ADASStubCmplxInsctType adas_stub_cmplx_insct = 11;
  // [] [0|15] [initial_value:0]
  optional ADASStubFormOfWayType adas_stub_form_of_way = 12;
  // [] [0|358.583] [initial_value:0]
  optional double adas_stub_turn_angl = 13;
  // [] [0|1] [initial_value:0]
  optional bool adas_stub_last_stub = 14;
  // [] [0|3] [initial_value:0]
  optional ADASStubRtOfWayType adas_stub_rt_of_way = 15;
  // [] [0|3] [initial_value:0]
  optional ADASStubNumOfLaneOppDirType adas_stub_num_of_lane_opp_dir = 16;
  // [] [0|7] [initial_value:0]
  optional ADASStubNumOfLaneDrvDirType adas_stub_num_of_lane_drv_dir = 17;
}

message HUT22_2DA {
// Report Message
  enum ADASSegMsgTypType {
    ADAS_SEG_MSGTYP_SYSTEM_SPECIFIC = 0;
    ADAS_SEG_MSGTYP_POSITION = 1;
    ADAS_SEG_MSGTYP_SEGMENT = 2;
    ADAS_SEG_MSGTYP_STUB = 3;
    ADAS_SEG_MSGTYP_PROFILE_SHORT = 4;
    ADAS_SEG_MSGTYP_PROFILE_LONG = 5;
    ADAS_SEG_MSGTYP_META_DATA = 6;
    ADAS_SEG_MSGTYP_RESERVED = 7;
  }
  enum ADASSegTunnelType {
    ADAS_SEG_TUNNEL_SEGMENT_IS_NOT_PART_OF_A_TUNNEL = 0;
    ADAS_SEG_TUNNEL_SEGMENT_IS_PART_OF_A_TUNNEL = 1;
    ADAS_SEG_TUNNEL_UNKNOWN = 2;
    ADAS_SEG_TUNNEL_INVALID = 3;
  }
  enum ADASSegBrdgType {
    ADAS_SEG_BRDG_SEGMENT_IS_NOT_PART_OF_A_BRIDGE = 0;
    ADAS_SEG_BRDG_SEGMENT_IS_PART_OF_A_BRIDGE = 1;
    ADAS_SEG_BRDG_UNKNOWN = 2;
    ADAS_SEG_BRDG_INVALID = 3;
  }
  enum ADASSegBuildUpAreaType {
    ADAS_SEG_BUILDUPAREA_SEGMENT_IS_NOT_PART_OF_A_BUILT_UP_AREA = 0;
    ADAS_SEG_BUILDUPAREA_SEGMENT_IS_PART_OF_A_BUILT_UP_AREA = 1;
    ADAS_SEG_BUILDUPAREA_UNKNOWN = 2;
    ADAS_SEG_BUILDUPAREA_INVALID = 3;
  }
  enum ADASSegFuncRoadClassType {
    ADAS_SEG_FUNCROADCLASS_UNKOWN = 0;
    ADAS_SEG_FUNCROADCLASS_EXPRESSWAY = 1;
    ADAS_SEG_FUNCROADCLASS_CITY_EXPRESSWAY = 2;
    ADAS_SEG_FUNCROADCLASS_NATIONAL_ROAD = 3;
    ADAS_SEG_FUNCROADCLASS_PROVINCIAL_RODE_OR_COUNTY_ROAD = 4;
    ADAS_SEG_FUNCROADCLASS_CITY_OR_VILLAGE_ROAD = 5;
    ADAS_SEG_FUNCROADCLASS_OTHER_ROAD = 6;
    ADAS_SEG_FUNCROADCLASS_INVALID = 7;
  }
  enum ADASSegPartOfCalcRouteType {
    ADAS_SEG_PARTOFCALCROUTE_SEGMENT_IS_NOT_PART_OF_CALCULATED_ROUTE = 0;
    ADAS_SEG_PARTOFCALCROUTE_SEGMENT_IS_PART_OF_CALCULATED_ROUTE = 1;
    ADAS_SEG_PARTOFCALCROUTE_UNKNOWN = 2;
    ADAS_SEG_PARTOFCALCROUTE_INVALID = 3;
  }
  enum ADASSegCmplxInsctType {
    ADAS_SEG_CMPLXINSCT_SEGMENT_IS_NOT_PART_OF_COMPLEX_INTERSECTION = 0;
    ADAS_SEG_CMPLXINSCT_SEGMENT_IS_PART_OF_A_COMPLEX_INTERSECTION = 1;
    ADAS_SEG_CMPLXINSCT_UNKNOWN = 2;
    ADAS_SEG_CMPLXINSCT_INVALID = 3;
  }
  enum ADASSegFormOfWayType {
    ADAS_SEG_FORMOFWAY_UNKOWN = 0;
    ADAS_SEG_FORMOFWAY_FREEWAY_OR_CONTROLLED_ACCESS_ROAD_THAT_IS_NOT_A_SLIP_ROAD_RAMP = 1;
    ADAS_SEG_FORMOFWAY_MULTIPLE_CARRIAGEWAY_OR_MULTIPLY_DIGITIZED_ROAD = 2;
    ADAS_SEG_FORMOFWAY_SINGLE_CARRIAGEWAY_DEFAULT = 3;
    ADAS_SEG_FORMOFWAY_ROUNDABOUT_CIRCLE = 4;
    ADAS_SEG_FORMOFWAY_TRAFFIC_SQUARE_SPECIAL_TRAFFIC_FIGURE = 5;
    ADAS_SEG_FORMOFWAY_RESERVED = 6;
    ADAS_SEG_FORMOFWAY_PARALLEL_ROAD_AS_SPECIAL_TYPE_OF_A_SLIP_ROAD_RAMP = 8;
    ADAS_SEG_FORMOFWAY_SLIP_ROAD_RAMP_ON_A_FREEWAY_OR_CONTROLLED_ACCESS_ROAD = 9;
    ADAS_SEG_FORMOFWAY_SLIP_ROAD_RAMP_NOT_ON_A_FREEWAY_OR_CONTROLLED_ACCESS_ROAD = 10;
    ADAS_SEG_FORMOFWAY_SERVICE_ROAD_OR_FRONTAGE_ROAD = 11;
    ADAS_SEG_FORMOFWAY_ENTRANCE_TO_OR_EXIT_OF_A_CAR_PARK = 12;
    ADAS_SEG_FORMOFWAY_ENTRANCE_TO_OR_EXIT_TO_SERVICE = 13;
    ADAS_SEG_FORMOFWAY_PEDESTRIAN_ZONE = 14;
    ADAS_SEG_FORMOFWAY_UNKNOW = 15;
  }
  enum ADASSegEffSpdLmtType {
    ADAS_SEG_EFFSPDLMT_UNKNOWN_INCLUDING_VARIABLE_SPEED_SIGN_CONTROLLED = 0;
    ADAS_SEG_EFFSPDLMT_VALUE_A1_DC_5_KM_H_MPH = 1;
    ADAS_SEG_EFFSPDLMT_5_VALUE_A1_DC_7_KM_H_MPH = 2;
    ADAS_SEG_EFFSPDLMT_7_VALUE_A1_DC_10_KM_H_MPH = 3;
    ADAS_SEG_EFFSPDLMT_10_VALUE_A1_DC_15_KM_H_MPH = 4;
    ADAS_SEG_EFFSPDLMT_15_VALUE_A1_DC_20_KM_H_MPH = 5;
    ADAS_SEG_EFFSPDLMT_20_VALUE_A1_DC_25_KM_H_MPH = 6;
    ADAS_SEG_EFFSPDLMT_25_VALUE_A1_DC_30_KM_H_MPH = 7;
    ADAS_SEG_EFFSPDLMT_30_VALUE_A1_DC_35_KM_H_MPH = 8;
    ADAS_SEG_EFFSPDLMT_35_VALUE_A1_DC_40_KM_H_MPH = 9;
    ADAS_SEG_EFFSPDLMT_40_VALUE_A1_DC_45_KM_H_MPH = 10;
    ADAS_SEG_EFFSPDLMT_45_VALUE_A1_DC_50_KM_H_MPH = 11;
    ADAS_SEG_EFFSPDLMT_50_VALUE_A1_DC_55_KM_H_MPH = 12;
    ADAS_SEG_EFFSPDLMT_55_VALUE_A1_DC_60_KM_H_MPH = 13;
    ADAS_SEG_EFFSPDLMT_60_VALUE_A1_DC_65_KM_H_MPH = 14;
    ADAS_SEG_EFFSPDLMT_65_VALUE_A1_DC_70_KM_H_MPH = 15;
    ADAS_SEG_EFFSPDLMT_70_VALUE_A1_DC_75_KM_H_MPH = 16;
    ADAS_SEG_EFFSPDLMT_75_VALUE_A1_DC_80_KM_H_MPH = 17;
    ADAS_SEG_EFFSPDLMT_80_VALUE_A1_DC_85_KM_H_MPH = 18;
    ADAS_SEG_EFFSPDLMT_85_VALUE_A1_DC_90_KM_H_MPH = 19;
    ADAS_SEG_EFFSPDLMT_90_VALUE_A1_DC_95_KM_H_MPH = 20;
    ADAS_SEG_EFFSPDLMT_95_VALUE_A1_DC_100_KM_H_MPH = 21;
    ADAS_SEG_EFFSPDLMT_100_VALUE_A1_DC_105_KM_H_MPH = 22;
    ADAS_SEG_EFFSPDLMT_105_VALUE_A1_DC_110_KM_H_MPH = 23;
    ADAS_SEG_EFFSPDLMT_110_VALUE_A1_DC_115_KM_H_MPH = 24;
    ADAS_SEG_EFFSPDLMT_115_VALUE_A1_DC_120_KM_H_MPH = 25;
    ADAS_SEG_EFFSPDLMT_120_VALUE_A1_DC_130_KM_H_MPH = 26;
    ADAS_SEG_EFFSPDLMT_130_VALUE_A1_DC_140_KM_H_MPH = 27;
    ADAS_SEG_EFFSPDLMT_140_VALUE_A1_DC_150_KM_H_MPH = 28;
    ADAS_SEG_EFFSPDLMT_150_VALUE_UNLIMITED = 29;
    ADAS_SEG_EFFSPDLMT_UNLIMITED = 30;
    ADAS_SEG_EFFSPDLMT_N_A = 31;
  }
  enum ADASSegEffSpdLmtTypType {
    ADAS_SEG_EFFSPDLMTTYP_IMPLICIT_FOR_INSTANCE_DEFAULT_SPEED_LIMIT_IN_THE_CITIES = 0;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_ON_TRAFFIC_SIGN = 1;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_BY_NIGHT = 2;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_BY_DAY = 3;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_TIME_OF_DAY = 4;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_ELECTRONIC_EYE = 5;
    ADAS_SEG_EFFSPDLMTTYP_EXPLICIT_A8C_SNOW = 6;
    ADAS_SEG_EFFSPDLMTTYP_UNKNOWN = 7;
  }
  enum ADASSegDivideRoadType {
    ADAS_SEG_DIVIDEROAD_SEGMENT_IS_NOT_PART_OF_A_DIVIDED_ROAD_DUAL_CARRIAGEWAY = 0;
    ADAS_SEG_DIVIDEROAD_SEGMENT_IS_PART_OF_A_DIVIDED_ROAD_DUAL_CARRIAGEWAY = 1;
    ADAS_SEG_DIVIDEROAD_UNKNOWN = 2;
    ADAS_SEG_DIVIDEROAD_N_A = 3;
  }
  enum ADASSegNumOfLaneOppDirType {
    ADAS_SEG_NUMOFLANEOPPDIR_ONE_DIRECTION_ROAD = 0;
    ADAS_SEG_NUMOFLANEOPPDIR_ONE_LANE_ONLY = 1;
    ADAS_SEG_NUMOFLANEOPPDIR_INDICATES_2_OR_MORE_LANES = 2;
    ADAS_SEG_NUMOFLANEOPPDIR_INVALID = 3;
  }
  enum ADASSegNumOfLaneDrvDirType {
    ADAS_SEG_NUMOFLANEDRVDIR_ONE_DIRECTION_ROAD_DRIVEN_IN_WRONG_DIRECTION = 0;
    ADAS_SEG_NUMOFLANEDRVDIR_VERBATIM_VALUES = 1;
    ADAS_SEG_NUMOFLANEDRVDIR_SIX_OR_MORE_LANES = 6;
    ADAS_SEG_NUMOFLANEDRVDIR_INVALID = 7;
  }
  // [] [0|7] [initial_value:0]
  optional ADASSegMsgTypType adas_seg_msg_typ = 1;
  // [m] [0|8190] [initial_value:0]
  optional int32 adas_seg_offset = 2;
  // [] [0|3] [initial_value:0]
  optional int32 adas_seg_cyc_cnt = 3;
  // [NoUnit] [0|63] [initial_value:0]
  optional int32 adas_seg_path_idx = 4;
  // [] [0|3] [initial_value:0]
  optional ADASSegTunnelType adas_seg_tunnel = 5;
  // [] [0|3] [initial_value:0]
  optional ADASSegBrdgType adas_seg_brdg = 6;
  // [] [0|3] [initial_value:0]
  optional ADASSegBuildUpAreaType adas_seg_build_up_area = 7;
  // [] [0|1] [initial_value:0]
  optional bool adas_seg_retr = 8;
  // [] [0|1] [initial_value:0]
  optional bool adas_seg_update = 9;
  // [] [0|100] [initial_value:0]
  optional double adas_seg_rel_probb = 10;
  // [] [0|7] [initial_value:0]
  optional ADASSegFuncRoadClassType adas_seg_func_road_class = 11;
  // [] [0|3] [initial_value:0]
  optional ADASSegPartOfCalcRouteType adas_seg_part_of_calc_route = 12;
  // [] [0|3] [initial_value:0]
  optional ADASSegCmplxInsctType adas_seg_cmplx_insct = 13;
  // [] [0|15] [initial_value:0]
  optional ADASSegFormOfWayType adas_seg_form_of_way = 14;
  // [] [0|31] [initial_value:0]
  optional ADASSegEffSpdLmtType adas_seg_eff_spd_lmt = 15;
  // [] [0|7] [initial_value:0]
  optional ADASSegEffSpdLmtTypType adas_seg_eff_spd_lmt_typ = 16;
  // [] [0|3] [initial_value:0]
  optional ADASSegDivideRoadType adas_seg_divide_road = 17;
  // [] [0|3] [initial_value:0]
  optional ADASSegNumOfLaneOppDirType adas_seg_num_of_lane_opp_dir = 18;
  // [] [0|7] [initial_value:0]
  optional ADASSegNumOfLaneDrvDirType adas_seg_num_of_lane_drv_dir = 19;
}

message HUT21_2DD {
// Report Message
  enum ADASProfShortMsgTypType {
    ADAS_PROFSHORT_MSGTYP_SYSTEM_SPECIFIC = 0;
    ADAS_PROFSHORT_MSGTYP_POSITION = 1;
    ADAS_PROFSHORT_MSGTYP_SEGMENT = 2;
    ADAS_PROFSHORT_MSGTYP_STUB = 3;
    ADAS_PROFSHORT_MSGTYP_PROFILE_SHORT = 4;
    ADAS_PROFSHORT_MSGTYP_PROFILE_LONG = 5;
    ADAS_PROFSHORT_MSGTYP_META_DATA = 6;
    ADAS_PROFSHORT_MSGTYP_RESERVED = 7;
  }
  enum ADASProfShortProfTypType {
    ADAS_PROFSHORT_PROFTYP_UNKNOW = 0;
    ADAS_PROFSHORT_PROFTYP_CURVATURE = 1;
    ADAS_PROFSHORT_PROFTYP_ROUTE_NUMBER_TYPES = 2;
    ADAS_PROFSHORT_PROFTYP_SLOPE_A3_A8STEP_A3_A9 = 3;
    ADAS_PROFSHORT_PROFTYP_SLOPE_A3_A8LINER_A3_A9 = 4;
    ADAS_PROFSHORT_PROFTYP_ROAD_ACCESSIBILITY = 5;
    ADAS_PROFSHORT_PROFTYP_ROAD_CONDITION = 6;
    ADAS_PROFSHORT_PROFTYP_VARIABLE_SPEED_SIGN_POSITION = 7;
    ADAS_PROFSHORT_PROFTYP_HEADING_CHANGE = 8;
    ADAS_PROFSHORT_PROFTYP_RESERVED_FOR_STANDARD_TYPES = 9;
    ADAS_PROFSHORT_PROFTYP_RESERVED_FOR_SYSTEM_SPECIFIC_TYPES = 16;
    ADAS_PROFSHORT_PROFTYP_HISTORY_AVERAGE_SPEED = 18;
    ADAS_PROFSHORT_PROFTYP_TRAFFIC_FLOW = 19;
    ADAS_PROFSHORT_PROFTYP_RESERVED_FOR_SYSTEM_SPECIFIC_TYPES_20 = 20;
  }
  enum ADASProfShortAccurClassType {
    ADAS_PROFSHORT_ACCURCLASS_HIEST_ACCURACY = 0;
    ADAS_PROFSHORT_ACCURCLASS_MEDIUM_ACCURACY = 1;
    ADAS_PROFSHORT_ACCURCLASS_LOWEST_ACCURACY = 2;
    ADAS_PROFSHORT_ACCURCLASS_UNKNOWN = 3;
  }
  enum ADASProfShortDist1Type {
    ADAS_PROFSHORT_DIST1_NO_OTHER_PROFILE_VALUES_ARE_DEFINED_IN_THIS_MESSAGE = 0;
    ADAS_PROFSHORT_DIST1_VALID_VALUE = 1;
    ADAS_PROFSHORT_DIST1_INVALID = 1023;
  }
  // [] [0|7] [initial_value:0]
  optional ADASProfShortMsgTypType adas_prof_short_msg_typ = 1;
  // [m] [0|8190] [initial_value:0]
  optional int32 adas_prof_short_offset = 2;
  // [] [0|3] [initial_value:0]
  optional int32 adas_prof_short_cyc_cnt = 3;
  // [] [0|63] [initial_value:0]
  optional int32 adas_prof_short_path_idx = 4;
  // [] [0|31] [initial_value:0]
  optional ADASProfShortProfTypType adas_prof_short_prof_typ = 5;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_short_ctrl_point = 6;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_short_retr = 7;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_short_update = 8;
  // [] [0|3] [initial_value:3]
  optional ADASProfShortAccurClassType adas_prof_short_accur_class = 9;
  // [m] [0|1022] [initial_value:0]
  optional int32 adas_prof_short_dist1 = 10;
  // [] [0|1022] [initial_value:0]
  optional int32 adas_prof_short_value0 = 11;
  // [] [0|1022] [initial_value:0]
  optional int32 adas_prof_short_value1 = 12;
}

message HUT20_2DE {
// Report Message
  enum ADASProfLongMsgTypType {
    ADAS_PROFLONG_MSGTYP_SYSTEM_SPECIFIC = 0;
    ADAS_PROFLONG_MSGTYP_POSITION = 1;
    ADAS_PROFLONG_MSGTYP_SEGMENT = 2;
    ADAS_PROFLONG_MSGTYP_STUB = 3;
    ADAS_PROFLONG_MSGTYP_PROFILE_SHORT = 4;
    ADAS_PROFLONG_MSGTYP_PROFILE_LONG = 5;
    ADAS_PROFLONG_MSGTYP_META_DATA = 6;
    ADAS_PROFLONG_MSGTYP_RESERVED = 7;
  }
  enum ADASProfLongProfTypType {
    ADAS_PROFLONG_PROFTYP_UNKOWN = 0;
    ADAS_PROFLONG_PROFTYP_LONGITUDE = 1;
    ADAS_PROFLONG_PROFTYP_LATITUDE = 2;
    ADAS_PROFLONG_PROFTYP_ALTITUDE = 3;
    ADAS_PROFLONG_PROFTYP_CONTROL_POINT_LONGITUDE = 4;
    ADAS_PROFLONG_PROFTYP_CONTROL_POINT_LATITUDE = 5;
    ADAS_PROFLONG_PROFTYP_CONTROL_POINT_ALTITUDE = 6;
    ADAS_PROFLONG_PROFTYP_LINK_IDENTIFIER = 7;
    ADAS_PROFLONG_PROFTYP_TRAFFIC_SIGN = 8;
    ADAS_PROFLONG_PROFTYP_TRUCK_SPEED_LIMITS = 9;
    ADAS_PROFLONG_PROFTYP_EXTENDED_LANE = 10;
    ADAS_PROFLONG_PROFTYP_RESERVED_FOR_STANDARD_TYPES = 11;
    ADAS_PROFLONG_PROFTYP_RESERVED_FOR_SYSTEM_SPECIFIC_TYPES = 16;
    ADAS_PROFLONG_PROFTYP_TRAFFIC_EVENT = 19;
    ADAS_PROFLONG_PROFTYP_WEATHER = 20;
    ADAS_PROFLONG_PROFTYP_RESERVED_FOR_SYSTEM_SPECIFIC_TYPES_21 = 21;
  }
  // [] [0|7] [initial_value:0]
  optional ADASProfLongMsgTypType adas_prof_long_msg_typ = 1;
  // [m] [0|8190] [initial_value:0]
  optional int32 adas_prof_long_offset = 2;
  // [] [0|3] [initial_value:0]
  optional int32 adas_prof_long_cyc_cnt = 3;
  // [] [0|63] [initial_value:0]
  optional int32 adas_prof_long_path_idx = 4;
  // [] [0|31] [initial_value:0]
  optional ADASProfLongProfTypType adas_prof_long_prof_typ = 5;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_long_ctrl_point = 6;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_long_retr = 7;
  // [] [0|1] [initial_value:0]
  optional bool adas_prof_long_update = 8;
  // [] [0|4294967295] [initial_value:0]
  optional int32 adas_prof_long_value = 9;
}

message DVR_FD1_2EC {
// Report Message
  enum DVRStsType {
    DVRSTS_DVR_NORMAL_OPERATION = 0;
    DVRSTS_DVR_ERROR_OPERATION = 1;
    DVRSTS_TF1_IS_NOT_INSET = 2;
    DVRSTS_TF1_IS_FAULT = 3;
    DVRSTS_TF1_IS_FULL = 4;
    DVRSTS_TF1_IS_WRITING_SLOW = 5;
    DVRSTS_TF2_IS_NOT_INSET = 6;
    DVRSTS_TF2_IS_FAULT = 7;
    DVRSTS_TF2_IS_FULL = 8;
    DVRSTS_TF2_IS_WRITING_SLOW = 9;
    DVRSTS_RESERVED = 10;
  }
  // [] [0|1] [initial_value:0]
  optional bool dvr_360_video_capture_req = 1;
  // [] [0|15] [initial_value:0]
  optional DVRStsType dvr_sts = 2;
}

message BCM19_30F {
// Report Message
  enum WiprReqType {
    WIPRREQ_WIPER_OFF = 0;
    WIPRREQ_WIPER_ACTION_ONCE = 1;
    WIPRREQ_SPEED1_LOW = 2;
    WIPRREQ_SPEED2_HIGH = 3;
    WIPRREQ_RESERVED = 4;
    WIPRREQ_SIGNAL_NOT_AVAILABLE = 7;
  }
  // [] [0|7] [initial_value:0]
  optional WiprReqType wipr_req = 1;
}

message MDC_FD1_312 {
// Control Message
  // [NoUnit] [0|1] [initial_value:0]
  optional bool mdc_thm_post_run = 1;
  // [NoUnit] [0|4] [initial_value:2]
  optional double mdc_flow_req = 2;
  // [DegCgrd] [-50|204] [initial_value:255]
  optional int32 mdc_max_temp = 3;
  // [DegCgrd] [-50|204] [initial_value:255]
  optional int32 mdc_aver_temp = 4;
}

message BCM1_319 {
// Report Message
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_bcm1 = 1;
  // [] [0|1] [initial_value:0]
  optional bool drv_door_sts = 2;
  // [] [0|1] [initial_value:0]
  optional bool lr_door_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool passenger_door_sts = 4;
  // [] [0|1] [initial_value:0]
  optional bool rr_door_sts = 5;
  // [] [0|1] [initial_value:0]
  optional bool trunk_sts = 6;
  // [] [0|1] [initial_value:0]
  optional bool windshld_wipr_actv_sts = 7;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool rls_fail_sts = 8;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_bcm1 = 9;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_bcm1 = 10;
}

message VCU5_31B {
// Report Message
  enum VCUSysFltLvlType {
    VCU_SYSFLTLVL_NO_FAULT = 0;
    VCU_SYSFLTLVL_LEVEL_1 = 1;
    VCU_SYSFLTLVL_LEVEL_2 = 2;
    VCU_SYSFLTLVL_LEVEL_3 = 3;
    VCU_SYSFLTLVL_LEVEL_4 = 4;
    VCU_SYSFLTLVL_LEVEL_5 = 5;
    VCU_SYSFLTLVL_RESERVED = 6;
  }
  // description / [No Unit] [0|7] [initial_value:0]
  optional VCUSysFltLvlType vcu_sys_flt_lvl = 1;
}

message OBC_FD2_31F {
// Report Message
  enum OBCCCLineStsType {
    OBC_CCLINESTS_NOT_CONNECTED = 0;
    OBC_CCLINESTS_CONNECTED = 1;
    OBC_CCLINESTS_HALF_CONNECTION = 2;
    OBC_CCLINESTS_ERROR = 3;
  }
  // description B [NoUnit] [0|3] [initial_value:0]
  optional OBCCCLineStsType obc_cc_line_sts = 1;
}

message T_BOX_FD6_33D {
// Report Message
  enum TBOXAVPActvReqType {
    T_BOX_AVPACTVREQ_NO_REQUEST = 0;
    T_BOX_AVPACTVREQ_REQUEST_HAVP = 1;
    T_BOX_AVPACTVREQ_REQUEST_PAVP = 2;
    T_BOX_AVPACTVREQ_RESERVED = 3;
  }
  // [] [0|3] [initial_value:0]
  optional TBOXAVPActvReqType t_box_avp_actv_req = 1;
}

message TPMS1_341 {
// Report Message
  enum FLTirePressIndStsType {
    FLTIREPRESSINDSTS_NORMAL = 0;
    FLTIREPRESSINDSTS_LOW = 1;
    FLTIREPRESSINDSTS_RESERVED = 2;
    FLTIREPRESSINDSTS_HIGH = 3;
    FLTIREPRESSINDSTS_NOT_AVAILABLE = 4;
    FLTIREPRESSINDSTS_RESERVED_5 = 5;
  }
  enum FRTirePressIndStsType {
    FRTIREPRESSINDSTS_NORMAL = 0;
    FRTIREPRESSINDSTS_LOW = 1;
    FRTIREPRESSINDSTS_RESERVED = 2;
    FRTIREPRESSINDSTS_HIGH = 3;
    FRTIREPRESSINDSTS_NOT_AVAILABLE = 4;
    FRTIREPRESSINDSTS_RESERVED_5 = 5;
  }
  enum TirePressSysFailrIndcnType {
    TIREPRESSSYSFAILRINDCN_NO_FAILURE = 0;
    TIREPRESSSYSFAILRINDCN_FAILURE = 1;
    TIREPRESSSYSFAILRINDCN_RESERVED = 2;
  }
  enum RLTirePressIndStsType {
    RLTIREPRESSINDSTS_NORMAL = 0;
    RLTIREPRESSINDSTS_LOW = 1;
    RLTIREPRESSINDSTS_RESERVED = 2;
    RLTIREPRESSINDSTS_HIGH = 3;
    RLTIREPRESSINDSTS_NOT_AVAILABLE = 4;
    RLTIREPRESSINDSTS_RESERVED_5 = 5;
  }
  enum RRTirePressIndStsType {
    RRTIREPRESSINDSTS_NORMAL = 0;
    RRTIREPRESSINDSTS_LOW = 1;
    RRTIREPRESSINDSTS_RESERVED = 2;
    RRTIREPRESSINDSTS_HIGH = 3;
    RRTIREPRESSINDSTS_NOT_AVAILABLE = 4;
    RRTIREPRESSINDSTS_RESERVED_5 = 5;
  }
  enum FLTireTempStsType {
    FLTIRETEMPSTS_NORMAL = 0;
    FLTIRETEMPSTS_HIGH = 1;
    FLTIRETEMPSTS_NOT_AVAILABLE = 2;
    FLTIRETEMPSTS_RESERVED = 3;
  }
  enum FRTireTempStsType {
    FRTIRETEMPSTS_NORMAL = 0;
    FRTIRETEMPSTS_HIGH = 1;
    FRTIRETEMPSTS_NOT_AVAILABLE = 2;
    FRTIRETEMPSTS_RESERVED = 3;
  }
  enum RLTireTempStsType {
    RLTIRETEMPSTS_NORMAL = 0;
    RLTIRETEMPSTS_HIGH = 1;
    RLTIRETEMPSTS_NOT_AVAILABLE = 2;
    RLTIRETEMPSTS_RESERVED = 3;
  }
  enum RRTireTempStsType {
    RRTIRETEMPSTS_NORMAL = 0;
    RRTIRETEMPSTS_HIGH = 1;
    RRTIRETEMPSTS_NOT_AVAILABLE = 2;
    RRTIRETEMPSTS_RESERVED = 3;
  }
  enum TPMSAutoLoctStsType {
    TPMS_AUTOLOCTSTS_AUTO_LEARN_OR_AUTO_LOCATE_NOT_IN_PROGRESS_TPMS_IS_BASE_SYSTEM = 0;
    TPMS_AUTOLOCTSTS_AUTO_LEARN_IN_PROGRESS = 1;
    TPMS_AUTOLOCTSTS_AUTO_LOCATE_IN_PROGRESS_AUTO_LEARN_COMPLETE = 2;
    TPMS_AUTOLOCTSTS_AUTO_LOCATE_COMPLETE = 3;
    TPMS_AUTOLOCTSTS_AUTO_LEARN_TIMEOUT = 4;
    TPMS_AUTOLOCTSTS_AUTO_LOCATE_TIMEOUT = 5;
    TPMS_AUTOLOCTSTS_RESERVED = 6;
  }
  // [] [0|7] [initial_value:0]
  optional FLTirePressIndStsType fl_tire_press_ind_sts = 1;
  // [] [0|7] [initial_value:0]
  optional FRTirePressIndStsType fr_tire_press_ind_sts = 2;
  // [] [0|3] [initial_value:0]
  optional TirePressSysFailrIndcnType tire_press_sys_failr_indcn = 3;
  // [] [0|7] [initial_value:0]
  optional RLTirePressIndStsType rl_tire_press_ind_sts = 4;
  // [] [0|7] [initial_value:0]
  optional RRTirePressIndStsType rr_tire_press_ind_sts = 5;
  // [] [0|3] [initial_value:0]
  optional FLTireTempStsType fl_tire_temp_sts = 6;
  // [] [0|3] [initial_value:0]
  optional FRTireTempStsType fr_tire_temp_sts = 7;
  // [] [0|3] [initial_value:0]
  optional RLTireTempStsType rl_tire_temp_sts = 8;
  // [] [0|3] [initial_value:0]
  optional RRTireTempStsType rr_tire_temp_sts = 9;
  // [] [0|5] [initial_value:0]
  optional TPMSAutoLoctStsType tpms_auto_loct_sts = 10;
  // [NoUnit] [0|65535] [initial_value:0]
  optional int32 freshness_value_tpms1 = 11;
}

message BCM3_345 {
// Report Message
  enum EnvModDetnType {
    ENVMODDETN_DAY = 0;
    ENVMODDETN_NIGHT = 1;
    ENVMODDETN_TUNNEL = 2;
    ENVMODDETN_DARKSTART = 3;
  }
  enum HiLowBeamStsType {
    HILOWBEAMSTS_OFF = 0;
    HILOWBEAMSTS_ON = 1;
    HILOWBEAMSTS_ERROR = 2;
    HILOWBEAMSTS_RESERVED = 3;
  }
  // [] [0|1] [initial_value:0]
  optional bool hazard_lmp_swt_sts = 1;
  // [] [0|1] [initial_value:0]
  optional bool drv_door_lock_sts = 2;
  // description / [NoUnit] [0|3] [initial_value:0]
  optional EnvModDetnType env_mod_detn = 3;
  // [NoUnit] [0|2] [initial_value:0]
  optional HiLowBeamStsType hi_low_beam_sts = 4;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_bcm3 = 5;
}

message HUT16_348 {
// Report Message
  enum SentinelModeHUTType {
    SENTINELMODE_HUT_NO_ACTION = 0;
    SENTINELMODE_HUT_OFF = 1;
    SENTINELMODE_HUT_ON = 2;
    SENTINELMODE_HUT_RESERVED = 3;
  }
  // [Nounit] [0|3] [initial_value:0]
  optional SentinelModeHUTType sentinel_mode_hut = 1;
}

message ABM1_351 {
// Report Message
  enum CrashOutputStsType {
    CRASHOUTPUTSTS_NO_CRASH = 0;
    CRASHOUTPUTSTS_RESERVED = 1;
    CRASHOUTPUTSTS_FRONT_FIRST_LEVEL = 2;
    CRASHOUTPUTSTS_REAR_CRASH_RESERVED = 3;
    CRASHOUTPUTSTS_RESERVED_4 = 4;
    CRASHOUTPUTSTS_RIGHT_HAND_SIDE = 8;
    CRASHOUTPUTSTS_RESERVED_9 = 9;
    CRASHOUTPUTSTS_LEFT_HAND_SIDE = 16;
    CRASHOUTPUTSTS_ROLLOVER_RESERVED = 17;
    CRASHOUTPUTSTS_STATIC_ROLL_RESERVED = 18;
    CRASHOUTPUTSTS_RESERVED_19 = 19;
  }
  enum PassSBRVisualType {
    PASSSBR_VISUAL_LAMP_OFF = 0;
    PASSSBR_VISUAL_LAMP_ON = 1;
    PASSSBR_VISUAL_RESERVED = 2;
    PASSSBR_VISUAL_LAMP_BLINK_SOUND_REMIND = 3;
  }
  enum DrvSBRVisualType {
    DRVSBR_VISUAL_LAMP_OFF = 0;
    DRVSBR_VISUAL_LAMP_ON = 1;
    DRVSBR_VISUAL_RESERVED = 2;
    DRVSBR_VISUAL_LAMP_BLINK_SOUND_REMIND = 3;
  }
  enum SecRowLSBRVisualType {
    SECROWLSBR_VISUAL_LAMP_OFF = 0;
    SECROWLSBR_VISUAL_LAMP_ON = 1;
    SECROWLSBR_VISUAL_RESERVED = 2;
    SECROWLSBR_VISUAL_LAMP_BLINK_SOUND_REMIND = 3;
  }
  enum SecRowMidSBRVisualType {
    SECROWMIDSBR_VISUAL_LAMP_OFF = 0;
    SECROWMIDSBR_VISUAL_LAMP_ON = 1;
    SECROWMIDSBR_VISUAL_RESERVED = 2;
    SECROWMIDSBR_VISUAL_LAMP_BLINK_SOUND_REMIND = 3;
  }
  enum SecRowRSBRVisualType {
    SECROWRSBR_VISUAL_LAMP_OFF = 0;
    SECROWRSBR_VISUAL_LAMP_ON = 1;
    SECROWRSBR_VISUAL_RESERVED = 2;
    SECROWRSBR_VISUAL_LAMP_BLINK_SOUND_REMIND = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_abm1 = 1;
  // [] [0|1] [initial_value:1]
  optional bool drv_sbr = 2;
  // [] [0|1] [initial_value:1]
  optional bool pass_sb_lock_sts = 3;
  // [] [0|18] [initial_value:0]
  optional CrashOutputStsType crash_output_sts = 4;
  // [] [0|1] [initial_value:1]
  optional bool pab_swt_sts = 5;
  // [] [0|1] [initial_value:1]
  optional bool sec_row_lsb_lock_sts = 6;
  // [] [0|1] [initial_value:1]
  optional bool airb_fail_lmp_cmd = 7;
  // [] [0|1] [initial_value:1]
  optional bool sec_row_mid_sb_lock_sts = 8;
  // [] [0|1] [initial_value:1]
  optional bool sec_row_rsb_lock_sts = 9;
  // [] [0|3] [initial_value:1]
  optional PassSBRVisualType pass_sbr_visual = 10;
  // [] [0|3] [initial_value:1]
  optional DrvSBRVisualType drv_sbr_visual = 11;
  // [] [0|3] [initial_value:1]
  optional SecRowLSBRVisualType sec_row_lsbr_visual = 12;
  // [] [0|3] [initial_value:1]
  optional SecRowMidSBRVisualType sec_row_mid_sbr_visual = 13;
  // [] [0|3] [initial_value:1]
  optional SecRowRSBRVisualType sec_row_rsbr_visual = 14;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_abm1 = 15;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_abm1 = 16;
}

message HUT34_370 {
// Report Message
  // [] [0|1] [initial_value:0]
  optional bool time_mod = 1;
  // [] [0|1] [initial_value:0]
  optional bool time_indcn = 2;
  // [] [0|31] [initial_value:31]
  optional int32 time_hour = 3;
  // [] [0|63] [initial_value:63]
  optional int32 time_minutes = 4;
  // [] [0|63] [initial_value:63]
  optional int32 time_second = 5;
  // [] [0|15] [initial_value:15]
  optional int32 time_year_left = 6;
  // [] [0|15] [initial_value:15]
  optional int32 time_year_right = 7;
  // [] [0|15] [initial_value:0]
  optional int32 time_month = 8;
  // [] [0|31] [initial_value:0]
  optional int32 time_day = 9;
}

message AC2_385 {
// Report Message
  enum ACFrntBlwrSpdType {
    ACFRNTBLWRSPD_OFF = 0;
    ACFRNTBLWRSPD_LEVEL_1_LOW = 1;
    ACFRNTBLWRSPD_LEVEL_2 = 2;
    ACFRNTBLWRSPD_LEVEL_3 = 3;
    ACFRNTBLWRSPD_LEVEL_4 = 4;
    ACFRNTBLWRSPD_LEVEL_5 = 5;
    ACFRNTBLWRSPD_LEVEL_6 = 6;
    ACFRNTBLWRSPD_LEVEL_7_HI = 7;
    ACFRNTBLWRSPD_RESERVED = 8;
    ACFRNTBLWRSPD_AUTO_BLOWER_LEVEL = 14;
    ACFRNTBLWRSPD_INITIAL_VALUE = 15;
  }
  // [] [0|15] [initial_value:15]
  optional ACFrntBlwrSpdType ac_frnt_blwr_spd = 1;
  // [\A1\E3C] [-40|86.5] [initial_value:254]
  optional double ac_amb_temp = 2;
}

message TPMS2_395 {
// Report Message
  // [kPa] [0|348.615] [initial_value:0]
  optional double fl_tire_press = 1;
  // [\A1\E3C] [-50|204] [initial_value:0]
  optional int32 fl_tire_temp = 2;
  // [kPa] [0|348.615] [initial_value:0]
  optional double fr_tire_press = 3;
  // [\A1\E3C] [-50|204] [initial_value:0]
  optional int32 fr_tire_temp = 4;
  // [kPa] [0|348.615] [initial_value:0]
  optional double rl_tire_press = 5;
  // [\A1\E3C] [-50|204] [initial_value:0]
  optional int32 rl_tire_temp = 6;
  // [kPa] [0|348.615] [initial_value:0]
  optional double rr_tire_press = 7;
  // [\A1\E3C] [-50|204] [initial_value:0]
  optional int32 rr_tire_temp = 8;
}

message T_BOX_FD3_3E9 {
// Report Message
  enum TBOXSysErrStsType {
    T_BOX_SYSERRSTS_NO_ERROR = 0;
    T_BOX_SYSERRSTS_ERROR = 1;
    T_BOX_SYSERRSTS_STANDBY_BATTERY_ERROR = 2;
    T_BOX_SYSERRSTS_RESERVED = 3;
  }
  // [] [0|31] [initial_value:0]
  optional TBOXSysErrStsType t_box_sys_err_sts = 1;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_t_box_fd3 = 2;
}

message HUT19_415 {
// Report Message
  enum ADASMetaMsgTypType {
    ADAS_META_MSGTYP_SYSTEM_SPECIFIC = 0;
    ADAS_META_MSGTYP_POSITION = 1;
    ADAS_META_MSGTYP_SEGMENT = 2;
    ADAS_META_MSGTYP_STUB = 3;
    ADAS_META_MSGTYP_PROFILE_SHORT = 4;
    ADAS_META_MSGTYP_PROFILE_LONG = 5;
    ADAS_META_MSGTYP_META_DATA = 6;
    ADAS_META_MSGTYP_RESERVED = 7;
  }
  enum ADASMetaMapProviderType {
    ADAS_META_MAPPROVIDER_UNKNOWN = 0;
    ADAS_META_MAPPROVIDER_NOKIA_HERE_NAVTEQ = 1;
    ADAS_META_MAPPROVIDER_TOMTOM_TELE_ATLAS = 2;
    ADAS_META_MAPPROVIDER_ZENRIN = 3;
    ADAS_META_MAPPROVIDER_IPC = 4;
    ADAS_META_MAPPROVIDER_NAVINFO = 5;
    ADAS_META_MAPPROVIDER_OTHER = 6;
    ADAS_META_MAPPROVIDER_N_A = 7;
  }
  enum ADASMetaRegionCodeType {
    ADAS_META_REGIONCODE_UNKNOW = 0;
    ADAS_META_REGIONCODE_VALID_VALUES = 1;
    ADAS_META_REGIONCODE_INVALID = 32767;
  }
  // [] [0|7] [initial_value:0]
  optional ADASMetaMsgTypType adas_meta_msg_typ = 1;
  // [] [0|7] [initial_value:0]
  optional ADASMetaMapProviderType adas_meta_map_provider = 2;
  // [] [0|1023] [initial_value:0]
  optional int32 adas_meta_country_code = 3;
  // [] [0|3] [initial_value:0]
  optional int32 adas_meta_cyc_cnt = 4;
  // [] [0|3] [initial_value:0]
  optional int32 adas_meta_prot_ver_major = 5;
  // [] [0|7] [initial_value:0]
  optional int32 adas_meta_prot_ver_minor_sub = 6;
  // [] [0|511] [initial_value:0]
  optional int32 adas_meta_hw_ver = 7;
  // [] [0|1] [initial_value:1]
  optional bool adas_meta_drv_side = 8;
  // [] [0|32767] [initial_value:0]
  optional ADASMetaRegionCodeType adas_meta_region_code = 9;
  // [] [0|3] [initial_value:0]
  optional int32 adas_meta_map_ver_qtr = 10;
  // [] [0|63] [initial_value:0]
  optional int32 adas_meta_map_ver_year = 11;
  // [] [0|1] [initial_value:0]
  optional bool adas_meta_spd_units = 12;
  // [] [0|15] [initial_value:0]
  optional int32 adas_meta_prot_ver_minor = 13;
}

message HUT7_44 {
// Report Message
  enum CarWashModeReqType {
    CARWASHMODEREQ_OFF = 0;
    CARWASHMODEREQ_ON = 1;
    CARWASHMODEREQ_NO_ACTION = 2;
    CARWASHMODEREQ_RESERVED = 3;
  }
  // [NoUnit] [0|3] [initial_value:2]
  optional CarWashModeReqType car_wash_mode_req = 1;
}

message HUT6_4A {
// Report Message
  enum SglViewSelType {
    SGL_VIEW_SEL_NO_COMMANDS = 0;
    SGL_VIEW_SEL_TOUCH_2D_FRONT_BUTTON = 1;
    SGL_VIEW_SEL_TOUCH_2D_REAR_BUTTON = 2;
    SGL_VIEW_SEL_TOUCH_2D_LEFT_BUTTON = 3;
    SGL_VIEW_SEL_TOUCH_2D_RIGHT_BUTTON = 4;
    SGL_VIEW_SEL_TOUCH_2D_LEFT_FRONT_BUTTON = 5;
    SGL_VIEW_SEL_TOUCH_2D_RIGHT_FRONT_BUTTON = 6;
    SGL_VIEW_SEL_TOUCH_2D_LEFT_BACK_BUTTON = 7;
    SGL_VIEW_SEL_TOUCH_2D_RIGHT_BACK_BUTTON = 8;
    SGL_VIEW_SEL_TOUCH_RETURN_BUTTON = 9;
    SGL_VIEW_SEL_TOUCH_ENGINE_ROOM_BUTTON = 10;
    SGL_VIEW_SEL_TOUCH_FRONT_WHEEL_BUTTON = 11;
    SGL_VIEW_SEL_TOUCH_REAR_WHEEL_BUTTON = 12;
    SGL_VIEW_SEL_VERTICAL_VIEW_BUTTON = 13;
    SGL_VIEW_SEL_SINGLE_VIEW_BUTTON = 14;
    SGL_VIEW_SEL_REQUEST = 15;
  }
  // [] [0|1] [initial_value:0]
  optional bool lane_cal_actvt_cmd = 1;
  // [] [0|15] [initial_value:0]
  optional SglViewSelType sgl_view_sel = 2;
  // [] [0|1] [initial_value:0]
  optional bool sw_to_field_cal_rst_cmd = 3;
  // [] [0|1] [initial_value:0]
  optional bool auto_view_chg_cmd = 4;
  // [] [0|1] [initial_value:0]
  optional bool car_mdl_trsprcy_swt_cmd = 5;
  // [] [0|1] [initial_value:0]
  optional bool car_mdl_disp_cmd = 6;
  // [] [0|1] [initial_value:0]
  optional bool auto_avm_sw_set_cmd = 7;
  // [] [0|1] [initial_value:0]
  optional bool fpas_auto_mod_swt = 8;
  // [] [0|1] [initial_value:0]
  optional bool meb_swt_set = 9;
  // [] [0|1] [initial_value:0]
  optional bool view_softswitch_cmd = 10;
}

message CEM_NM_501 {
// Report Message
  enum DestinationCEMNMType {
    DESTINATION_CEM_NM_GW = 1;
    DESTINATION_CEM_NM_PEPS = 2;
    DESTINATION_CEM_NM_ESCL = 3;
    DESTINATION_CEM_NM_LRSDS = 4;
    DESTINATION_CEM_NM_RRSDS = 5;
    DESTINATION_CEM_NM_BCM = 6;
    DESTINATION_CEM_NM_TRAILER = 7;
    DESTINATION_CEM_NM_T_BOX = 8;
    DESTINATION_CEM_NM_CMS = 9;
    DESTINATION_CEM_NM_AC = 10;
    DESTINATION_CEM_NM_SCM = 11;
    DESTINATION_CEM_NM_DSM = 12;
    DESTINATION_CEM_NM_DDCM = 13;
    DESTINATION_CEM_NM_PDCM = 14;
    DESTINATION_CEM_NM_HUT = 15;
    DESTINATION_CEM_NM_HCM = 16;
    DESTINATION_CEM_NM_AC_RCP = 17;
    DESTINATION_CEM_NM_TPMS = 18;
    DESTINATION_CEM_NM_ACU = 32;
    DESTINATION_CEM_NM_HCU = 33;
    DESTINATION_CEM_NM_OBC = 34;
    DESTINATION_CEM_NM_BMS = 35;
    DESTINATION_CEM_NM_MCU = 36;
    DESTINATION_CEM_NM_BSG = 37;
  }
  enum OpCodeCEMNMType {
    OPCODE_CEM_NM_ALIVE = 1;
    OPCODE_CEM_NM_RING = 2;
    OPCODE_CEM_NM_LIMPHOME = 4;
    OPCODE_CEM_NM_RING_SLEEP_IND = 18;
    OPCODE_CEM_NM_LIMPHOME_SLEEP_IND = 20;
    OPCODE_CEM_NM_RING_SLEEP_ACK = 50;
  }
  // [] [0|63] [initial_value:0]
  optional DestinationCEMNMType destination_cem_nm = 1;
  // [] [0|63] [initial_value:0]
  optional OpCodeCEMNMType op_code_cem_nm = 2;
}

message IDC_NM_51C {
// Report Message
  enum DestinationIDCNMType {
    DESTINATION_IDC_NM_GW = 1;
    DESTINATION_IDC_NM_PEPS = 2;
    DESTINATION_IDC_NM_ESCL = 3;
    DESTINATION_IDC_NM_LRSDS = 4;
    DESTINATION_IDC_NM_RRSDS = 5;
    DESTINATION_IDC_NM_BCM = 6;
    DESTINATION_IDC_NM_TRAILER = 7;
    DESTINATION_IDC_NM_T_BOX = 8;
    DESTINATION_IDC_NM_CMS = 9;
    DESTINATION_IDC_NM_AC = 10;
    DESTINATION_IDC_NM_SCM = 11;
    DESTINATION_IDC_NM_DSM = 12;
    DESTINATION_IDC_NM_DDCM = 13;
    DESTINATION_IDC_NM_PDCM = 14;
    DESTINATION_IDC_NM_HUT = 15;
    DESTINATION_IDC_NM_HCM = 16;
    DESTINATION_IDC_NM_AC_RCP = 17;
    DESTINATION_IDC_NM_TPMS = 18;
    DESTINATION_IDC_NM_ACU = 32;
    DESTINATION_IDC_NM_HCU = 33;
    DESTINATION_IDC_NM_OBC = 34;
    DESTINATION_IDC_NM_BMS = 35;
    DESTINATION_IDC_NM_MCU = 36;
    DESTINATION_IDC_NM_BSG = 37;
  }
  enum OpCodeIDCNMType {
    OPCODE_IDC_NM_ALIVE = 1;
    OPCODE_IDC_NM_RING = 2;
    OPCODE_IDC_NM_LIMPHOME = 4;
    OPCODE_IDC_NM_RING_SLEEP_IND = 18;
    OPCODE_IDC_NM_LIMPHOME_SLEEP_IND = 20;
    OPCODE_IDC_NM_RING_SLEEP_ACK = 50;
  }
  // [] [0|63] [initial_value:0]
  optional DestinationIDCNMType destination_idc_nm = 1;
  // [] [0|63] [initial_value:0]
  optional OpCodeIDCNMType op_code_idc_nm = 2;
}

message HCU_FD1_60 {
// Report Message
  enum HCUPowertrainStsType {
    HCU_POWERTRAINSTS_SLEEP = 0;
    HCU_POWERTRAINSTS_INITIALIZE_MODE = 1;
    HCU_POWERTRAINSTS_POWER_UP = 2;
    HCU_POWERTRAINSTS_POWER_DOWN = 3;
    HCU_POWERTRAINSTS_HOME = 4;
    HCU_POWERTRAINSTS_RUN = 5;
    HCU_POWERTRAINSTS_CHARGING = 6;
    HCU_POWERTRAINSTS_CELLPHONE_AC = 7;
    HCU_POWERTRAINSTS_LIMPHOME = 8;
    HCU_POWERTRAINSTS_EMERGENCY_MODE = 9;
    HCU_POWERTRAINSTS_V2L = 10;
    HCU_POWERTRAINSTS_PREPARE_SLEEP = 11;
    HCU_POWERTRAINSTS_READY = 12;
    HCU_POWERTRAINSTS_LV_CHARGING = 13;
    HCU_POWERTRAINSTS_LT_CHARGING = 14;
    HCU_POWERTRAINSTS_IL_HEATING = 15;
    HCU_POWERTRAINSTS_THERMAL_RUN_AWAY = 16;
    HCU_POWERTRAINSTS_END_OF_LINE = 17;
    HCU_POWERTRAINSTS_BATTTEMPKEEP = 18;
    HCU_POWERTRAINSTS_RESERVED = 19;
  }
  enum ACUShiftInProgressType {
    ACU_SHIFTINPROGRESS_NOT_IN_PROGRESS = 0;
    ACU_SHIFTINPROGRESS_IN_PROGRESS = 1;
    ACU_SHIFTINPROGRESS_RESERVED = 2;
    ACU_SHIFTINPROGRESS_INVALID = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_hcu_pt1 = 1;
  // [Nm] [-6000|14475] [initial_value:1200]
  optional double act_wheel_trq_dmd = 2;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_hcu_pt1 = 3;
  // [NoUnit] [0|31] [initial_value:0]
  optional HCUPowertrainStsType hcu_powertrain_sts = 4;
  // description / [NoUnit] [0|3] [initial_value:0]
  optional ACUShiftInProgressType acu_shift_in_progress = 5;
  // [Nm] [0|880] [initial_value:275]
  optional double hcu_trq_lim_max_norm = 6;
  // [] [0|65535] [initial_value:0]
  optional int32 freshness_value_hcu_fd1 = 7;
}

message CSA2_A1 {
// Report Message
  enum SASStsType {
    SAS_STS_SAS_ANGLE_AND_SPEED_CORRECT = 0;
    SAS_STS_SAS_NOT_CALIBRATED = 1;
    SAS_STS_INTERMITTENT_ERROR_DETECTED = 2;
    SAS_STS_PERMANENT_ERROR_DETECTED = 3;
  }
  enum ManualShiftReqType {
    MANUALSHIFTREQ_NO_SHIFT_NO_CONTROL = 0;
    MANUALSHIFTREQ_SHIFT_UP_REQUEST = 1;
    MANUALSHIFTREQ_SHIFT_DOWN_REQUEST = 2;
    MANUALSHIFTREQ_BOTH_PEDALS_PRESSED = 3;
    MANUALSHIFTREQ_RESERVED = 4;
    MANUALSHIFTREQ_INVALID = 6;
    MANUALSHIFTREQ_FAULT = 7;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_csa2 = 1;
  // [deg] [0|780] [initial_value:0]
  optional double steer_wheel_ang = 2;
  // [] [0|1] [initial_value:0]
  optional bool steer_wheel_ang_sign = 3;
  // [deg/s] [0|1016] [initial_value:0]
  optional double steer_wheel_spd = 4;
  // [] [0|1] [initial_value:0]
  optional bool steer_wheel_spd_sign = 5;
  // [] [0|3] [initial_value:0]
  optional SASStsType sas_sts = 6;
  // description A [] [0|1] [initial_value:0]
  optional bool acc_time_gap_inc_set_swt = 7;
  // description A [] [0|1] [initial_value:0]
  optional bool acc_time_gap_dec_set_swt = 8;
  // description A [] [0|1] [initial_value:0]
  optional bool cc_acc_cruise_off_swt = 9;
  // description A [] [0|1] [initial_value:0]
  optional bool cc_acc_cancel_off_swt = 10;
  // description A [] [0|1] [initial_value:0]
  optional bool cc_acc_inc_set_spd_resu_swt = 11;
  // description A [] [0|1] [initial_value:0]
  optional bool cc_acc_dec_set_spd_set_swt = 12;
  // description A [] [0|1] [initial_value:0]
  optional bool cc_acc_swt_err = 13;
  // description B [] [0|1] [initial_value:0]
  optional bool hwa_ok_off_swt = 14;
  // [] [0|7] [initial_value:0]
  optional ManualShiftReqType manual_shift_req = 15;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_csa2 = 16;
}

message VCU_FD1_B5 {
// Report Message
  enum VCUBrkPedlStsType {
    VCU_BRKPEDLSTS_NO_ACTIVE = 0;
    VCU_BRKPEDLSTS_BRAKE_ACTIVE = 1;
    VCU_BRKPEDLSTS_INVALID = 2;
    VCU_BRKPEDLSTS_RESERVED = 3;
  }
  // [] [0|255] [initial_value:0]
  optional int32 checksum_vcu6 = 1;
  // description / [No Unit] [0|3] [initial_value:0]
  optional VCUBrkPedlStsType vcu_brk_pedl_sts = 2;
  // description / [Perc] [0|100.3935] [initial_value:0]
  optional double vcu_act_accr_pedl_rat = 3;
  // description / [No Unit] [0|1] [initial_value:0]
  optional bool vcu_a_pedl_pos_vld = 4;
  // description / [NoUnit] [0|1] [initial_value:0]
  optional bool hcu_brk_pedal_sts_valid = 5;
  // description / [No Unit] [0|1] [initial_value:0]
  optional bool vcu_aut_park_avl_sts = 6;
  // description / [Perc] [0|100.3935] [initial_value:0]
  optional double vcu_virt_accr_pedl_rat = 7;
  // description C [NoUnit] [0|1] [initial_value:1]
  optional bool vcu_accel_pedal_posn_valid = 8;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_vcu6 = 9;
  // [NoUnit] [0|1] [initial_value:0]
  optional bool vcu_drvr_intv_gear_sts = 10;
}

message ESP_FD1_BD {
// Report Message
  enum TgtRcprtnTrqStsType {
    TGTRCPRTNTRQSTS_NOT_INITIALIZED = 0;
    TGTRCPRTNTRQSTS_NORMAL = 1;
    TGTRCPRTNTRQSTS_FAULTY = 2;
    TGTRCPRTNTRQSTS_RESERVED = 3;
  }
  // [NoUnit] [0|3] [initial_value:0]
  optional TgtRcprtnTrqStsType tgt_rcprtn_trq_sts = 1;
  // [NoUnit] [0|255] [initial_value:0]
  optional int32 checksum_esp12 = 2;
  // [Nm] [-16383|0] [initial_value:8192]
  optional int32 tgt_rcprtn_trq_req = 3;
  // [NoUnit] [0|15] [initial_value:0]
  optional int32 rolling_counter_esp12 = 4;
}

message SBWM1_C7 {
// Report Message
  enum DRVRQSHFTType {
    DRV_RQ_SHFT_SHIFTER_POSITION_ZERO_Z_POS_ZERO = 0;
    DRV_RQ_SHFT_RESERVED = 1;
    DRV_RQ_SHFT_SHIFTER_POSITION_TWO_FORWARD_TWICE_X2_POS_TWO = 2;
    DRV_RQ_SHFT_SHIFTER_POSITION_THREE_FORWARD_ONCE_X1_POS_THREE = 3;
    DRV_RQ_SHFT_SHIFTER_POSITION_FOUR_BACKWARDS_ONCE_Y1_POS_FOUR = 4;
    DRV_RQ_SHFT_SHIFTER_POSITION_FIVE_BACKWARDS_TWICE_Y2_POS_FIVE = 5;
    DRV_RQ_SHFT_RESERVED_6 = 6;
    DRV_RQ_SHFT_RESERVED_7 = 7;
    DRV_RQ_SHFT_SHIFTER_NOT_INITIALIZED_INIT = 8;
    DRV_RQ_SHFT_RESERVED_9 = 9;
    DRV_RQ_SHFT_SIGNAL_NOT_AVAILABLE_SNA_DOUBLE_FAULT_SHIFTER_SENSOR = 15;
  }
  enum DrvReqParkType {
    DRVREQPARK_NO_REQUEST = 0;
    DRVREQPARK_DRIVER_REQUEST_PARK_BUTTON = 1;
    DRVREQPARK_PARK_BUTTON_FAULT = 2;
    DRVREQPARK_RESERVED = 3;
  }
  enum EShiftFaultType {
    E_SHIFTFAULT_E_SHIFT_NORMAL = 0;
    E_SHIFTFAULT_E_SHIFT_FAULT = 1;
    E_SHIFTFAULT_RESERVED = 2;
  }
  // [] [0|15] [initial_value:8]
  optional DRVRQSHFTType drv_rq_shft = 1;
  // [] [0|2] [initial_value:0]
  optional DrvReqParkType drv_req_park = 2;
  // [] [0|3] [initial_value:0]
  optional EShiftFaultType e_shift_fault = 3;
}

message ACU1_F0 {
// Report Message
  enum ACUShiftInProgressType {
    ACU_SHIFTINPROGRESS_NOT_IN_PROGRESS = 0;
    ACU_SHIFTINPROGRESS_IN_PROGRESS = 1;
    ACU_SHIFTINPROGRESS_RESERVED = 2;
    ACU_SHIFTINPROGRESS_INVALID = 3;
  }
  // [] [0|3] [initial_value:0]
  optional ACUShiftInProgressType acu_shift_in_progress = 1;
}

message RSDS_FD1_16F {
// Report Message
  enum RSDSLEDLightReqLeftType {
    RSDS_LEDLIGHTREQLEFT_NO_WARNING = 0;
    RSDS_LEDLIGHTREQLEFT_WARNING_LEVEL_1_A3_A8LED_B3_A3_C1_C1_A3_A9 = 1;
    RSDS_LEDLIGHTREQLEFT_WARNING_LEVEL_2_A3_A8LED_2HZ_C9_C1_A3_A9 = 2;
    RSDS_LEDLIGHTREQLEFT_WARNING_LEVEL_3_A3_A8LED_4HZ_C9_C1_A3_A9 = 3;
  }
  enum RSDSLEDLightReqRightType {
    RSDS_LEDLIGHTREQRIGHT_NO_WARNING = 0;
    RSDS_LEDLIGHTREQRIGHT_WARNING_LEVEL_1_A3_A8LED_B3_A3_C1_C1_A3_A9 = 1;
    RSDS_LEDLIGHTREQRIGHT_WARNING_LEVEL_2_A3_A8LED_2HZ_C9_C1_A3_A9 = 2;
    RSDS_LEDLIGHTREQRIGHT_WARNING_LEVEL_3_A3_A8LED_4HZ_C9_C1_A3_A9 = 3;
  }
  enum RSDSIPSoundReqType {
    RSDS_IPSOUNDREQ_NO_WARNING = 0;
    RSDS_IPSOUNDREQ_WARNING_LEVEL_1_A3_A82HZ_C9_F9_D2_F4_A3_A9 = 1;
    RSDS_IPSOUNDREQ_WARNING_LEVEL_2_A3_A84HZ_C9_F9_D2_F4_A3_A9 = 2;
    RSDS_IPSOUNDREQ_WARNING_LEVEL_3_A3_A8_B3_A3_CF = 3;
  }
  enum RSDSRCWTriggerType {
    RSDS_RCW_TRIGGER_NO_WARNING = 0;
    RSDS_RCW_TRIGGER_RCW_LEVEL_1 = 1;
    RSDS_RCW_TRIGGER_RCW_LEVEL_2 = 2;
    RSDS_RCW_TRIGGER_RESERVED = 3;
  }
  enum BSDLCAwarningReqRightType {
    BSD_LCA_WARNINGREQRIGHT_NO_WARNING = 0;
    BSD_LCA_WARNINGREQRIGHT_WARNING_LEVEL_1 = 1;
    BSD_LCA_WARNINGREQRIGHT_WARNING_LEVEL_2 = 2;
    BSD_LCA_WARNINGREQRIGHT_RESERVED = 3;
  }
  enum BSDLCAwarningReqleftType {
    BSD_LCA_WARNINGREQLEFT_NO_WARNING = 0;
    BSD_LCA_WARNINGREQLEFT_WARNING_LEVEL_1 = 1;
    BSD_LCA_WARNINGREQLEFT_WARNING_LEVEL_2 = 2;
    BSD_LCA_WARNINGREQLEFT_RESERVED = 3;
  }
  enum DOWwarningReqRightType {
    DOW_WARNINGREQRIGHT_NO_WARNING = 0;
    DOW_WARNINGREQRIGHT_WARNING_LEVEL_1 = 1;
    DOW_WARNINGREQRIGHT_WARNING_LEVEL_2 = 2;
    DOW_WARNINGREQRIGHT_RESERVED = 3;
  }
  enum DOWwarningReqleftType {
    DOW_WARNINGREQLEFT_NO_WARNING = 0;
    DOW_WARNINGREQLEFT_WARNING_LEVEL_1 = 1;
    DOW_WARNINGREQLEFT_WARNING_LEVEL_2 = 2;
    DOW_WARNINGREQLEFT_RESERVED = 3;
  }
  enum ZoneIndLeType {
    ZONEINDLE_NO_OBJECT_IN_THE_ZONE = 0;
    ZONEINDLE_OBJECT_IN_BSW_ZONE = 1;
    ZONEINDLE_OBJECT_IN_CVW_ZONE = 2;
    ZONEINDLE_OBJECT_IN_THE_SHARED_ZONE = 3;
  }
  enum ObjStsLeType {
    OBJSTSLE_INVALID = 0;
    OBJSTSLE_NEW_D0_C2_B1_EA = 1;
    OBJSTSLE_MATURE_B3_C9_CA_EC_B1_EA = 2;
    OBJSTSLE_COASTED_BB_AC_D0_C4 = 3;
  }
  enum ZoneIndRiType {
    ZONEINDRI_NO_OBJECT_IN_THE_ZONE = 0;
    ZONEINDRI_OBJECT_IN_BSW_ZONE = 1;
    ZONEINDRI_OBJECT_IN_CVW_ZONE = 2;
    ZONEINDRI_OBJECT_IN_THE_SHARED_ZONE = 3;
  }
  enum ObjStsRiType {
    OBJSTSRI_INVALID = 0;
    OBJSTSRI_NEW = 1;
    OBJSTSRI_MATURE = 2;
    OBJSTSRI_COASTED = 3;
  }
  enum BSDStateType {
    BSD_STATE_INITIAL_VALUE = 0;
    BSD_STATE_OFF = 1;
    BSD_STATE_STANDBY = 2;
    BSD_STATE_ACTIVE = 3;
    BSD_STATE_PASSIVE = 4;
    BSD_STATE_FAULT = 5;
    BSD_STATE_RESERVED = 6;
  }
  enum LCAStateType {
    LCA_STATE_INITIAL_VALUE = 0;
    LCA_STATE_OFF = 1;
    LCA_STATE_STANDBY = 2;
    LCA_STATE_ACTIVE = 3;
    LCA_STATE_PASSIVE = 4;
    LCA_STATE_FAULT = 5;
    LCA_STATE_RESERVED = 6;
  }
  enum DOWStateType {
    DOW_STATE_INITIAL_VALUE = 0;
    DOW_STATE_OFF = 1;
    DOW_STATE_STANDBY = 2;
    DOW_STATE_ACTIVE = 3;
    DOW_STATE_PASSIVE = 4;
    DOW_STATE_FAULT = 5;
    DOW_STATE_RESERVED = 6;
  }
  enum RCWStateType {
    RCW_STATE_INITIAL_VALUE = 0;
    RCW_STATE_OFF = 1;
    RCW_STATE_STANDBY = 2;
    RCW_STATE_ACTIVE = 3;
    RCW_STATE_PASSIVE = 4;
    RCW_STATE_FAULT = 5;
    RCW_STATE_RESERVED = 6;
  }
  enum RCTAStateType {
    RCTA_STATE_INITIAL_VALUE = 0;
    RCTA_STATE_OFF = 1;
    RCTA_STATE_STANDBY = 2;
    RCTA_STATE_ACTIVE = 3;
    RCTA_STATE_PASSIVE = 4;
    RCTA_STATE_FAULT = 5;
    RCTA_STATE_RESERVED = 6;
  }
  enum RCTBStateType {
    RCTB_STATE_INITIAL_VALUE = 0;
    RCTB_STATE_OFF = 1;
    RCTB_STATE_STANDBY = 2;
    RCTB_STATE_ACTIVE = 3;
    RCTB_STATE_PASSIVE = 4;
    RCTB_STATE_FAULT = 5;
    RCTB_STATE_HOLD = 6;
    RCTB_STATE_RESERVED = 7;
  }
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double rsds_obj_rel_accel_y_le = 1;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_rsds_r_2 = 2;
  // [] [0|1] [initial_value:0]
  optional bool rsds_brk_err_sts = 3;
  // [] [0|1] [initial_value:0]
  optional bool rsds_brkg_req = 4;
  // [m/s^2] [-10|2.75] [initial_value:200]
  optional double rsds_brkg_req_val = 5;
  // [] [0|3] [initial_value:0]
  optional RSDSLEDLightReqLeftType rsds_led_light_req_left = 6;
  // [] [0|3] [initial_value:0]
  optional RSDSLEDLightReqRightType rsds_led_light_req_right = 7;
  // [] [0|3] [initial_value:0]
  optional RSDSIPSoundReqType rsds_ip_sound_req = 8;
  // [] [0|3] [initial_value:0]
  optional RSDSRCWTriggerType rsds_rcw_trigger = 9;
  // [] [0|1] [initial_value:0]
  optional bool rsds_err_sts = 10;
  // [] [0|1] [initial_value:0]
  optional bool rsds_brkg_trig = 11;
  // [] [0|1] [initial_value:0]
  optional bool rsds_bli_sts = 12;
  // [] [0|1] [initial_value:0]
  optional bool rsds_trailer_sts = 13;
  // [] [0|1] [initial_value:0]
  optional bool rsds_lca_resp = 14;
  // [] [0|1] [initial_value:0]
  optional bool rsds_cta_actv = 15;
  // [] [0|1] [initial_value:0]
  optional bool rsds_dow_resp = 16;
  // [] [0|1] [initial_value:0]
  optional bool rsds_rcw_resp = 17;
  // [] [0|1] [initial_value:0]
  optional bool rsds_rcta_resp = 18;
  // [] [0|1] [initial_value:0]
  optional bool rsds_rcta_brk_resp = 19;
  // [] [0|3] [initial_value:0]
  optional BSDLCAwarningReqRightType bsd_lca_warning_req_right = 20;
  // [] [0|3] [initial_value:0]
  optional BSDLCAwarningReqleftType bsd_lca_warning_reqleft = 21;
  // [] [0|3] [initial_value:0]
  optional DOWwarningReqRightType dow_warning_req_right = 22;
  // [] [0|3] [initial_value:0]
  optional DOWwarningReqleftType dow_warning_reqleft = 23;
  // [] [0|1] [initial_value:0]
  optional bool rcta_warning_req_right = 24;
  // [] [0|1] [initial_value:0]
  optional bool rcta_warning_req_left = 25;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_rsds_r_2 = 26;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_rsds_r_3 = 27;
  // [] [0|3] [initial_value:2]
  optional int32 le_tar_sts = 28;
  // [] [0|63] [initial_value:0]
  optional int32 obj_id_le = 29;
  // [m] [-92.3|10] [initial_value:0]
  optional double obj_lgt_posn_curv_le = 30;
  // [m] [-12.8|12.7] [initial_value:128]
  optional double obj_lat_posn_curv_le = 31;
  // [m/s] [-10|92.3] [initial_value:0]
  optional double obj_lgt_spd_curv_le = 32;
  // [m/s] [-6.4|6.3] [initial_value:64]
  optional double obj_lat_spd_curv_le = 33;
  // [s] [0|12.7] [initial_value:127]
  optional double obj_ttc_le = 34;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_rsds_r_3 = 35;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_rsds_r_4 = 36;
  // [m] [0|15] [initial_value:0]
  optional int32 obj_len_le = 37;
  // [m] [0|3.5] [initial_value:0]
  optional double obj_width_le = 38;
  // [] [0|1] [initial_value:0]
  optional bool obj_staty_le = 39;
  // [] [0|3] [initial_value:0]
  optional ZoneIndLeType zone_ind_le = 40;
  // [] [0|3] [initial_value:0]
  optional ObjStsLeType obj_sts_le = 41;
  // [m] [0|15] [initial_value:0]
  optional int32 obj_rin_ri = 42;
  // [m] [0|3.5] [initial_value:0]
  optional double obj_width_ri = 43;
  // [] [0|1] [initial_value:0]
  optional bool obj_staty_ri = 44;
  // [] [0|3] [initial_value:0]
  optional ZoneIndRiType zone_ind_ri = 45;
  // [] [0|3] [initial_value:0]
  optional ObjStsRiType obj_sts_ri = 46;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_rsds_r_4 = 47;
  // [] [0|255] [initial_value:0]
  optional int32 checksum_rsds_r_5 = 48;
  // [] [0|3] [initial_value:2]
  optional int32 ri_tar_sts = 49;
  // [] [0|63] [initial_value:0]
  optional int32 obj_id_ri = 50;
  // [m] [-92.3|10] [initial_value:0]
  optional double obj_lgt_posn_curv_ri = 51;
  // [m] [-12.8|12.7] [initial_value:128]
  optional double obj_lat_posn_curv_ri = 52;
  // [m/s] [-10|92.3] [initial_value:0]
  optional double obj_lgt_spd_curv_ri = 53;
  // [m/s] [-6.4|6.3] [initial_value:64]
  optional double obj_lat_spd_curv_ri = 54;
  // [s] [0|12.7] [initial_value:127]
  optional double obj_ttc_ri = 55;
  // [] [0|15] [initial_value:0]
  optional int32 rolling_counter_rsds_r_5 = 56;
  // [m] [0|11.625] [initial_value:0]
  optional double rsds_obj_dist_x_std_le = 57;
  // [m] [0|11.625] [initial_value:0]
  optional double rsds_obj_dist_y_std_le = 58;
  // [mps] [0|11.625] [initial_value:0]
  optional double rsds_obj_rel_vel_x_std_le = 59;
  // [mps] [0|11.625] [initial_value:0]
  optional double rsds_obj_rel_vel_y_std_le = 60;
  // [mps2] [0|11.625] [initial_value:0]
  optional double rsds_obj_r_accel_x_std_le = 61;
  // [mps2] [0|11.625] [initial_value:0]
  optional double rsds_obj_r_accel_y_std_le = 62;
  // [m] [0|11.625] [initial_value:0]
  optional double rsds_obj_dist_x_std_ri = 63;
  // [m] [0|11.625] [initial_value:0]
  optional double rsds_obj_dist_y_std_ri = 64;
  // [mps] [0|11.625] [initial_value:0]
  optional double rsds_obj_rel_vel_x_std_ri = 65;
  // [mps] [0|11.625] [initial_value:0]
  optional double rsds_obj_rel_vel_y_std_ri = 66;
  // [mps2] [0|11.625] [initial_value:0]
  optional double rsds_obj_r_accel_x_std_ri = 67;
  // [mps2] [0|11.625] [initial_value:0]
  optional double rsds_obj_r_accel_y_std_ri = 68;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double rsds_obj_rel_accel_x_le = 69;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double rsds_obj_rel_accel_x_ri = 70;
  // [m/s2] [-16|15.9688] [initial_value:512]
  optional double rsds_obj_rel_accel_y_ri = 71;
  // [NoUnit] [0|7] [initial_value:0]
  optional BSDStateType bsd_state = 72;
  // [NoUnit] [0|7] [initial_value:0]
  optional LCAStateType lca_state = 73;
  // [NoUnit] [0|7] [initial_value:0]
  optional DOWStateType dow_state = 74;
  // [NoUnit] [0|7] [initial_value:0]
  optional RCWStateType rcw_state = 75;
  // [NoUnit] [0|7] [initial_value:0]
  optional RCTAStateType rcta_state = 76;
  // [NoUnit] [0|7] [initial_value:0]
  optional RCTBStateType rctb_state = 77;
}

message RSDS_FD2_30A {
// Report Message
  // [s] [0|6.3] [initial_value:0]
  optional double rcta_b_ttc = 1;
  // [s] [0|6.3] [initial_value:0]
  optional double bsd_lca_left_ttc = 2;
  // [s] [0|6.3] [initial_value:0]
  optional double bsd_lca_right_ttc = 3;
  // [s] [0|6.3] [initial_value:0]
  optional double rcw_ttc = 4;
  // [] [0|1] [initial_value:0]
  optional bool rsds_le_tgt_01 = 5;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_le_tgt_01_dx = 6;
  // [m] [0|8] [initial_value:0]
  optional double rsds_le_tgt_01_dy = 7;
  // [] [0|1] [initial_value:0]
  optional bool rsds_le_tgt_02 = 8;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_le_tgt_02_dx = 9;
  // [m] [0|8] [initial_value:0]
  optional double rsds_le_tgt_02_dy = 10;
  // [] [0|1] [initial_value:0]
  optional bool rsds_ri_tgt_01 = 11;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_ri_tgt_01_dx = 12;
  // [m] [-8|0] [initial_value:0]
  optional double rsds_ri_tgt_01_dy = 13;
  // [] [0|1] [initial_value:0]
  optional bool rsds_ri_tgt_02 = 14;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_ri_tgt_02_dx = 15;
  // [m] [-8|0] [initial_value:0]
  optional double rsds_ri_tgt_02_dy = 16;
  // [] [0|1] [initial_value:0]
  optional bool rsds_mid_tgt_01 = 17;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_mid_tgt_01_dx = 18;
  // [m] [-4|4] [initial_value:0]
  optional double rsds_mid_tgt_01_dy = 19;
  // [] [0|1] [initial_value:0]
  optional bool rsds_mid_tgt_02 = 20;
  // [m] [-90|5] [initial_value:0]
  optional double rsds_mid_tgt_02_dx = 21;
  // [m] [-4|4] [initial_value:0]
  optional double rsds_mid_tgt_02_dy = 22;
}

message ADAS_AD1_470 {
// Report Message
  // description / [] [0|255] [initial_value:0]
  optional int32 sync_crc = 1;
  // description / [] [0|3] [initial_value:0]
  optional int32 sync_ovs = 2;
  // description / [] [0|31] [initial_value:0]
  optional int32 sync_resd = 3;
  // description / [] [0|15] [initial_value:0]
  optional int32 sync_counter = 4;
  // description / [] [0|1] [initial_value:0]
  optional bool sync_sgw = 5;
  // description / [] [0|4294967295] [initial_value:0]
  optional int32 sync_sync_time = 6;
  // description / [] [0|15] [initial_value:0]
  optional int32 sync_time_domain = 7;
  // description / [] [0|255] [initial_value:0]
  optional int32 sync_type = 8;
}

message RSDS_L_SGU1_580 {
// Report Message
  enum RSDSLObjType1Type {
    RSDS_L_OBJTYPE_1_UNKNOWN = 0;
    RSDS_L_OBJTYPE_1_TRUCK = 1;
    RSDS_L_OBJTYPE_1_CAR = 2;
    RSDS_L_OBJTYPE_1_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_1_CYCLIST = 4;
    RSDS_L_OBJTYPE_1_RESERVED = 5;
  }
  enum RSDSLObjMotionType1Type {
    RSDS_L_OBJMOTIONTYPE_1_INT = 0;
    RSDS_L_OBJMOTIONTYPE_1_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_1_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_1_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_1_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_1_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_1_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_1_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured1Type {
    RSDS_L_OBJMEASURED_1_INIT = 0;
    RSDS_L_OBJMEASURED_1_MEASURED = 1;
    RSDS_L_OBJMEASURED_1_PREDICTED = 2;
    RSDS_L_OBJMEASURED_1_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection1Type {
    RSDS_L_OBJMOTIONDIRECTION_1_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_1_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_1_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_1_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_1_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_1_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_1_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_1_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint1Type {
    RSDS_L_OBJREFPOINT_1_REAR = 0;
    RSDS_L_OBJREFPOINT_1_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_1_LEFT = 2;
    RSDS_L_OBJREFPOINT_1_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_1_FRONT = 4;
    RSDS_L_OBJREFPOINT_1_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_1_RIGHT = 6;
    RSDS_L_OBJREFPOINT_1_REAR_RIGHT = 7;
  }
  enum RSDSLObjType2Type {
    RSDS_L_OBJTYPE_2_UNKNOWN = 0;
    RSDS_L_OBJTYPE_2_TRUCK = 1;
    RSDS_L_OBJTYPE_2_CAR = 2;
    RSDS_L_OBJTYPE_2_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_2_CYCLIST = 4;
    RSDS_L_OBJTYPE_2_RESERVED = 5;
  }
  enum RSDSLObjMotionType2Type {
    RSDS_L_OBJMOTIONTYPE_2_INT = 0;
    RSDS_L_OBJMOTIONTYPE_2_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_2_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_2_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_2_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_2_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_2_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_2_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured2Type {
    RSDS_L_OBJMEASURED_2_INIT = 0;
    RSDS_L_OBJMEASURED_2_MEASURED = 1;
    RSDS_L_OBJMEASURED_2_PREDICTED = 2;
    RSDS_L_OBJMEASURED_2_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection2Type {
    RSDS_L_OBJMOTIONDIRECTION_2_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_2_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_2_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_2_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_2_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_2_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_2_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_2_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint2Type {
    RSDS_L_OBJREFPOINT_2_REAR = 0;
    RSDS_L_OBJREFPOINT_2_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_2_LEFT = 2;
    RSDS_L_OBJREFPOINT_2_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_2_FRONT = 4;
    RSDS_L_OBJREFPOINT_2_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_2_RIGHT = 6;
    RSDS_L_OBJREFPOINT_2_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_1 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_1 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_1 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_1 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_1 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_1 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType1Type rsds_l_obj_type_1 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_1 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_1 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_1 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_1 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType1Type rsds_l_obj_motion_type_1 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured1Type rsds_l_obj_measured_1 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_1 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_1 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_1 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_1 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_1 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_1 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection1Type rsds_l_obj_motion_direction_1 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_1 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_1 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_1 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_1 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_1 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint1Type rsds_l_obj_ref_point_1 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_1 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_2 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_2 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_2 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_2 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_2 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_2 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType2Type rsds_l_obj_type_2 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_2 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_2 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_2 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_2 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType2Type rsds_l_obj_motion_type_2 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured2Type rsds_l_obj_measured_2 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_2 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_2 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_2 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_2 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_2 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_2 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection2Type rsds_l_obj_motion_direction_2 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_2 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_2 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_2 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_2 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_2 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint2Type rsds_l_obj_ref_point_2 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_2 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu1_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu1_rolling_counter8 = 71;
}

message RSDS_L_SGU2_581 {
// Report Message
  enum RSDSLObjType3Type {
    RSDS_L_OBJTYPE_3_UNKNOWN = 0;
    RSDS_L_OBJTYPE_3_TRUCK = 1;
    RSDS_L_OBJTYPE_3_CAR = 2;
    RSDS_L_OBJTYPE_3_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_3_CYCLIST = 4;
    RSDS_L_OBJTYPE_3_RESERVED = 5;
  }
  enum RSDSLObjMotionType3Type {
    RSDS_L_OBJMOTIONTYPE_3_INT = 0;
    RSDS_L_OBJMOTIONTYPE_3_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_3_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_3_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_3_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_3_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_3_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_3_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured3Type {
    RSDS_L_OBJMEASURED_3_INIT = 0;
    RSDS_L_OBJMEASURED_3_MEASURED = 1;
    RSDS_L_OBJMEASURED_3_PREDICTED = 2;
    RSDS_L_OBJMEASURED_3_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection3Type {
    RSDS_L_OBJMOTIONDIRECTION_3_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_3_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_3_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_3_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_3_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_3_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_3_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_3_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint3Type {
    RSDS_L_OBJREFPOINT_3_REAR = 0;
    RSDS_L_OBJREFPOINT_3_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_3_LEFT = 2;
    RSDS_L_OBJREFPOINT_3_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_3_FRONT = 4;
    RSDS_L_OBJREFPOINT_3_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_3_RIGHT = 6;
    RSDS_L_OBJREFPOINT_3_REAR_RIGHT = 7;
  }
  enum RSDSLObjType4Type {
    RSDS_L_OBJTYPE_4_UNKNOWN = 0;
    RSDS_L_OBJTYPE_4_TRUCK = 1;
    RSDS_L_OBJTYPE_4_CAR = 2;
    RSDS_L_OBJTYPE_4_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_4_CYCLIST = 4;
    RSDS_L_OBJTYPE_4_RESERVED = 5;
  }
  enum RSDSLObjMotionType4Type {
    RSDS_L_OBJMOTIONTYPE_4_INT = 0;
    RSDS_L_OBJMOTIONTYPE_4_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_4_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_4_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_4_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_4_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_4_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_4_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured4Type {
    RSDS_L_OBJMEASURED_4_INIT = 0;
    RSDS_L_OBJMEASURED_4_MEASURED = 1;
    RSDS_L_OBJMEASURED_4_PREDICTED = 2;
    RSDS_L_OBJMEASURED_4_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection4Type {
    RSDS_L_OBJMOTIONDIRECTION_4_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_4_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_4_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_4_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_4_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_4_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_4_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_4_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint4Type {
    RSDS_L_OBJREFPOINT_4_REAR = 0;
    RSDS_L_OBJREFPOINT_4_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_4_LEFT = 2;
    RSDS_L_OBJREFPOINT_4_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_4_FRONT = 4;
    RSDS_L_OBJREFPOINT_4_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_4_RIGHT = 6;
    RSDS_L_OBJREFPOINT_4_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_3 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_3 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_3 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_3 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_3 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_3 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType3Type rsds_l_obj_type_3 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_3 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_3 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_3 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_3 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType3Type rsds_l_obj_motion_type_3 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured3Type rsds_l_obj_measured_3 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_3 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_3 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_3 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_3 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_3 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_3 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection3Type rsds_l_obj_motion_direction_3 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_3 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_3 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_3 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_3 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_3 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint3Type rsds_l_obj_ref_point_3 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_3 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_4 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_4 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_4 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_4 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_4 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_4 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType4Type rsds_l_obj_type_4 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_4 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_4 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_4 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_4 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType4Type rsds_l_obj_motion_type_4 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured4Type rsds_l_obj_measured_4 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_4 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_4 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_4 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_4 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_4 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_4 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection4Type rsds_l_obj_motion_direction_4 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_4 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_4 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_4 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_4 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_4 = 52;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_4 = 53;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint4Type rsds_l_obj_ref_point_4 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu2_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu2_rolling_counter8 = 71;
}

message RSDS_L_SGU3_582 {
// Report Message
  enum RSDSLObjType5Type {
    RSDS_L_OBJTYPE_5_UNKNOWN = 0;
    RSDS_L_OBJTYPE_5_TRUCK = 1;
    RSDS_L_OBJTYPE_5_CAR = 2;
    RSDS_L_OBJTYPE_5_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_5_CYCLIST = 4;
    RSDS_L_OBJTYPE_5_RESERVED = 5;
  }
  enum RSDSLObjMotionType5Type {
    RSDS_L_OBJMOTIONTYPE_5_INT = 0;
    RSDS_L_OBJMOTIONTYPE_5_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_5_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_5_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_5_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_5_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_5_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_5_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured5Type {
    RSDS_L_OBJMEASURED_5_INIT = 0;
    RSDS_L_OBJMEASURED_5_MEASURED = 1;
    RSDS_L_OBJMEASURED_5_PREDICTED = 2;
    RSDS_L_OBJMEASURED_5_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection5Type {
    RSDS_L_OBJMOTIONDIRECTION_5_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_5_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_5_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_5_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_5_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_5_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_5_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_5_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint5Type {
    RSDS_L_OBJREFPOINT_5_REAR = 0;
    RSDS_L_OBJREFPOINT_5_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_5_LEFT = 2;
    RSDS_L_OBJREFPOINT_5_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_5_FRONT = 4;
    RSDS_L_OBJREFPOINT_5_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_5_RIGHT = 6;
    RSDS_L_OBJREFPOINT_5_REAR_RIGHT = 7;
  }
  enum RSDSLObjType6Type {
    RSDS_L_OBJTYPE_6_UNKNOWN = 0;
    RSDS_L_OBJTYPE_6_TRUCK = 1;
    RSDS_L_OBJTYPE_6_CAR = 2;
    RSDS_L_OBJTYPE_6_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_6_CYCLIST = 4;
    RSDS_L_OBJTYPE_6_RESERVED = 5;
  }
  enum RSDSLObjMotionType6Type {
    RSDS_L_OBJMOTIONTYPE_6_INT = 0;
    RSDS_L_OBJMOTIONTYPE_6_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_6_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_6_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_6_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_6_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_6_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_6_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured6Type {
    RSDS_L_OBJMEASURED_6_INIT = 0;
    RSDS_L_OBJMEASURED_6_MEASURED = 1;
    RSDS_L_OBJMEASURED_6_PREDICTED = 2;
    RSDS_L_OBJMEASURED_6_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection6Type {
    RSDS_L_OBJMOTIONDIRECTION_6_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_6_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_6_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_6_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_6_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_6_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_6_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_6_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint6Type {
    RSDS_L_OBJREFPOINT_6_REAR = 0;
    RSDS_L_OBJREFPOINT_6_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_6_LEFT = 2;
    RSDS_L_OBJREFPOINT_6_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_6_FRONT = 4;
    RSDS_L_OBJREFPOINT_6_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_6_RIGHT = 6;
    RSDS_L_OBJREFPOINT_6_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_5 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_5 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_5 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_5 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_5 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_5 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType5Type rsds_l_obj_type_5 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_5 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_5 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_5 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_5 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType5Type rsds_l_obj_motion_type_5 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured5Type rsds_l_obj_measured_5 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_5 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_5 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_5 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_5 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_5 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_5 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection5Type rsds_l_obj_motion_direction_5 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_5 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_5 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_5 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_5 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_5 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint5Type rsds_l_obj_ref_point_5 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_5 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_6 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_6 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_6 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_6 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_6 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_6 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType6Type rsds_l_obj_type_6 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_6 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_6 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_6 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_6 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType6Type rsds_l_obj_motion_type_6 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured6Type rsds_l_obj_measured_6 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_6 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_6 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_6 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_6 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_6 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_6 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection6Type rsds_l_obj_motion_direction_6 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_6 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_6 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_6 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_6 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_6 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint6Type rsds_l_obj_ref_point_6 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_6 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu3_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu3_rolling_counter8 = 71;
}

message RSDS_L_SGU4_583 {
// Report Message
  enum RSDSLObjType7Type {
    RSDS_L_OBJTYPE_7_UNKNOWN = 0;
    RSDS_L_OBJTYPE_7_TRUCK = 1;
    RSDS_L_OBJTYPE_7_CAR = 2;
    RSDS_L_OBJTYPE_7_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_7_CYCLIST = 4;
    RSDS_L_OBJTYPE_7_RESERVED = 5;
  }
  enum RSDSLObjMotionType7Type {
    RSDS_L_OBJMOTIONTYPE_7_INT = 0;
    RSDS_L_OBJMOTIONTYPE_7_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_7_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_7_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_7_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_7_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_7_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_7_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured7Type {
    RSDS_L_OBJMEASURED_7_INIT = 0;
    RSDS_L_OBJMEASURED_7_MEASURED = 1;
    RSDS_L_OBJMEASURED_7_PREDICTED = 2;
    RSDS_L_OBJMEASURED_7_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection7Type {
    RSDS_L_OBJMOTIONDIRECTION_7_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_7_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_7_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_7_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_7_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_7_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_7_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_7_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint7Type {
    RSDS_L_OBJREFPOINT_7_REAR = 0;
    RSDS_L_OBJREFPOINT_7_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_7_LEFT = 2;
    RSDS_L_OBJREFPOINT_7_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_7_FRONT = 4;
    RSDS_L_OBJREFPOINT_7_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_7_RIGHT = 6;
    RSDS_L_OBJREFPOINT_7_REAR_RIGHT = 7;
  }
  enum RSDSLObjType8Type {
    RSDS_L_OBJTYPE_8_UNKNOWN = 0;
    RSDS_L_OBJTYPE_8_TRUCK = 1;
    RSDS_L_OBJTYPE_8_CAR = 2;
    RSDS_L_OBJTYPE_8_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_8_CYCLIST = 4;
    RSDS_L_OBJTYPE_8_RESERVED = 5;
  }
  enum RSDSLObjMotionType8Type {
    RSDS_L_OBJMOTIONTYPE_8_INT = 0;
    RSDS_L_OBJMOTIONTYPE_8_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_8_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_8_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_8_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_8_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_8_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_8_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured8Type {
    RSDS_L_OBJMEASURED_8_INIT = 0;
    RSDS_L_OBJMEASURED_8_MEASURED = 1;
    RSDS_L_OBJMEASURED_8_PREDICTED = 2;
    RSDS_L_OBJMEASURED_8_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection8Type {
    RSDS_L_OBJMOTIONDIRECTION_8_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_8_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_8_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_8_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_8_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_8_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_8_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_8_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint8Type {
    RSDS_L_OBJREFPOINT_8_REAR = 0;
    RSDS_L_OBJREFPOINT_8_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_8_LEFT = 2;
    RSDS_L_OBJREFPOINT_8_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_8_FRONT = 4;
    RSDS_L_OBJREFPOINT_8_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_8_RIGHT = 6;
    RSDS_L_OBJREFPOINT_8_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_7 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_7 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_7 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_7 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_7 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_7 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType7Type rsds_l_obj_type_7 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_7 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_7 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_7 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_7 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType7Type rsds_l_obj_motion_type_7 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured7Type rsds_l_obj_measured_7 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_7 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_7 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_7 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_7 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_7 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_7 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection7Type rsds_l_obj_motion_direction_7 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_7 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_7 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_7 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_7 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_7 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint7Type rsds_l_obj_ref_point_7 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_7 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_8 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_8 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_8 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_8 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_8 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_8 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType8Type rsds_l_obj_type_8 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_8 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_8 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_8 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_8 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType8Type rsds_l_obj_motion_type_8 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured8Type rsds_l_obj_measured_8 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_8 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_8 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_8 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_8 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_8 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_8 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection8Type rsds_l_obj_motion_direction_8 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_8 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_8 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_8 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_8 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_8 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint8Type rsds_l_obj_ref_point_8 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_8 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu4_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu4_rolling_counter8 = 71;
}

message RSDS_L_SGU5_584 {
// Report Message
  enum RSDSLObjType9Type {
    RSDS_L_OBJTYPE_9_UNKNOWN = 0;
    RSDS_L_OBJTYPE_9_TRUCK = 1;
    RSDS_L_OBJTYPE_9_CAR = 2;
    RSDS_L_OBJTYPE_9_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_9_CYCLIST = 4;
    RSDS_L_OBJTYPE_9_RESERVED = 5;
  }
  enum RSDSLObjMotionType9Type {
    RSDS_L_OBJMOTIONTYPE_9_INT = 0;
    RSDS_L_OBJMOTIONTYPE_9_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_9_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_9_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_9_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_9_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_9_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_9_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured9Type {
    RSDS_L_OBJMEASURED_9_INIT = 0;
    RSDS_L_OBJMEASURED_9_MEASURED = 1;
    RSDS_L_OBJMEASURED_9_PREDICTED = 2;
    RSDS_L_OBJMEASURED_9_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection9Type {
    RSDS_L_OBJMOTIONDIRECTION_9_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_9_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_9_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_9_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_9_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_9_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_9_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_9_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint9Type {
    RSDS_L_OBJREFPOINT_9_REAR = 0;
    RSDS_L_OBJREFPOINT_9_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_9_LEFT = 2;
    RSDS_L_OBJREFPOINT_9_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_9_FRONT = 4;
    RSDS_L_OBJREFPOINT_9_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_9_RIGHT = 6;
    RSDS_L_OBJREFPOINT_9_REAR_RIGHT = 7;
  }
  enum RSDSLObjType10Type {
    RSDS_L_OBJTYPE_10_UNKNOWN = 0;
    RSDS_L_OBJTYPE_10_TRUCK = 1;
    RSDS_L_OBJTYPE_10_CAR = 2;
    RSDS_L_OBJTYPE_10_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_10_CYCLIST = 4;
    RSDS_L_OBJTYPE_10_RESERVED = 5;
  }
  enum RSDSLObjMotionType10Type {
    RSDS_L_OBJMOTIONTYPE_10_INT = 0;
    RSDS_L_OBJMOTIONTYPE_10_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_10_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_10_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_10_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_10_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_10_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_10_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured10Type {
    RSDS_L_OBJMEASURED_10_INIT = 0;
    RSDS_L_OBJMEASURED_10_MEASURED = 1;
    RSDS_L_OBJMEASURED_10_PREDICTED = 2;
    RSDS_L_OBJMEASURED_10_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection10Type {
    RSDS_L_OBJMOTIONDIRECTION_10_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_10_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_10_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_10_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_10_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_10_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_10_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_10_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint10Type {
    RSDS_L_OBJREFPOINT_10_REAR = 0;
    RSDS_L_OBJREFPOINT_10_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_10_LEFT = 2;
    RSDS_L_OBJREFPOINT_10_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_10_FRONT = 4;
    RSDS_L_OBJREFPOINT_10_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_10_RIGHT = 6;
    RSDS_L_OBJREFPOINT_10_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_9 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_9 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_9 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_9 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_9 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_9 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType9Type rsds_l_obj_type_9 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_9 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_9 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_9 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_9 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType9Type rsds_l_obj_motion_type_9 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured9Type rsds_l_obj_measured_9 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_9 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_9 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_9 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_9 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_9 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_9 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection9Type rsds_l_obj_motion_direction_9 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_9 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_9 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_9 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_9 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_9 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint9Type rsds_l_obj_ref_point_9 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_9 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_10 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_10 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_10 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_10 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_10 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_10 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType10Type rsds_l_obj_type_10 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_10 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_10 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_10 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_10 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType10Type rsds_l_obj_motion_type_10 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured10Type rsds_l_obj_measured_10 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_10 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_10 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_10 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_10 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_10 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_10 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection10Type rsds_l_obj_motion_direction_10 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_10 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_10 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_10 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_10 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_10 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint10Type rsds_l_obj_ref_point_10 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_10 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu5_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu5_rolling_counter8 = 71;
}

message RSDS_L_SGU6_585 {
// Report Message
  enum RSDSLObjType11Type {
    RSDS_L_OBJTYPE_11_UNKNOWN = 0;
    RSDS_L_OBJTYPE_11_TRUCK = 1;
    RSDS_L_OBJTYPE_11_CAR = 2;
    RSDS_L_OBJTYPE_11_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_11_CYCLIST = 4;
    RSDS_L_OBJTYPE_11_RESERVED = 5;
  }
  enum RSDSLObjMotionType11Type {
    RSDS_L_OBJMOTIONTYPE_11_INT = 0;
    RSDS_L_OBJMOTIONTYPE_11_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_11_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_11_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_11_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_11_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_11_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_11_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured11Type {
    RSDS_L_OBJMEASURED_11_INIT = 0;
    RSDS_L_OBJMEASURED_11_MEASURED = 1;
    RSDS_L_OBJMEASURED_11_PREDICTED = 2;
    RSDS_L_OBJMEASURED_11_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection11Type {
    RSDS_L_OBJMOTIONDIRECTION_11_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_11_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_11_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_11_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_11_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_11_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_11_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_11_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint11Type {
    RSDS_L_OBJREFPOINT_11_REAR = 0;
    RSDS_L_OBJREFPOINT_11_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_11_LEFT = 2;
    RSDS_L_OBJREFPOINT_11_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_11_FRONT = 4;
    RSDS_L_OBJREFPOINT_11_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_11_RIGHT = 6;
    RSDS_L_OBJREFPOINT_11_REAR_RIGHT = 7;
  }
  enum RSDSLObjType12Type {
    RSDS_L_OBJTYPE_12_UNKNOWN = 0;
    RSDS_L_OBJTYPE_12_TRUCK = 1;
    RSDS_L_OBJTYPE_12_CAR = 2;
    RSDS_L_OBJTYPE_12_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_12_CYCLIST = 4;
    RSDS_L_OBJTYPE_12_RESERVED = 5;
  }
  enum RSDSLObjMotionType12Type {
    RSDS_L_OBJMOTIONTYPE_12_INT = 0;
    RSDS_L_OBJMOTIONTYPE_12_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_12_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_12_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_12_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_12_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_12_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_12_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured12Type {
    RSDS_L_OBJMEASURED_12_INIT = 0;
    RSDS_L_OBJMEASURED_12_MEASURED = 1;
    RSDS_L_OBJMEASURED_12_PREDICTED = 2;
    RSDS_L_OBJMEASURED_12_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection12Type {
    RSDS_L_OBJMOTIONDIRECTION_12_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_12_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_12_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_12_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_12_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_12_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_12_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_12_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint12Type {
    RSDS_L_OBJREFPOINT_12_REAR = 0;
    RSDS_L_OBJREFPOINT_12_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_12_LEFT = 2;
    RSDS_L_OBJREFPOINT_12_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_12_FRONT = 4;
    RSDS_L_OBJREFPOINT_12_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_12_RIGHT = 6;
    RSDS_L_OBJREFPOINT_12_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_11 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_11 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_11 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_11 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_11 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_11 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType11Type rsds_l_obj_type_11 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_11 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_11 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_11 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_11 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType11Type rsds_l_obj_motion_type_11 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured11Type rsds_l_obj_measured_11 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_11 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_11 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_11 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_11 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_11 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_11 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection11Type rsds_l_obj_motion_direction_11 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_11 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_11 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_11 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_11 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_11 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint11Type rsds_l_obj_ref_point_11 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_11 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_12 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_12 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_12 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_12 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_12 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_12 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType12Type rsds_l_obj_type_12 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_12 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_12 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_12 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_12 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType12Type rsds_l_obj_motion_type_12 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured12Type rsds_l_obj_measured_12 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_12 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_12 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_12 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_12 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_12 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_12 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection12Type rsds_l_obj_motion_direction_12 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_12 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_12 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_12 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_12 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_12 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint12Type rsds_l_obj_ref_point_12 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_12 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu6_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu6_rolling_counter8 = 71;
}

message RSDS_L_SGU7_586 {
// Report Message
  enum RSDSLObjType13Type {
    RSDS_L_OBJTYPE_13_UNKNOWN = 0;
    RSDS_L_OBJTYPE_13_TRUCK = 1;
    RSDS_L_OBJTYPE_13_CAR = 2;
    RSDS_L_OBJTYPE_13_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_13_CYCLIST = 4;
    RSDS_L_OBJTYPE_13_RESERVED = 5;
  }
  enum RSDSLObjMotionType13Type {
    RSDS_L_OBJMOTIONTYPE_13_INT = 0;
    RSDS_L_OBJMOTIONTYPE_13_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_13_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_13_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_13_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_13_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_13_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_13_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured13Type {
    RSDS_L_OBJMEASURED_13_INIT = 0;
    RSDS_L_OBJMEASURED_13_MEASURED = 1;
    RSDS_L_OBJMEASURED_13_PREDICTED = 2;
    RSDS_L_OBJMEASURED_13_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection13Type {
    RSDS_L_OBJMOTIONDIRECTION_13_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_13_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_13_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_13_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_13_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_13_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_13_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_13_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint13Type {
    RSDS_L_OBJREFPOINT_13_REAR = 0;
    RSDS_L_OBJREFPOINT_13_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_13_LEFT = 2;
    RSDS_L_OBJREFPOINT_13_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_13_FRONT = 4;
    RSDS_L_OBJREFPOINT_13_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_13_RIGHT = 6;
    RSDS_L_OBJREFPOINT_13_REAR_RIGHT = 7;
  }
  enum RSDSLObjType14Type {
    RSDS_L_OBJTYPE_14_UNKNOWN = 0;
    RSDS_L_OBJTYPE_14_TRUCK = 1;
    RSDS_L_OBJTYPE_14_CAR = 2;
    RSDS_L_OBJTYPE_14_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_14_CYCLIST = 4;
    RSDS_L_OBJTYPE_14_RESERVED = 5;
  }
  enum RSDSLObjMotionType14Type {
    RSDS_L_OBJMOTIONTYPE_14_INT = 0;
    RSDS_L_OBJMOTIONTYPE_14_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_14_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_14_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_14_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_14_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_14_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_14_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured14Type {
    RSDS_L_OBJMEASURED_14_INIT = 0;
    RSDS_L_OBJMEASURED_14_MEASURED = 1;
    RSDS_L_OBJMEASURED_14_PREDICTED = 2;
    RSDS_L_OBJMEASURED_14_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection14Type {
    RSDS_L_OBJMOTIONDIRECTION_14_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_14_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_14_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_14_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_14_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_14_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_14_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_14_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint14Type {
    RSDS_L_OBJREFPOINT_14_REAR = 0;
    RSDS_L_OBJREFPOINT_14_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_14_LEFT = 2;
    RSDS_L_OBJREFPOINT_14_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_14_FRONT = 4;
    RSDS_L_OBJREFPOINT_14_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_14_RIGHT = 6;
    RSDS_L_OBJREFPOINT_14_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_13 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_13 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_13 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_13 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_13 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_13 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType13Type rsds_l_obj_type_13 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_13 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_13 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_13 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_13 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType13Type rsds_l_obj_motion_type_13 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured13Type rsds_l_obj_measured_13 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_13 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_13 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_13 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_13 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_13 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_13 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection13Type rsds_l_obj_motion_direction_13 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_13 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_13 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_13 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_13 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_13 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint13Type rsds_l_obj_ref_point_13 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_13 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_14 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_14 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_14 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_14 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_14 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_14 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType14Type rsds_l_obj_type_14 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_14 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_14 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_14 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_14 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType14Type rsds_l_obj_motion_type_14 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured14Type rsds_l_obj_measured_14 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_14 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_14 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_14 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_14 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_14 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_14 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection14Type rsds_l_obj_motion_direction_14 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_14 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_14 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_14 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_14 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_14 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint14Type rsds_l_obj_ref_point_14 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_14 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu7_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu7_rolling_counter8 = 71;
}

message RSDS_L_SGU8_587 {
// Report Message
  enum RSDSLObjType15Type {
    RSDS_L_OBJTYPE_15_UNKNOWN = 0;
    RSDS_L_OBJTYPE_15_TRUCK = 1;
    RSDS_L_OBJTYPE_15_CAR = 2;
    RSDS_L_OBJTYPE_15_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_15_CYCLIST = 4;
    RSDS_L_OBJTYPE_15_RESERVED = 5;
  }
  enum RSDSLObjMotionType15Type {
    RSDS_L_OBJMOTIONTYPE_15_INT = 0;
    RSDS_L_OBJMOTIONTYPE_15_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_15_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_15_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_15_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_15_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_15_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_15_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured15Type {
    RSDS_L_OBJMEASURED_15_INIT = 0;
    RSDS_L_OBJMEASURED_15_MEASURED = 1;
    RSDS_L_OBJMEASURED_15_PREDICTED = 2;
    RSDS_L_OBJMEASURED_15_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection15Type {
    RSDS_L_OBJMOTIONDIRECTION_15_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_15_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_15_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_15_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_15_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_15_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_15_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_15_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint15Type {
    RSDS_L_OBJREFPOINT_15_REAR = 0;
    RSDS_L_OBJREFPOINT_15_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_15_LEFT = 2;
    RSDS_L_OBJREFPOINT_15_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_15_FRONT = 4;
    RSDS_L_OBJREFPOINT_15_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_15_RIGHT = 6;
    RSDS_L_OBJREFPOINT_15_REAR_RIGHT = 7;
  }
  enum RSDSLObjType16Type {
    RSDS_L_OBJTYPE_16_UNKNOWN = 0;
    RSDS_L_OBJTYPE_16_TRUCK = 1;
    RSDS_L_OBJTYPE_16_CAR = 2;
    RSDS_L_OBJTYPE_16_PEDESTRIAN = 3;
    RSDS_L_OBJTYPE_16_CYCLIST = 4;
    RSDS_L_OBJTYPE_16_RESERVED = 5;
  }
  enum RSDSLObjMotionType16Type {
    RSDS_L_OBJMOTIONTYPE_16_INT = 0;
    RSDS_L_OBJMOTIONTYPE_16_UNKNOWN = 1;
    RSDS_L_OBJMOTIONTYPE_16_DRIVE = 2;
    RSDS_L_OBJMOTIONTYPE_16_STOPPED = 3;
    RSDS_L_OBJMOTIONTYPE_16_STAND = 4;
    RSDS_L_OBJMOTIONTYPE_16_RESERVED = 5;
    RSDS_L_OBJMOTIONTYPE_16_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONTYPE_16_RESERVED_7 = 7;
  }
  enum RSDSLObjMeasured16Type {
    RSDS_L_OBJMEASURED_16_INIT = 0;
    RSDS_L_OBJMEASURED_16_MEASURED = 1;
    RSDS_L_OBJMEASURED_16_PREDICTED = 2;
    RSDS_L_OBJMEASURED_16_RESERVED = 3;
  }
  enum RSDSLObjMotionDirection16Type {
    RSDS_L_OBJMOTIONDIRECTION_16_INIT = 0;
    RSDS_L_OBJMOTIONDIRECTION_16_INVERSE_DIRECTION = 1;
    RSDS_L_OBJMOTIONDIRECTION_16_SAME_DIRECTION = 2;
    RSDS_L_OBJMOTIONDIRECTION_16_CROSS = 3;
    RSDS_L_OBJMOTIONDIRECTION_16_RESERVED = 4;
    RSDS_L_OBJMOTIONDIRECTION_16_RESERVED_5 = 5;
    RSDS_L_OBJMOTIONDIRECTION_16_RESERVED_6 = 6;
    RSDS_L_OBJMOTIONDIRECTION_16_RESERVED_7 = 7;
  }
  enum RSDSLObjRefPoint16Type {
    RSDS_L_OBJREFPOINT_16_REAR = 0;
    RSDS_L_OBJREFPOINT_16_REAR_LEFT = 1;
    RSDS_L_OBJREFPOINT_16_LEFT = 2;
    RSDS_L_OBJREFPOINT_16_FRONT_LEFT = 3;
    RSDS_L_OBJREFPOINT_16_FRONT = 4;
    RSDS_L_OBJREFPOINT_16_FRONT_RIGHT = 5;
    RSDS_L_OBJREFPOINT_16_RIGHT = 6;
    RSDS_L_OBJREFPOINT_16_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_15 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_15 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_15 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_15 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_15 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_15 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType15Type rsds_l_obj_type_15 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_15 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_15 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_15 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_15 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType15Type rsds_l_obj_motion_type_15 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured15Type rsds_l_obj_measured_15 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_15 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_15 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_15 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_15 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_15 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_15 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection15Type rsds_l_obj_motion_direction_15 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_15 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_15 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_15 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_15 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_15 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint15Type rsds_l_obj_ref_point_15 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_15 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_id_16 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_exist_prob_16 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_obj_obstacle_prob_16 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_l_obj_dist_x_16 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_l_obj_dist_y_16 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_x_std_16 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSLObjType16Type rsds_l_obj_type_16 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_x_16 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_l_obj_rel_vel_y_16 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_x_16 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_l_obj_rel_accel_y_16 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionType16Type rsds_l_obj_motion_type_16 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSLObjMeasured16Type rsds_l_obj_measured_16 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_l_obj_dist_y_std_16 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_x_std_16 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_rel_vel_y_std_16 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_x_std_16 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_l_obj_r_accel_y_std_16 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_l_obj_width_16 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSLObjMotionDirection16Type rsds_l_obj_motion_direction_16 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_obj_age_16 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_l_obj_length_16 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_heading_16 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_l_obj_yawrate_16 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_l_obj_rcs_16 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSLObjRefPoint16Type rsds_l_obj_ref_point_16 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_l_hwa_target_validity_16 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu8_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu8_rolling_counter8 = 71;
}

message RSDS_R_SGU1_590 {
// Report Message
  enum RSDSRObjType1Type {
    RSDS_R_OBJTYPE_1_UNKNOWN = 0;
    RSDS_R_OBJTYPE_1_TRUCK = 1;
    RSDS_R_OBJTYPE_1_CAR = 2;
    RSDS_R_OBJTYPE_1_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_1_CYCLIST = 4;
    RSDS_R_OBJTYPE_1_RESERVED = 5;
  }
  enum RSDSRObjMotionType1Type {
    RSDS_R_OBJMOTIONTYPE_1_INT = 0;
    RSDS_R_OBJMOTIONTYPE_1_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_1_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_1_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_1_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_1_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_1_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_1_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured1Type {
    RSDS_R_OBJMEASURED_1_INIT = 0;
    RSDS_R_OBJMEASURED_1_MEASURED = 1;
    RSDS_R_OBJMEASURED_1_PREDICTED = 2;
    RSDS_R_OBJMEASURED_1_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection1Type {
    RSDS_R_OBJMOTIONDIRECTION_1_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_1_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_1_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_1_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_1_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_1_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_1_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_1_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint1Type {
    RSDS_R_OBJREFPOINT_1_REAR = 0;
    RSDS_R_OBJREFPOINT_1_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_1_LEFT = 2;
    RSDS_R_OBJREFPOINT_1_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_1_FRONT = 4;
    RSDS_R_OBJREFPOINT_1_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_1_RIGHT = 6;
    RSDS_R_OBJREFPOINT_1_REAR_RIGHT = 7;
  }
  enum RSDSRObjType2Type {
    RSDS_R_OBJTYPE_2_UNKNOWN = 0;
    RSDS_R_OBJTYPE_2_TRUCK = 1;
    RSDS_R_OBJTYPE_2_CAR = 2;
    RSDS_R_OBJTYPE_2_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_2_CYCLIST = 4;
    RSDS_R_OBJTYPE_2_RESERVED = 5;
  }
  enum RSDSRObjMotionType2Type {
    RSDS_R_OBJMOTIONTYPE_2_INT = 0;
    RSDS_R_OBJMOTIONTYPE_2_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_2_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_2_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_2_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_2_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_2_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_2_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured2Type {
    RSDS_R_OBJMEASURED_2_INIT = 0;
    RSDS_R_OBJMEASURED_2_MEASURED = 1;
    RSDS_R_OBJMEASURED_2_PREDICTED = 2;
    RSDS_R_OBJMEASURED_2_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection2Type {
    RSDS_R_OBJMOTIONDIRECTION_2_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_2_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_2_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_2_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_2_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_2_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_2_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_2_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint2Type {
    RSDS_R_OBJREFPOINT_2_REAR = 0;
    RSDS_R_OBJREFPOINT_2_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_2_LEFT = 2;
    RSDS_R_OBJREFPOINT_2_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_2_FRONT = 4;
    RSDS_R_OBJREFPOINT_2_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_2_RIGHT = 6;
    RSDS_R_OBJREFPOINT_2_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_1 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_1 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_1 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_1 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_1 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_1 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType1Type rsds_r_obj_type_1 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_1 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_1 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_1 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_1 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType1Type rsds_r_obj_motion_type_1 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured1Type rsds_r_obj_measured_1 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_1 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_1 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_1 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_1 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_1 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_1 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection1Type rsds_r_obj_motion_direction_1 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_1 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_1 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_1 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_1 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_1 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint1Type rsds_r_obj_ref_point_1 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_1 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_2 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_2 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_2 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_2 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_2 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_2 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType2Type rsds_r_obj_type_2 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_2 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_2 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_2 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_2 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType2Type rsds_r_obj_motion_type_2 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured2Type rsds_r_obj_measured_2 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_2 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_2 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_2 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_2 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_2 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_2 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection2Type rsds_r_obj_motion_direction_2 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_2 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_2 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_2 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_2 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_2 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint2Type rsds_r_obj_ref_point_2 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_2 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu1_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu1_rolling_counter8 = 71;
}

message RSDS_R_SGU2_591 {
// Report Message
  enum RSDSRObjType3Type {
    RSDS_R_OBJTYPE_3_UNKNOWN = 0;
    RSDS_R_OBJTYPE_3_TRUCK = 1;
    RSDS_R_OBJTYPE_3_CAR = 2;
    RSDS_R_OBJTYPE_3_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_3_CYCLIST = 4;
    RSDS_R_OBJTYPE_3_RESERVED = 5;
  }
  enum RSDSRObjMotionType3Type {
    RSDS_R_OBJMOTIONTYPE_3_INT = 0;
    RSDS_R_OBJMOTIONTYPE_3_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_3_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_3_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_3_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_3_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_3_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_3_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured3Type {
    RSDS_R_OBJMEASURED_3_INIT = 0;
    RSDS_R_OBJMEASURED_3_MEASURED = 1;
    RSDS_R_OBJMEASURED_3_PREDICTED = 2;
    RSDS_R_OBJMEASURED_3_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection3Type {
    RSDS_R_OBJMOTIONDIRECTION_3_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_3_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_3_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_3_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_3_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_3_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_3_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_3_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint3Type {
    RSDS_R_OBJREFPOINT_3_REAR = 0;
    RSDS_R_OBJREFPOINT_3_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_3_LEFT = 2;
    RSDS_R_OBJREFPOINT_3_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_3_FRONT = 4;
    RSDS_R_OBJREFPOINT_3_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_3_RIGHT = 6;
    RSDS_R_OBJREFPOINT_3_REAR_RIGHT = 7;
  }
  enum RSDSRObjType4Type {
    RSDS_R_OBJTYPE_4_UNKNOWN = 0;
    RSDS_R_OBJTYPE_4_TRUCK = 1;
    RSDS_R_OBJTYPE_4_CAR = 2;
    RSDS_R_OBJTYPE_4_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_4_CYCLIST = 4;
    RSDS_R_OBJTYPE_4_RESERVED = 5;
  }
  enum RSDSRObjMotionType4Type {
    RSDS_R_OBJMOTIONTYPE_4_INT = 0;
    RSDS_R_OBJMOTIONTYPE_4_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_4_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_4_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_4_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_4_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_4_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_4_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured4Type {
    RSDS_R_OBJMEASURED_4_INIT = 0;
    RSDS_R_OBJMEASURED_4_MEASURED = 1;
    RSDS_R_OBJMEASURED_4_PREDICTED = 2;
    RSDS_R_OBJMEASURED_4_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection4Type {
    RSDS_R_OBJMOTIONDIRECTION_4_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_4_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_4_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_4_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_4_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_4_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_4_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_4_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint4Type {
    RSDS_R_OBJREFPOINT_4_REAR = 0;
    RSDS_R_OBJREFPOINT_4_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_4_LEFT = 2;
    RSDS_R_OBJREFPOINT_4_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_4_FRONT = 4;
    RSDS_R_OBJREFPOINT_4_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_4_RIGHT = 6;
    RSDS_R_OBJREFPOINT_4_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_3 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_3 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_3 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_3 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_3 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_3 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType3Type rsds_r_obj_type_3 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_3 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_3 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_3 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_3 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType3Type rsds_r_obj_motion_type_3 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured3Type rsds_r_obj_measured_3 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_3 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_3 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_3 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_3 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_3 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_3 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection3Type rsds_r_obj_motion_direction_3 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_3 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_3 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_3 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_3 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_3 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint3Type rsds_r_obj_ref_point_3 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_3 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_4 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_4 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_4 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_4 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_4 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_4 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType4Type rsds_r_obj_type_4 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_4 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_4 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_4 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_4 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType4Type rsds_r_obj_motion_type_4 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured4Type rsds_r_obj_measured_4 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_4 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_4 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_4 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_4 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_4 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_4 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection4Type rsds_r_obj_motion_direction_4 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_4 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_4 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_4 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_4 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_4 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint4Type rsds_r_obj_ref_point_4 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_4 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu2_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu2_rolling_counter8 = 71;
}

message RSDS_R_SGU3_592 {
// Report Message
  enum RSDSRObjType5Type {
    RSDS_R_OBJTYPE_5_UNKNOWN = 0;
    RSDS_R_OBJTYPE_5_TRUCK = 1;
    RSDS_R_OBJTYPE_5_CAR = 2;
    RSDS_R_OBJTYPE_5_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_5_CYCLIST = 4;
    RSDS_R_OBJTYPE_5_RESERVED = 5;
  }
  enum RSDSRObjMotionType5Type {
    RSDS_R_OBJMOTIONTYPE_5_INT = 0;
    RSDS_R_OBJMOTIONTYPE_5_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_5_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_5_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_5_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_5_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_5_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_5_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured5Type {
    RSDS_R_OBJMEASURED_5_INIT = 0;
    RSDS_R_OBJMEASURED_5_MEASURED = 1;
    RSDS_R_OBJMEASURED_5_PREDICTED = 2;
    RSDS_R_OBJMEASURED_5_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection5Type {
    RSDS_R_OBJMOTIONDIRECTION_5_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_5_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_5_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_5_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_5_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_5_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_5_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_5_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint5Type {
    RSDS_R_OBJREFPOINT_5_REAR = 0;
    RSDS_R_OBJREFPOINT_5_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_5_LEFT = 2;
    RSDS_R_OBJREFPOINT_5_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_5_FRONT = 4;
    RSDS_R_OBJREFPOINT_5_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_5_RIGHT = 6;
    RSDS_R_OBJREFPOINT_5_REAR_RIGHT = 7;
  }
  enum RSDSRObjType6Type {
    RSDS_R_OBJTYPE_6_UNKNOWN = 0;
    RSDS_R_OBJTYPE_6_TRUCK = 1;
    RSDS_R_OBJTYPE_6_CAR = 2;
    RSDS_R_OBJTYPE_6_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_6_CYCLIST = 4;
    RSDS_R_OBJTYPE_6_RESERVED = 5;
  }
  enum RSDSRObjMotionType6Type {
    RSDS_R_OBJMOTIONTYPE_6_INT = 0;
    RSDS_R_OBJMOTIONTYPE_6_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_6_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_6_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_6_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_6_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_6_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_6_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured6Type {
    RSDS_R_OBJMEASURED_6_INIT = 0;
    RSDS_R_OBJMEASURED_6_MEASURED = 1;
    RSDS_R_OBJMEASURED_6_PREDICTED = 2;
    RSDS_R_OBJMEASURED_6_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection6Type {
    RSDS_R_OBJMOTIONDIRECTION_6_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_6_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_6_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_6_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_6_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_6_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_6_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_6_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint6Type {
    RSDS_R_OBJREFPOINT_6_REAR = 0;
    RSDS_R_OBJREFPOINT_6_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_6_LEFT = 2;
    RSDS_R_OBJREFPOINT_6_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_6_FRONT = 4;
    RSDS_R_OBJREFPOINT_6_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_6_RIGHT = 6;
    RSDS_R_OBJREFPOINT_6_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_5 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_5 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_5 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_5 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_5 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_5 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType5Type rsds_r_obj_type_5 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_5 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_5 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_5 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_5 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType5Type rsds_r_obj_motion_type_5 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured5Type rsds_r_obj_measured_5 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_5 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_5 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_5 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_5 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_5 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_5 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection5Type rsds_r_obj_motion_direction_5 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_5 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_5 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_5 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_5 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_5 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint5Type rsds_r_obj_ref_point_5 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_5 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_6 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_6 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_6 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_6 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_6 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_6 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType6Type rsds_r_obj_type_6 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_6 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_6 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_6 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_6 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType6Type rsds_r_obj_motion_type_6 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured6Type rsds_r_obj_measured_6 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_6 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_6 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_6 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_6 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_6 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_6 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection6Type rsds_r_obj_motion_direction_6 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_6 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_6 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_6 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_6 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_6 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint6Type rsds_r_obj_ref_point_6 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_6 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu3_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu3_rolling_counter8 = 71;
}

message RSDS_R_SGU4_593 {
// Report Message
  enum RSDSRObjType7Type {
    RSDS_R_OBJTYPE_7_UNKNOWN = 0;
    RSDS_R_OBJTYPE_7_TRUCK = 1;
    RSDS_R_OBJTYPE_7_CAR = 2;
    RSDS_R_OBJTYPE_7_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_7_CYCLIST = 4;
    RSDS_R_OBJTYPE_7_RESERVED = 5;
  }
  enum RSDSRObjMotionType7Type {
    RSDS_R_OBJMOTIONTYPE_7_INT = 0;
    RSDS_R_OBJMOTIONTYPE_7_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_7_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_7_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_7_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_7_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_7_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_7_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured7Type {
    RSDS_R_OBJMEASURED_7_INIT = 0;
    RSDS_R_OBJMEASURED_7_MEASURED = 1;
    RSDS_R_OBJMEASURED_7_PREDICTED = 2;
    RSDS_R_OBJMEASURED_7_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection7Type {
    RSDS_R_OBJMOTIONDIRECTION_7_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_7_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_7_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_7_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_7_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_7_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_7_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_7_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint7Type {
    RSDS_R_OBJREFPOINT_7_REAR = 0;
    RSDS_R_OBJREFPOINT_7_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_7_LEFT = 2;
    RSDS_R_OBJREFPOINT_7_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_7_FRONT = 4;
    RSDS_R_OBJREFPOINT_7_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_7_RIGHT = 6;
    RSDS_R_OBJREFPOINT_7_REAR_RIGHT = 7;
  }
  enum RSDSRObjType8Type {
    RSDS_R_OBJTYPE_8_UNKNOWN = 0;
    RSDS_R_OBJTYPE_8_TRUCK = 1;
    RSDS_R_OBJTYPE_8_CAR = 2;
    RSDS_R_OBJTYPE_8_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_8_CYCLIST = 4;
    RSDS_R_OBJTYPE_8_RESERVED = 5;
  }
  enum RSDSRObjMotionType8Type {
    RSDS_R_OBJMOTIONTYPE_8_INT = 0;
    RSDS_R_OBJMOTIONTYPE_8_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_8_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_8_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_8_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_8_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_8_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_8_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured8Type {
    RSDS_R_OBJMEASURED_8_INIT = 0;
    RSDS_R_OBJMEASURED_8_MEASURED = 1;
    RSDS_R_OBJMEASURED_8_PREDICTED = 2;
    RSDS_R_OBJMEASURED_8_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection8Type {
    RSDS_R_OBJMOTIONDIRECTION_8_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_8_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_8_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_8_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_8_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_8_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_8_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_8_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint8Type {
    RSDS_R_OBJREFPOINT_8_REAR = 0;
    RSDS_R_OBJREFPOINT_8_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_8_LEFT = 2;
    RSDS_R_OBJREFPOINT_8_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_8_FRONT = 4;
    RSDS_R_OBJREFPOINT_8_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_8_RIGHT = 6;
    RSDS_R_OBJREFPOINT_8_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_7 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_7 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_7 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_7 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_7 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_7 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType7Type rsds_r_obj_type_7 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_7 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_7 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_7 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_7 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType7Type rsds_r_obj_motion_type_7 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured7Type rsds_r_obj_measured_7 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_7 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_7 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_7 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_7 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_7 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_7 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection7Type rsds_r_obj_motion_direction_7 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_7 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_7 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_7 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_7 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_7 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint7Type rsds_r_obj_ref_point_7 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_7 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_8 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_8 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_8 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_8 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_8 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_8 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType8Type rsds_r_obj_type_8 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_8 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_8 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_8 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_8 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType8Type rsds_r_obj_motion_type_8 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured8Type rsds_r_obj_measured_8 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_8 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_8 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_8 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_8 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_8 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_8 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection8Type rsds_r_obj_motion_direction_8 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_8 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_8 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_8 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_8 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_8 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint8Type rsds_r_obj_ref_point_8 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_8 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu4_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu4_rolling_counter8 = 71;
}

message RSDS_R_SGU5_594 {
// Report Message
  enum RSDSRObjType9Type {
    RSDS_R_OBJTYPE_9_UNKNOWN = 0;
    RSDS_R_OBJTYPE_9_TRUCK = 1;
    RSDS_R_OBJTYPE_9_CAR = 2;
    RSDS_R_OBJTYPE_9_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_9_CYCLIST = 4;
    RSDS_R_OBJTYPE_9_RESERVED = 5;
  }
  enum RSDSRObjMotionType9Type {
    RSDS_R_OBJMOTIONTYPE_9_INT = 0;
    RSDS_R_OBJMOTIONTYPE_9_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_9_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_9_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_9_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_9_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_9_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_9_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured9Type {
    RSDS_R_OBJMEASURED_9_INIT = 0;
    RSDS_R_OBJMEASURED_9_MEASURED = 1;
    RSDS_R_OBJMEASURED_9_PREDICTED = 2;
    RSDS_R_OBJMEASURED_9_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection9Type {
    RSDS_R_OBJMOTIONDIRECTION_9_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_9_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_9_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_9_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_9_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_9_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_9_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_9_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint9Type {
    RSDS_R_OBJREFPOINT_9_REAR = 0;
    RSDS_R_OBJREFPOINT_9_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_9_LEFT = 2;
    RSDS_R_OBJREFPOINT_9_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_9_FRONT = 4;
    RSDS_R_OBJREFPOINT_9_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_9_RIGHT = 6;
    RSDS_R_OBJREFPOINT_9_REAR_RIGHT = 7;
  }
  enum RSDSRObjType10Type {
    RSDS_R_OBJTYPE_10_UNKNOWN = 0;
    RSDS_R_OBJTYPE_10_TRUCK = 1;
    RSDS_R_OBJTYPE_10_CAR = 2;
    RSDS_R_OBJTYPE_10_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_10_CYCLIST = 4;
    RSDS_R_OBJTYPE_10_RESERVED = 5;
  }
  enum RSDSRObjMotionType10Type {
    RSDS_R_OBJMOTIONTYPE_10_INT = 0;
    RSDS_R_OBJMOTIONTYPE_10_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_10_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_10_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_10_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_10_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_10_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_10_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured10Type {
    RSDS_R_OBJMEASURED_10_INIT = 0;
    RSDS_R_OBJMEASURED_10_MEASURED = 1;
    RSDS_R_OBJMEASURED_10_PREDICTED = 2;
    RSDS_R_OBJMEASURED_10_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection10Type {
    RSDS_R_OBJMOTIONDIRECTION_10_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_10_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_10_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_10_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_10_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_10_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_10_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_10_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint10Type {
    RSDS_R_OBJREFPOINT_10_REAR = 0;
    RSDS_R_OBJREFPOINT_10_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_10_LEFT = 2;
    RSDS_R_OBJREFPOINT_10_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_10_FRONT = 4;
    RSDS_R_OBJREFPOINT_10_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_10_RIGHT = 6;
    RSDS_R_OBJREFPOINT_10_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_9 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_9 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_9 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_9 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_9 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_9 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType9Type rsds_r_obj_type_9 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_9 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_9 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_9 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_9 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType9Type rsds_r_obj_motion_type_9 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured9Type rsds_r_obj_measured_9 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_9 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_9 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_9 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_9 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_9 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_9 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection9Type rsds_r_obj_motion_direction_9 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_9 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_9 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_9 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_9 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_9 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint9Type rsds_r_obj_ref_point_9 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_9 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_10 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_10 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_10 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_10 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_10 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_10 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType10Type rsds_r_obj_type_10 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_10 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_10 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_10 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_10 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType10Type rsds_r_obj_motion_type_10 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured10Type rsds_r_obj_measured_10 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_10 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_10 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_10 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_10 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_10 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_10 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection10Type rsds_r_obj_motion_direction_10 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_10 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_10 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_10 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_10 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_10 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint10Type rsds_r_obj_ref_point_10 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_10 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu5_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu5_rolling_counter8 = 71;
}

message RSDS_R_SGU6_595 {
// Report Message
  enum RSDSRObjType11Type {
    RSDS_R_OBJTYPE_11_UNKNOWN = 0;
    RSDS_R_OBJTYPE_11_TRUCK = 1;
    RSDS_R_OBJTYPE_11_CAR = 2;
    RSDS_R_OBJTYPE_11_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_11_CYCLIST = 4;
    RSDS_R_OBJTYPE_11_RESERVED = 5;
  }
  enum RSDSRObjMotionType11Type {
    RSDS_R_OBJMOTIONTYPE_11_INT = 0;
    RSDS_R_OBJMOTIONTYPE_11_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_11_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_11_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_11_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_11_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_11_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_11_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured11Type {
    RSDS_R_OBJMEASURED_11_INIT = 0;
    RSDS_R_OBJMEASURED_11_MEASURED = 1;
    RSDS_R_OBJMEASURED_11_PREDICTED = 2;
    RSDS_R_OBJMEASURED_11_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection11Type {
    RSDS_R_OBJMOTIONDIRECTION_11_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_11_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_11_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_11_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_11_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_11_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_11_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_11_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint11Type {
    RSDS_R_OBJREFPOINT_11_REAR = 0;
    RSDS_R_OBJREFPOINT_11_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_11_LEFT = 2;
    RSDS_R_OBJREFPOINT_11_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_11_FRONT = 4;
    RSDS_R_OBJREFPOINT_11_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_11_RIGHT = 6;
    RSDS_R_OBJREFPOINT_11_REAR_RIGHT = 7;
  }
  enum RSDSRObjType12Type {
    RSDS_R_OBJTYPE_12_UNKNOWN = 0;
    RSDS_R_OBJTYPE_12_TRUCK = 1;
    RSDS_R_OBJTYPE_12_CAR = 2;
    RSDS_R_OBJTYPE_12_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_12_CYCLIST = 4;
    RSDS_R_OBJTYPE_12_RESERVED = 5;
  }
  enum RSDSRObjMotionType12Type {
    RSDS_R_OBJMOTIONTYPE_12_INT = 0;
    RSDS_R_OBJMOTIONTYPE_12_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_12_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_12_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_12_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_12_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_12_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_12_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured12Type {
    RSDS_R_OBJMEASURED_12_INIT = 0;
    RSDS_R_OBJMEASURED_12_MEASURED = 1;
    RSDS_R_OBJMEASURED_12_PREDICTED = 2;
    RSDS_R_OBJMEASURED_12_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection12Type {
    RSDS_R_OBJMOTIONDIRECTION_12_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_12_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_12_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_12_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_12_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_12_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_12_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_12_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint12Type {
    RSDS_R_OBJREFPOINT_12_REAR = 0;
    RSDS_R_OBJREFPOINT_12_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_12_LEFT = 2;
    RSDS_R_OBJREFPOINT_12_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_12_FRONT = 4;
    RSDS_R_OBJREFPOINT_12_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_12_RIGHT = 6;
    RSDS_R_OBJREFPOINT_12_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_11 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_11 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_11 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_11 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_11 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_11 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType11Type rsds_r_obj_type_11 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_11 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_11 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_11 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_11 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType11Type rsds_r_obj_motion_type_11 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured11Type rsds_r_obj_measured_11 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_11 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_11 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_11 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_11 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_11 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_11 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection11Type rsds_r_obj_motion_direction_11 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_11 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_11 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_11 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_11 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_11 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint11Type rsds_r_obj_ref_point_11 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_11 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_12 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_12 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_12 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_12 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_12 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_12 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType12Type rsds_r_obj_type_12 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_12 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_12 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_12 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_12 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType12Type rsds_r_obj_motion_type_12 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured12Type rsds_r_obj_measured_12 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_12 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_12 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_12 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_12 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_12 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_12 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection12Type rsds_r_obj_motion_direction_12 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_12 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_12 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_12 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_12 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_12 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint12Type rsds_r_obj_ref_point_12 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_12 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu6_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu6_rolling_counter8 = 71;
}

message RSDS_R_SGU7_596 {
// Report Message
  enum RSDSRObjType13Type {
    RSDS_R_OBJTYPE_13_UNKNOWN = 0;
    RSDS_R_OBJTYPE_13_TRUCK = 1;
    RSDS_R_OBJTYPE_13_CAR = 2;
    RSDS_R_OBJTYPE_13_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_13_CYCLIST = 4;
    RSDS_R_OBJTYPE_13_RESERVED = 5;
  }
  enum RSDSRObjMotionType13Type {
    RSDS_R_OBJMOTIONTYPE_13_INT = 0;
    RSDS_R_OBJMOTIONTYPE_13_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_13_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_13_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_13_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_13_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_13_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_13_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured13Type {
    RSDS_R_OBJMEASURED_13_INIT = 0;
    RSDS_R_OBJMEASURED_13_MEASURED = 1;
    RSDS_R_OBJMEASURED_13_PREDICTED = 2;
    RSDS_R_OBJMEASURED_13_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection13Type {
    RSDS_R_OBJMOTIONDIRECTION_13_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_13_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_13_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_13_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_13_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_13_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_13_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_13_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint13Type {
    RSDS_R_OBJREFPOINT_13_REAR = 0;
    RSDS_R_OBJREFPOINT_13_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_13_LEFT = 2;
    RSDS_R_OBJREFPOINT_13_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_13_FRONT = 4;
    RSDS_R_OBJREFPOINT_13_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_13_RIGHT = 6;
    RSDS_R_OBJREFPOINT_13_REAR_RIGHT = 7;
  }
  enum RSDSRObjType14Type {
    RSDS_R_OBJTYPE_14_UNKNOWN = 0;
    RSDS_R_OBJTYPE_14_TRUCK = 1;
    RSDS_R_OBJTYPE_14_CAR = 2;
    RSDS_R_OBJTYPE_14_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_14_CYCLIST = 4;
    RSDS_R_OBJTYPE_14_RESERVED = 5;
  }
  enum RSDSRObjMotionType14Type {
    RSDS_R_OBJMOTIONTYPE_14_INT = 0;
    RSDS_R_OBJMOTIONTYPE_14_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_14_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_14_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_14_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_14_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_14_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_14_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured14Type {
    RSDS_R_OBJMEASURED_14_INIT = 0;
    RSDS_R_OBJMEASURED_14_MEASURED = 1;
    RSDS_R_OBJMEASURED_14_PREDICTED = 2;
    RSDS_R_OBJMEASURED_14_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection14Type {
    RSDS_R_OBJMOTIONDIRECTION_14_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_14_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_14_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_14_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_14_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_14_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_14_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_14_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint14Type {
    RSDS_R_OBJREFPOINT_14_REAR = 0;
    RSDS_R_OBJREFPOINT_14_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_14_LEFT = 2;
    RSDS_R_OBJREFPOINT_14_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_14_FRONT = 4;
    RSDS_R_OBJREFPOINT_14_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_14_RIGHT = 6;
    RSDS_R_OBJREFPOINT_14_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_13 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_13 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_13 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_13 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_13 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_13 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType13Type rsds_r_obj_type_13 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_13 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_13 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_13 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_13 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType13Type rsds_r_obj_motion_type_13 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured13Type rsds_r_obj_measured_13 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_13 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_13 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_13 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_13 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_13 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_13 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection13Type rsds_r_obj_motion_direction_13 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_13 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_13 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_13 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_13 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_13 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint13Type rsds_r_obj_ref_point_13 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_13 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_14 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_14 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_14 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_14 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_14 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_14 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType14Type rsds_r_obj_type_14 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_14 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_14 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_14 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_14 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType14Type rsds_r_obj_motion_type_14 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured14Type rsds_r_obj_measured_14 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_14 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_14 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_14 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_14 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_14 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_14 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection14Type rsds_r_obj_motion_direction_14 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_14 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_14 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_14 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_14 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_14 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint14Type rsds_r_obj_ref_point_14 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_14 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu7_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu7_rolling_counter8 = 71;
}

message RSDS_R_SGU8_597 {
// Report Message
  enum RSDSRObjType15Type {
    RSDS_R_OBJTYPE_15_UNKNOWN = 0;
    RSDS_R_OBJTYPE_15_TRUCK = 1;
    RSDS_R_OBJTYPE_15_CAR = 2;
    RSDS_R_OBJTYPE_15_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_15_CYCLIST = 4;
    RSDS_R_OBJTYPE_15_RESERVED = 5;
  }
  enum RSDSRObjMotionType15Type {
    RSDS_R_OBJMOTIONTYPE_15_INT = 0;
    RSDS_R_OBJMOTIONTYPE_15_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_15_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_15_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_15_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_15_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_15_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_15_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured15Type {
    RSDS_R_OBJMEASURED_15_INIT = 0;
    RSDS_R_OBJMEASURED_15_MEASURED = 1;
    RSDS_R_OBJMEASURED_15_PREDICTED = 2;
    RSDS_R_OBJMEASURED_15_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection15Type {
    RSDS_R_OBJMOTIONDIRECTION_15_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_15_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_15_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_15_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_15_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_15_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_15_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_15_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint15Type {
    RSDS_R_OBJREFPOINT_15_REAR = 0;
    RSDS_R_OBJREFPOINT_15_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_15_LEFT = 2;
    RSDS_R_OBJREFPOINT_15_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_15_FRONT = 4;
    RSDS_R_OBJREFPOINT_15_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_15_RIGHT = 6;
    RSDS_R_OBJREFPOINT_15_REAR_RIGHT = 7;
  }
  enum RSDSRObjType16Type {
    RSDS_R_OBJTYPE_16_UNKNOWN = 0;
    RSDS_R_OBJTYPE_16_TRUCK = 1;
    RSDS_R_OBJTYPE_16_CAR = 2;
    RSDS_R_OBJTYPE_16_PEDESTRIAN = 3;
    RSDS_R_OBJTYPE_16_CYCLIST = 4;
    RSDS_R_OBJTYPE_16_RESERVED = 5;
  }
  enum RSDSRObjMotionType16Type {
    RSDS_R_OBJMOTIONTYPE_16_INT = 0;
    RSDS_R_OBJMOTIONTYPE_16_UNKNOWN = 1;
    RSDS_R_OBJMOTIONTYPE_16_DRIVE = 2;
    RSDS_R_OBJMOTIONTYPE_16_STOPPED = 3;
    RSDS_R_OBJMOTIONTYPE_16_STAND = 4;
    RSDS_R_OBJMOTIONTYPE_16_RESERVED = 5;
    RSDS_R_OBJMOTIONTYPE_16_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONTYPE_16_RESERVED_7 = 7;
  }
  enum RSDSRObjMeasured16Type {
    RSDS_R_OBJMEASURED_16_INIT = 0;
    RSDS_R_OBJMEASURED_16_MEASURED = 1;
    RSDS_R_OBJMEASURED_16_PREDICTED = 2;
    RSDS_R_OBJMEASURED_16_RESERVED = 3;
  }
  enum RSDSRObjMotionDirection16Type {
    RSDS_R_OBJMOTIONDIRECTION_16_INIT = 0;
    RSDS_R_OBJMOTIONDIRECTION_16_INVERSE_DIRECTION = 1;
    RSDS_R_OBJMOTIONDIRECTION_16_SAME_DIRECTION = 2;
    RSDS_R_OBJMOTIONDIRECTION_16_CROSS = 3;
    RSDS_R_OBJMOTIONDIRECTION_16_RESERVED = 4;
    RSDS_R_OBJMOTIONDIRECTION_16_RESERVED_5 = 5;
    RSDS_R_OBJMOTIONDIRECTION_16_RESERVED_6 = 6;
    RSDS_R_OBJMOTIONDIRECTION_16_RESERVED_7 = 7;
  }
  enum RSDSRObjRefPoint16Type {
    RSDS_R_OBJREFPOINT_16_REAR = 0;
    RSDS_R_OBJREFPOINT_16_REAR_LEFT = 1;
    RSDS_R_OBJREFPOINT_16_LEFT = 2;
    RSDS_R_OBJREFPOINT_16_FRONT_LEFT = 3;
    RSDS_R_OBJREFPOINT_16_FRONT = 4;
    RSDS_R_OBJREFPOINT_16_FRONT_RIGHT = 5;
    RSDS_R_OBJREFPOINT_16_RIGHT = 6;
    RSDS_R_OBJREFPOINT_16_REAR_RIGHT = 7;
  }
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_15 = 1;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_15 = 2;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_15 = 3;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_15 = 4;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_15 = 5;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_15 = 6;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType15Type rsds_r_obj_type_15 = 7;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_15 = 8;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_15 = 9;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_15 = 10;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_15 = 11;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType15Type rsds_r_obj_motion_type_15 = 12;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured15Type rsds_r_obj_measured_15 = 13;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_15 = 14;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_15 = 15;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_15 = 16;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_15 = 17;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_15 = 18;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_15 = 19;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection15Type rsds_r_obj_motion_direction_15 = 20;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_15 = 21;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_15 = 22;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_15 = 23;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_15 = 24;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_15 = 25;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint15Type rsds_r_obj_ref_point_15 = 26;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_15 = 27;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_id_16 = 28;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_exist_prob_16 = 29;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_obj_obstacle_prob_16 = 30;
  // description / [m] [-128|127.9375] [initial_value:0]
  optional double rsds_r_obj_dist_x_16 = 31;
  // description / [m] [-128|127.875] [initial_value:1024]
  optional double rsds_r_obj_dist_y_16 = 32;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_x_std_16 = 33;
  // description / [] [0|15] [initial_value:0]
  optional RSDSRObjType16Type rsds_r_obj_type_16 = 34;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_x_16 = 35;
  // description / [mps] [-128|126.9976] [initial_value:4102]
  optional double rsds_r_obj_rel_vel_y_16 = 36;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_x_16 = 37;
  // description / [mps2] [-16|15.96875] [initial_value:512]
  optional double rsds_r_obj_rel_accel_y_16 = 38;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionType16Type rsds_r_obj_motion_type_16 = 39;
  // description / [] [0|3] [initial_value:1]
  optional RSDSRObjMeasured16Type rsds_r_obj_measured_16 = 40;
  // description / [m] [0|6] [initial_value:0]
  optional double rsds_r_obj_dist_y_std_16 = 41;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_x_std_16 = 42;
  // description / [mps] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_rel_vel_y_std_16 = 43;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_x_std_16 = 44;
  // description / [mps2] [0|3.9375] [initial_value:0]
  optional double rsds_r_obj_r_accel_y_std_16 = 45;
  // description / [m] [0|5] [initial_value:0]
  optional double rsds_r_obj_width_16 = 46;
  // description / [] [0|7] [initial_value:0]
  optional RSDSRObjMotionDirection16Type rsds_r_obj_motion_direction_16 = 47;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_obj_age_16 = 48;
  // description / [m] [0|20] [initial_value:0]
  optional double rsds_r_obj_length_16 = 49;
  // description / [rad] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_heading_16 = 50;
  // description / [rad/s] [-3.14|3.14] [initial_value:0]
  optional double rsds_r_obj_yawrate_16 = 51;
  // description / [dBm2] [-50|70] [initial_value:400]
  optional double rsds_r_obj_rcs_16 = 52;
  // description / [] [0|8] [initial_value:0]
  optional RSDSRObjRefPoint16Type rsds_r_obj_ref_point_16 = 53;
  // description / [] [0|1] [initial_value:0]
  optional bool rsds_r_hwa_target_validity_16 = 54;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_block_counter = 55;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum1 = 56;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum2 = 57;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum3 = 58;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum4 = 59;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum5 = 60;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum6 = 61;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum7 = 62;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu8_checksum8 = 63;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter1 = 64;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter2 = 65;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter3 = 66;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter4 = 67;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter5 = 68;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter6 = 69;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter7 = 70;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu8_rolling_counter8 = 71;
}

message RSDS_L_SGU_HEAD_5CE {
// Report Message
  enum RSDSLhdrErrorStatusType {
    RSDS_L_HDR_ERRORSTATUS_NO_ERROR = 0;
    RSDS_L_HDR_ERRORSTATUS_NOT_DEFINED = 1;
    RSDS_L_HDR_ERRORSTATUS_MISALIGNMENT_ELEVATION = 2;
    RSDS_L_HDR_ERRORSTATUS_MISALIGNMENT_AZIMUTH = 3;
    RSDS_L_HDR_ERRORSTATUS_TEMPORARY_FAILURE = 4;
    RSDS_L_HDR_ERRORSTATUS_PERMANENT_FAILURE = 5;
    RSDS_L_HDR_ERRORSTATUS_VOLTAGE_ERROR = 6;
    RSDS_L_HDR_ERRORSTATUS_PRODUCTION_MODE = 7;
    RSDS_L_HDR_ERRORSTATUS_EOL_ERROR = 8;
    RSDS_L_HDR_ERRORSTATUS_CALIBRATION_PARAMETER_ERROR = 9;
    RSDS_L_HDR_ERRORSTATUS_RESERVED = 10;
  }
  enum RSDSLBLINDType {
    RSDS_L_BLIND_NO_BLINDNESS = 0;
    RSDS_L_BLIND_BLINDNESS = 1;
    RSDS_L_BLIND_RESERVED = 2;
    RSDS_L_BLIND_RESERVED_3 = 3;
  }
  // description / [] [0|15] [initial_value:0]
  optional RSDSLhdrErrorStatusType rsds_l_hdr_error_status = 1;
  // description / [ms] [0|255] [initial_value:0]
  optional int32 rsds_l_hdr_time_stamp = 2;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_hdr_time_stamp_status = 3;
  // description / [] [0|3] [initial_value:0]
  optional RSDSLBLINDType rsds_l_blind = 4;
  // description / [] [0|63] [initial_value:0]
  optional int32 rsds_l_obj_detected = 5;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu_head_checksum1 = 6;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu_head_rolling_counter1 = 7;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_l_guardrail_confidence = 8;
  // description / [m] [-160|160] [initial_value:0]
  optional double rsds_l_guardrail_dx_end = 9;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu_head_checksum2 = 10;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu_head_rolling_counter2 = 11;
  // description / [m] [-30|30] [initial_value:0]
  optional double rsds_l_guardrail_c0 = 12;
  // description / [rad] [-2|2] [initial_value:0]
  optional double rsds_l_guardrail_c1 = 13;
  // description / [INV(m)] [-0.125|0.125] [initial_value:0]
  optional double rsds_l_guardrail_c2 = 14;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu_head_checksum3 = 15;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu_head_rolling_counter3 = 16;
  // description / [INV(m2)] [-0.004|0.004] [initial_value:0]
  optional double rsds_l_guardrail_c3 = 17;
  // description / [m] [-160|160] [initial_value:0]
  optional double rsds_l_guardrail_dx_start = 18;
  // description / [rad] [-2|2] [initial_value:0]
  optional double rsds_l_obj_azi_angle_calib = 19;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_l_sgu_head_checksum4 = 20;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu_head_rolling_counter4 = 21;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_l_sgu_head_block_counter = 22;
}

message RSDS_R_SGU_HEAD_5CF {
// Report Message
  enum RSDSRhdrErrorStatusType {
    RSDS_R_HDR_ERRORSTATUS_NO_ERROR = 0;
    RSDS_R_HDR_ERRORSTATUS_NOT_DEFINED = 1;
    RSDS_R_HDR_ERRORSTATUS_MISALIGNMENT_ELEVATION = 2;
    RSDS_R_HDR_ERRORSTATUS_MISALIGNMENT_AZIMUTH = 3;
    RSDS_R_HDR_ERRORSTATUS_TEMPORARY_FAILURE = 4;
    RSDS_R_HDR_ERRORSTATUS_PERMANENT_FAILURE = 5;
    RSDS_R_HDR_ERRORSTATUS_VOLTAGE_ERROR = 6;
    RSDS_R_HDR_ERRORSTATUS_PRODUCTION_MODE = 7;
    RSDS_R_HDR_ERRORSTATUS_EOL_ERROR = 8;
    RSDS_R_HDR_ERRORSTATUS_CALIBRATION_PARAMETER_ERROR = 9;
    RSDS_R_HDR_ERRORSTATUS_RESERVED = 10;
  }
  enum RSDSRBLINDType {
    RSDS_R_BLIND_NO_BLINDNESS = 0;
    RSDS_R_BLIND_BLINDNESS = 1;
    RSDS_R_BLIND_RESERVED = 2;
    RSDS_R_BLIND_RESERVED_3 = 3;
  }
  // description / [] [0|15] [initial_value:0]
  optional RSDSRhdrErrorStatusType rsds_r_hdr_error_status = 1;
  // description / [ms] [0|255] [initial_value:0]
  optional int32 rsds_r_hdr_time_stamp = 2;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_hdr_time_stamp_status = 3;
  // description / [] [0|3] [initial_value:0]
  optional RSDSRBLINDType rsds_r_blind = 4;
  // description / [] [0|63] [initial_value:0]
  optional int32 rsds_r_obj_detected = 5;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu_head_checksum1 = 6;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu_head_rolling_counter1 = 7;
  // description / [] [0|1] [initial_value:0]
  optional double rsds_r_guardrail_confidence = 8;
  // description / [m] [-160|160] [initial_value:0]
  optional double rsds_r_guardrail_dx_end = 9;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu_head_checksum2 = 10;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu_head_rolling_counter2 = 11;
  // description / [m] [-30|30] [initial_value:0]
  optional double rsds_r_guardrail_c0 = 12;
  // description / [rad] [-2|2] [initial_value:0]
  optional double rsds_r_guardrail_c1 = 13;
  // description / [INV(m)] [-0.125|0.125] [initial_value:0]
  optional double rsds_r_guardrail_c2 = 14;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu_head_checksum3 = 15;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu_head_rolling_counter3 = 16;
  // description / [INV(m2)] [-0.004|0.004] [initial_value:0]
  optional double rsds_r_guardrail_c3 = 17;
  // description / [m] [-160|160] [initial_value:0]
  optional double rsds_r_guardrail_dx_start = 18;
  // description / [rad] [-2|2] [initial_value:0]
  optional double rsds_r_obj_azi_angle_calib = 19;
  // description / [] [0|255] [initial_value:0]
  optional int32 rsds_r_sgu_head_checksum4 = 20;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu_head_rolling_counter4 = 21;
  // description / [] [0|15] [initial_value:0]
  optional int32 rsds_r_sgu_head_block_counter = 22;
}

message GwmP03ChassisDetail {
  optional ECM_FD2_103 ecm_fd2_103 = 1; // report message
  optional ECM1_111 ecm1_111 = 2; // report message
  optional VCU_FD2_11B vcu_fd2_11b = 3; // report message
  optional HUT44_127 hut44_127 = 4; // report message
  optional IFC_FD1_12B ifc_fd1_12b = 5; // control message
  optional IBC_FD1_12E ibc_fd1_12e = 6; // report message
  optional ESP_FD2_137 esp_fd2_137 = 7; // report message
  optional ESP_FD3_13B esp_fd3_13b = 8; // report message
  optional ACC_FD1_143 acc_fd1_143 = 9; // control message
  optional EPS_FD1_147 eps_fd1_147 = 10; // report message
  optional HAP_FD1_15B hap_fd1_15b = 11; // control message
  optional CR_FD1_15E cr_fd1_15e = 12; // control message
  optional CSA1_165 csa1_165 = 13; // report message
  optional AEB_FD1_18B aeb_fd1_18b = 14; // control message
  optional F_PBOX1_19B f_pbox1_19b = 15; // report message
  optional R_PBOX1_19C r_pbox1_19c = 16; // report message
  optional IFC_FD5_19F ifc_fd5_19f = 17; // control message
  optional GW_OTA_1D9 gw_ota_1d9 = 18; // report message
  optional HUT_FD4_1DA hut_fd4_1da = 19; // report message
  optional HUT30_1E9 hut30_1e9 = 20; // report message
  optional BLE2_1EA ble2_1ea = 21; // report message
  optional HUT33_1ED hut33_1ed = 22; // report message
  optional HUT15_1EE hut15_1ee = 23; // report message
  optional HUT_FD2_203 hut_fd2_203 = 24; // report message
  optional DCT5_221 dct5_221 = 25; // report message
  optional IFC_FD6_222 ifc_fd6_222 = 26; // control message
  optional AEB_FD2_227 aeb_fd2_227 = 27; // control message
  optional DCT7_235 dct7_235 = 28; // report message
  optional BCM12_238 bcm12_238 = 29; // report message
  optional GW1_239 gw1_239 = 30; // report message
  optional CSA5_23A csa5_23a = 31; // report message
  optional IFC_FD2_23D ifc_fd2_23d = 32; // control message
  optional HUT_FD3_243 hut_fd3_243 = 33; // report message
  optional CSA3_244 csa3_244 = 34; // report message
  optional ABM2_245 abm2_245 = 35; // report message
  optional DMS_FD1_24D dms_fd1_24d = 36; // report message
  optional CSA6_254 csa6_254 = 37; // report message
  optional BLE1_25C ble1_25c = 38; // report message
  optional HUT32_26F hut32_26f = 39; // report message
  optional HAP_FD2_274 hap_fd2_274 = 40; // control message
  optional IP2_27F ip2_27f = 41; // report message
  optional HAP_FD6_289 hap_fd6_289 = 42; // control message
  optional IP1_293 ip1_293 = 43; // report message
  optional PEPS2_295 peps2_295 = 44; // report message
  optional HAP_FD3_298 hap_fd3_298 = 45; // control message
  optional HAP_FD7_29B hap_fd7_29b = 46; // control message
  optional AC1_29D ac1_29d = 47; // report message
  optional BMS_FD2_29E bms_fd2_29e = 48; // report message
  optional BCM8_29F bcm8_29f = 49; // report message
  optional IFC_FD7_2A2 ifc_fd7_2a2 = 50; // control message
  optional ACC_FD2_2AB acc_fd2_2ab = 51; // control message
  optional DRDCM1_2B0 drdcm1_2b0 = 52; // report message
  optional ACC_FD3_2B4 acc_fd3_2b4 = 53; // control message
  optional ACC_FD4_2B8 acc_fd4_2b8 = 54; // control message
  optional GW_FD1_2BB gw_fd1_2bb = 55; // report message
  optional HUT_FD1_2C3 hut_fd1_2c3 = 56; // report message
  optional DDCM1_2CA ddcm1_2ca = 57; // report message
  optional PDCM1_2CD pdcm1_2cd = 58; // report message
  optional IFC_FD3_2CF ifc_fd3_2cf = 59; // control message
  optional PRDCM1_2D5 prdcm1_2d5 = 60; // report message
  optional VCU_FD4_2D6 vcu_fd4_2d6 = 61; // report message
  optional HUT23_2D8 hut23_2d8 = 62; // report message
  optional HUT22_2DA hut22_2da = 63; // report message
  optional HUT21_2DD hut21_2dd = 64; // report message
  optional HUT20_2DE hut20_2de = 65; // report message
  optional DVR_FD1_2EC dvr_fd1_2ec = 66; // report message
  optional BCM19_30F bcm19_30f = 67; // report message
  optional MDC_FD1_312 mdc_fd1_312 = 68; // control message
  optional BCM1_319 bcm1_319 = 69; // report message
  optional VCU5_31B vcu5_31b = 70; // report message
  optional OBC_FD2_31F obc_fd2_31f = 71; // report message
  optional T_BOX_FD6_33D t_box_fd6_33d = 72; // report message
  optional TPMS1_341 tpms1_341 = 73; // report message
  optional BCM3_345 bcm3_345 = 74; // report message
  optional HUT16_348 hut16_348 = 75; // report message
  optional ABM1_351 abm1_351 = 76; // report message
  optional HUT34_370 hut34_370 = 77; // report message
  optional AC2_385 ac2_385 = 78; // report message
  optional TPMS2_395 tpms2_395 = 79; // report message
  optional T_BOX_FD3_3E9 t_box_fd3_3e9 = 80; // report message
  optional HUT19_415 hut19_415 = 81; // report message
  optional HUT7_44 hut7_44 = 82; // report message
  optional HUT6_4A hut6_4a = 83; // report message
  optional CEM_NM_501 cem_nm_501 = 84; // report message
  optional IDC_NM_51C idc_nm_51c = 85; // report message
  optional HCU_FD1_60 hcu_fd1_60 = 86; // report message
  optional CSA2_A1 csa2_a1 = 87; // report message
  optional VCU_FD1_B5 vcu_fd1_b5 = 88; // report message
  optional ESP_FD1_BD esp_fd1_bd = 89; // report message
  optional SBWM1_C7 sbwm1_c7 = 90; // report message
  optional ACU1_F0 acu1_f0 = 91; // report message
  optional RSDS_FD1_16F rsds_fd1_16f = 92; // report message
  optional RSDS_FD2_30A rsds_fd2_30a = 93; // report message
  optional ADAS_AD1_470 adas_ad1_470 = 94; // report message
  optional RSDS_L_SGU1_580 rsds_l_sgu1_580 = 95; // report message
  optional RSDS_L_SGU2_581 rsds_l_sgu2_581 = 96; // report message
  optional RSDS_L_SGU3_582 rsds_l_sgu3_582 = 97; // report message
  optional RSDS_L_SGU4_583 rsds_l_sgu4_583 = 98; // report message
  optional RSDS_L_SGU5_584 rsds_l_sgu5_584 = 99; // report message
  optional RSDS_L_SGU6_585 rsds_l_sgu6_585 = 100; // report message
  optional RSDS_L_SGU7_586 rsds_l_sgu7_586 = 101; // report message
  optional RSDS_L_SGU8_587 rsds_l_sgu8_587 = 102; // report message
  optional RSDS_R_SGU1_590 rsds_r_sgu1_590 = 103; // report message
  optional RSDS_R_SGU2_591 rsds_r_sgu2_591 = 104; // report message
  optional RSDS_R_SGU3_592 rsds_r_sgu3_592 = 105; // report message
  optional RSDS_R_SGU4_593 rsds_r_sgu4_593 = 106; // report message
  optional RSDS_R_SGU5_594 rsds_r_sgu5_594 = 107; // report message
  optional RSDS_R_SGU6_595 rsds_r_sgu6_595 = 108; // report message
  optional RSDS_R_SGU7_596 rsds_r_sgu7_596 = 109; // report message
  optional RSDS_R_SGU8_597 rsds_r_sgu8_597 = 110; // report message
  optional RSDS_L_SGU_HEAD_5CE rsds_l_sgu_head_5ce = 111; // report message
  optional RSDS_R_SGU_HEAD_5CF rsds_r_sgu_head_5cf = 112; // report message
}
