syntax = "proto2";
package deeproute.canbus;

message VehicleCustomConfig {
    optional double steering_wheel_angle_min = 1 [default = -500];
    optional double steering_wheel_angle_max = 2 [default = 500];
    optional double steering_wheel_angle_rate_max = 3 [default = 500];
    optional double driving_longitudinal_cmd_min = 4 [default = -5];
    optional double driving_longitudinal_cmd_max = 5 [default = 5];
    optional double parking_longitudinal_cmd_min = 6 [default = -10];
    optional double parking_longitudinal_cmd_max = 7 [default = 2];
    optional double longitudinal_cmd_distance_min = 8 [default = 0];
    optional double longitudinal_cmd_distance_max = 9 [default = 1000];
    optional double throttle_cmd_min = 10 [default = 0];
    optional double throttle_cmd_max = 11 [default = 1];
    optional double brake_cmd_min = 12 [default = -5];
    optional double brake_cmd_max = 13 [default = 5];
}