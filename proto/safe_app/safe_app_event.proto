syntax = "proto2";

package dr.safe.app;

message SafeAppTagInfo {
  optional string function_name = 1;
  repeated string label_name = 2;
  optional string description = 3;
  repeated string user_name = 4;

  optional double lng = 5;
  optional double lat = 6;
  optional double alt = 7;

  enum CoordinateType {
    WGS84 = 0;
    GCJ02 = 1;
  }
  optional CoordinateType coordinate_type = 8 [default = WGS84];
}

message SafeAppCollectionTagInfo {
  optional string label_name = 1;
  optional double start_time = 2;
  optional double end_time = 3;
  optional string trip_name = 4;
}