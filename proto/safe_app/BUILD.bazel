package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "safe_app_event_proto",
    visibility = ["//visibility:public"],
    srcs = ["safe_app_event.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "safe_app_event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":safe_app_event_proto",
    ],
)

python_proto_library(
    name = "safe_app_event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":safe_app_event_proto",
    ],
)

