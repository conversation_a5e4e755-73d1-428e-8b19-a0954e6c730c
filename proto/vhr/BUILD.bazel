package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "event_extra_info_proto",
    visibility = ["//visibility:public"],
    srcs = ["event_extra_info.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "event_extra_info_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":event_extra_info_proto",
    ],
)

python_proto_library(
    name = "event_extra_info_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":event_extra_info_proto",
    ],
)

proto_library(
    name = "event_proto",
    visibility = ["//visibility:public"],
    srcs = ["event.proto"],
    deps = [
        "//proto/safety:safety_event_proto",
        "//proto/safety:vehicle_info_proto",
        "//proto/drapi:operation_status_proto",
        "//proto/vhr:event_extra_info_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "event_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":event_proto",
    ],
)

python_proto_library(
    name = "event_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":event_proto",
    ],
)

proto_library(
    name = "vhr_proto",
    visibility = ["//visibility:public"],
    srcs = ["vhr.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "vhr_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":vhr_proto",
    ],
)

python_proto_library(
    name = "vhr_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":vhr_proto",
    ],
)

