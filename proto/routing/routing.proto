syntax = "proto2";

package deeproute.routing;

import "common/header.proto";
import "common/geometry.proto";
import "common/error_code.proto";
import "semantic_map/map_parking_space.proto";
import "drdtu/dtu_data.proto";
import "semantic_map/map_standby_area.proto";

message LaneWaypoint {
  optional int32 id = 1;
  optional double s = 2;
  optional deeproute.common.PointENU pose = 3;
  optional double heading = 4;
}

message LaneSegment {
  optional int32 id = 1;
  optional float start_s = 2;
  optional float end_s = 3;
  optional float accumulate_s = 4;
}

message LaneSegmentXY {
  optional deeproute.common.PointENU point = 1;
  optional double heading = 2;
  optional float start_s = 3;
  optional float end_s = 4;
}

message RoutingRequest {
  optional deeproute.common.Header header = 1;
  // at least two points. The first is start point, the end is final point.
  // The routing must go through each point in waypoint.
  repeated LaneWaypoint waypoint = 2;
  repeated LaneSegment blacklisted_lane = 3;
  repeated string blacklisted_road = 4;
  optional bool broadcast = 5 [default = true];
  optional deeproute.hdmap.ParkingSpace parking_space = 6;  // deprecated
  repeated LaneSegmentXY blacklisted_lane_xy = 7;
  // use for vpa
  optional string map_id = 8;
  optional deeproute.hdmap.StandbyArea standby_area = 9;
}

message Measurement {
  optional double distance = 1;
}

enum ChangeLaneType {
  FORWARD = 0;
  LEFT = 1;
  RIGHT = 2;
};

message Passage {
  repeated LaneSegment segment = 1;
  optional bool can_exit = 2;
  optional ChangeLaneType change_lane_type = 3 [default = FORWARD];
  optional int32 priority = 4;
}

message RoadSegment {
  optional string id = 1;
  repeated Passage passage = 2;
}

message CenterPoint {
  optional deeproute.common.PointENU point = 1;
  optional int32 lane_id = 2;
}

message RoutingResponse {
  optional deeproute.common.Header header = 1;
  repeated RoadSegment road = 2;
  optional Measurement measurement = 3;
  optional RoutingRequest routing_request = 4;

  // the map version which is used to build road graph
  optional bytes map_version = 5;
  optional deeproute.common.StatusPb status = 6;
  // suggest using center_points instead
  repeated deeproute.common.PointENU centerpoints = 7 [deprecated = true];
  optional double routing_length = 8;
  // the index of routing response
  optional int32 routing_index = 9;
  // center points with lane id
  repeated CenterPoint center_points = 10;
}

message RoutingResponseList {
  optional deeproute.common.Header header = 1;
  repeated RoutingResponse routing_response_list = 2;
}

// topic routing/routing_request
message RoutingCMD {
  optional string cmd_id = 1;
  optional deeproute.dtu.data.Module source = 2;
  oneof Content {
    RoutingRequest routing_request = 3;
    RoutingResponse routing_response = 4;
  }
}

enum RoutingStatus {
  SUCCESS = 0;
  // following are error indicator
  WAYPOINTS_INVALIED = 1;
  DESTINATION_UNREACHABLE = 2;
  MAP_VERSION_MISMATCH = 3;
}

// topic routing/routing_response
message RoutingCMDRP {
  optional string cmd_id = 1;
  optional deeproute.dtu.data.Module source = 2;
  required RoutingStatus routing_status = 3;
  optional RoutingResponse routing_response = 4;
}