package(features=['-hidden_visibility_feature'])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

load("@com_github_grpc_grpc//bazel:cc_grpc_library.bzl", "cc_grpc_library")

proto_library(
    name = "request_proto",
    visibility = ["//visibility:public"],
    srcs = ["request.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "request_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":request_proto",
    ],
)

python_proto_library(
    name = "request_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":request_proto",
    ],
)

proto_library(
    name = "emergency_stop_proto",
    visibility = ["//visibility:public"],
    srcs = ["emergency_stop.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "emergency_stop_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":emergency_stop_proto",
    ],
)

python_proto_library(
    name = "emergency_stop_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":emergency_stop_proto",
    ],
)

proto_library(
    name = "notify_control_proto",
    visibility = ["//visibility:public"],
    srcs = ["notify_control.proto"],
    deps = [
        "//proto/canbus:chassis_proto",
        "//proto/common:header_proto",
        "//proto/common/vehicle_state:vehicle_state_proto",
        "//proto/common:vehicle_signal_proto",
        "//proto/common:error_code_proto",
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "notify_control_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":notify_control_proto",
    ],
)

python_proto_library(
    name = "notify_control_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":notify_control_proto",
    ],
)

proto_library(
    name = "mode_state_proto",
    visibility = ["//visibility:public"],
    srcs = ["mode_state.proto"],
    deps = [
    ],
    strip_import_prefix = "/proto/",
)

cc_proto_library(
    name = "mode_state_proto_cc",
    visibility = ["//visibility:public"],
    deps = [
        ":mode_state_proto",
    ],
)

python_proto_library(
    name = "mode_state_proto_py",
    visibility = ["//visibility:public"],
    protos = [
        ":mode_state_proto",
    ],
)

