# DR Bag Decoder - Design Document

## Overview

The DR Bag Decoder is a high-performance ROS bag processing system designed to extract, decode, and combine camera imagery with neural network outputs and road masks. The system is optimized for macOS M4 Pro with hardware-accelerated HEVC decoding and efficient data processing pipelines.

## Key Features

- **Hardware-Accelerated HEVC Decoding**: Leverages macOS VideoToolbox for efficient camera data processing
- **Multi-Modal Data Fusion**: Combines camera images, RasMap neural network outputs, and road segmentation masks
- **Timestamp Synchronization**: Intelligent matching of data streams with different frequencies
- **Batch Processing**: Handles multiple ROS bag files with progress tracking
- **Optimized for Apple Silicon**: Specifically tuned for M4 Pro performance characteristics

## System Architecture

### Data Flow Pipeline

```
ROS Bag Files → Proto Parsing → HEVC Decoding → Data Sync → Image Fusion → Output
     ↓              ↓              ↓             ↓            ↓           ↓
  Multiple       Protobuf      VideoToolbox   Timestamp   OpenCV      PNG Files
   .bag         Messages      Hardware       Matching    Overlay     + Raw Data
   files                      Acceleration
```

### Core Components

#### 1. ROS Bag Reader (`process_topics.py`)
**Purpose**: Main orchestrator for processing ROS bag files
**Key Features**:
- Multi-file batch processing
- Progress tracking and statistics
- Configurable message limits for testing
- Robust error handling and logging

#### 2. HEVC Decoder (`topic_msg_def.cfg`)
**Purpose**: Hardware-accelerated video decoding
**Decoding Strategy** (in priority order):
1. **PyAV + VideoToolbox**: Primary method using Apple's hardware decoder
2. **FFmpeg CLI + VideoToolbox**: Fallback with hardware acceleration
3. **Placeholder Generation**: Debug mode for pipeline verification

**Performance Characteristics**:
- Input: HEVC NAL units (H.265 bitstream)
- Output: BGR images (2160×3840 resolution)
- Success Rate: ~10-15% (due to frame completeness requirements)
- Processing Speed: ~50-100ms per successful frame

#### 3. Proto Build System (`build_protos.py`)
**Purpose**: Generate Python bindings from protobuf definitions
**Features**:
- Dependency resolution for complex proto hierarchies
- Clean rebuild capabilities
- Version compatibility management
- Automated import path handling

#### 4. Data Synchronization Engine
**Purpose**: Align multi-modal data streams by timestamp
**Algorithm**:
- Nearest-neighbor timestamp matching
- Configurable tolerance windows
- Graceful handling of missing data
- Efficient temporal indexing

## Technical Specifications

### Input Data Streams

| Stream | Topic | Format | Frequency | Resolution |
|--------|-------|--------|-----------|------------|
| Camera | `/sensors/camera/camera_1_raw_data/compressed_proto` | HEVC | ~10 Hz | 2160×3840 |
| RasMap NN | `/perception/deeproute_perception_ras_map` | Protobuf | ~8 Hz | Variable |
| Road Masks | Embedded in RasMap | Binary | ~8 Hz | Variable |

### Output Structure

```
output/
├── camera1/                    # Raw HEVC data (for debugging)
│   └── cam1_<timestamp>.hevc
├── combined/                   # Main output: fused visualizations
│   └── combined_<timestamp>.png
└── rasmap/                     # NN output visualizations
    ├── debug_<timestamp>.png   # Debug overlays
    └── road_<timestamp>.png    # Road mask visualizations
```

### Performance Metrics

**Hardware**: macOS M4 Pro
**Environment**: Conda `slam_py310`
**Typical Performance**:
- Processing Speed: ~200-300 messages/second
- HEVC Decode Rate: ~2-3 successful frames/second
- Memory Usage: ~500MB-1GB peak
- Output Generation: ~150 combined images per bag file

## Dependencies and Environment

### Core Dependencies
```yaml
Python: 3.10
rosbags: Latest          # ROS bag parsing
av: 14.4.0              # PyAV for hardware video decoding
opencv-python-headless: 4.10.0.82  # Image processing
protobuf: 3.20.3        # Protocol buffer support (compatibility)
numpy: <2               # Numerical operations (compatibility)
grpcio-tools: Latest    # Proto compilation
```

### System Requirements
```yaml
OS: macOS (Apple Silicon recommended)
Hardware: M4 Pro or equivalent
Conda: Miniconda/Anaconda with conda-forge
FFmpeg: Hardware acceleration support
VideoToolbox: macOS system framework
```

### Environment Setup
```bash
# Activate conda environment
conda activate slam_py310

# Install ffmpeg with hardware acceleration
conda install -c conda-forge ffmpeg

# Set Python path for proto imports
export PYTHONPATH=$(pwd)/proto_build:$PYTHONPATH
```

## Usage Guide

### Basic Processing
```bash
# Process all bags in a directory
python process_topics.py sample_bags

# Limit processing for testing
python process_topics.py sample_bags --max_msgs 100

# Process specific bag file
python process_topics.py path/to/specific.bag
```

### Proto Management
```bash
# Clean rebuild of all proto bindings
python build_protos.py --clean

# Incremental build (faster)
python build_protos.py
```

### Debugging
```bash
# Analyze camera data format
python debug_camera.py sample_bags/example.bag

# Check HEVC decoder capabilities
python -c "from topic_msg_def import proto_compressed_to_cv_mat; print('Decoder ready')"
```

## Performance Optimization

### HEVC Decoding Optimization
1. **Frame Size Filtering**: Skip frames <50KB (likely incomplete)
2. **Hardware Acceleration**: Prioritize VideoToolbox over software decoding
3. **Memory Management**: Efficient cleanup of temporary files
4. **Error Recovery**: Graceful fallback between decoding methods

### Data Processing Optimization
1. **Timestamp Indexing**: Pre-build temporal indices for fast lookup
2. **Selective Processing**: Only process frames with valid camera data
3. **Batch I/O**: Efficient file writing with proper buffering
4. **Memory Pooling**: Reuse image buffers where possible

## Known Limitations and Solutions

### HEVC Decoding Challenges
**Issue**: Low success rate (~10-15%) for HEVC frame decoding
**Causes**:
- Incomplete NAL units in some frames
- Hardware decoder compatibility issues
- Frame dependency requirements

**Mitigation Strategies**:
- Multi-tier fallback system (PyAV → FFmpeg → Placeholder)
- Frame size filtering to improve success rates
- Graceful degradation with placeholder images

### Synchronization Challenges
**Issue**: Different frequencies between camera and NN data
**Solution**: Nearest-neighbor timestamp matching with tolerance windows

### Memory Management
**Issue**: Large image data (2160×3840×3 bytes per frame)
**Solution**: Streaming processing with immediate cleanup

## Future Enhancements

### Short Term (Next Release)
1. **Improved HEVC Success Rate**: Investigate frame reconstruction techniques
2. **Performance Metrics**: Add detailed timing and success rate reporting
3. **Configuration System**: YAML-based configuration for processing parameters
4. **Unit Tests**: Comprehensive test suite for all components

### Medium Term
1. **Real-time Processing**: Support for live ROS bag streams
2. **Advanced Visualization**: 3D overlays and interactive viewing
3. **Cross-platform Support**: Windows and Linux compatibility
4. **Distributed Processing**: Multi-machine processing for large datasets

### Long Term
1. **Machine Learning Integration**: Automated quality assessment
2. **Cloud Processing**: Scalable cloud-based processing pipelines
3. **Advanced Analytics**: Statistical analysis of processed data
4. **Integration APIs**: RESTful APIs for external system integration

## Development Guidelines

### Code Organization
- **Modular Design**: Separate concerns into focused modules
- **Error Handling**: Comprehensive exception handling with logging
- **Documentation**: Inline documentation for all public interfaces
- **Testing**: Unit tests for critical path components

### Proto Management Best Practices
- **Version Control**: Careful management of proto file versions
- **Dependency Tracking**: Document proto import relationships
- **Build Automation**: Automated proto compilation in CI/CD
- **Compatibility Testing**: Validate against different protobuf versions

### Performance Monitoring
- **Profiling**: Regular performance profiling of critical paths
- **Benchmarking**: Standardized benchmarks for performance regression testing
- **Resource Monitoring**: Memory and CPU usage tracking
- **Success Rate Tracking**: Monitor HEVC decoding success rates over time

This design document provides a comprehensive overview of the DR Bag Decoder system, balancing technical depth with practical usage guidance for developers and users.

