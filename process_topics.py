#!/usr/bin/env python3
"""Extract and combine rasmap-NN, road mask, and camera images from ROS1 bag files.

Prerequisites:
    python build_protos.py --clean
    export PYTHONPATH=$(pwd)/proto_build:$PYTHONPATH

Usage:
    python process_topics.py /path/to/bag/directory

Output:
    output/combined/combined_<rasmap_ts>.png
    
Each combined image contains:
    - Left: Most recent valid camera image (including HEVC decoded with ffmpeg)
    - Middle: Rasmap NN debug visualization (boundary/centerline/curb)
    - Right: Road mask confidence visualization

Logic: 
- Processes ALL messages by default (use --max_msgs to limit)
- Processes all .bag files in the specified directory
- For each rasmap+road mask pair, uses the most recent valid camera image
- Skips rasmap processing until at least one valid camera image is found
- HEVC camera data is decoded using ffmpeg for visualization
"""
from __future__ import annotations

import argparse
import struct
import sys
import re
from pathlib import Path
from typing import List, Tuple

import cv2
import numpy as np
import snappy  # used inside topic_cfg helpers
from google.protobuf.message import DecodeError
from rosbags.rosbag1 import Reader
# 
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from map.sd_map_pb2 import SDMapOnboard, SDMap
from perception.perception_nn_pb2 import NnFrame
from common.geometry_pb2 import PointLLH, Point2D
from lock_on_road.lock_on_road_pb2 import LockOnRoadResult
from drivers.sensor_image_pb2 import CompressedImage


# ---------------------------------------------------------------------------
# Add proto_build to path
PROTO_BUILD_DIR = Path("proto_build").resolve()
if PROTO_BUILD_DIR.exists():
    sys.path.insert(0, str(PROTO_BUILD_DIR))

# ---------------------------------------------------------------------------
# Load helpers from msg_processor.py
CFG_PATH = Path("msg_processor.py")
if not CFG_PATH.exists():
    raise SystemExit("msg_processor.py not found.")

import types
cfg_mod = types.ModuleType("topic_cfg")

# Execute only the definitions, skip the demo loop
cfg_lines: list[str] = []
for line in CFG_PATH.read_text().splitlines():
    if line.strip().startswith("elif topic"):
        break  # skip sample code
    cfg_lines.append(line)
exec("\n".join(cfg_lines), cfg_mod.__dict__)

# Inject required modules
import numpy as _np
for name, mod in {"np": _np, "cv2": cv2, "snappy": snappy}.items():
    cfg_mod.__dict__.setdefault(name, mod)

# Extract topic names and helper functions
RASMAP_TOPIC: str = cfg_mod.rasmap_topic  # type: ignore[attr-defined]
CAM_TOPIC: str = cfg_mod.camera_1_topic  # type: ignore[attr-defined]
GNSS_TOPIC: str = cfg_mod.gnss_pose_topic
strip_header = cfg_mod.strip_header  # type: ignore[attr-defined]
proto_compressed_to_cv_mat = cfg_mod.proto_compressed_to_cv_mat  # type: ignore[attr-defined]
process_rasmap_nn = cfg_mod.process_rasmap_nn  # type: ignore[attr-defined]

# Import protobuf classes
try:
    from deeproute.perception import perception_nn_pb2 as perception_nn_pb2  # type: ignore
except ModuleNotFoundError:
    from perception import perception_nn_pb2 as perception_nn_pb2  # type: ignore
try:
    from deeproute.drivers import sensor_image_pb2 as sensor_image_pb2  # type: ignore
except ModuleNotFoundError:
    from drivers import sensor_image_pb2 as sensor_image_pb2  # type: ignore

NnFrame = perception_nn_pb2.NnFrame
CompressedImage = sensor_image_pb2.CompressedImage

# ---------------------------------------------------------------------------
# CLI
def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Extract rasmap and camera images from ROS bags")
    p.add_argument("bag_dir", type=Path, help="directory containing .bag files to process")
    p.add_argument("--max_msgs", type=int, default=0, help="max messages per topic (0 = unlimited, default: process all)")
    p.add_argument("--out", type=Path, default=Path("output"), help="output directory")
    p.add_argument("--generate_video", action="store_true", help="generate MP4 video from combined images")
    p.add_argument("--video_fps", type=float, default=10.0, help="video frame rate (default: 10.0)")
    p.add_argument("--video_name", type=str, default="combined_video.mp4", help="output video filename (default: combined_video.mp4)")
    return p.parse_args()

# ---------------------------------------------------------------------------
# Utilities
def ensure_output_dirs(root: Path):
    (root / "combined").mkdir(parents=True, exist_ok=True)

def ros_string_payload(raw: bytes) -> bytes:
    """Extract payload from std_msgs/String raw buffer."""
    if len(raw) < 4:
        raise ValueError("String buffer too short")
    length = struct.unpack("<I", raw[:4])[0]
    return raw[4 : 4 + length]



def resize_to_height(img: np.ndarray, target_height: int) -> np.ndarray:
    """Resize image maintaining aspect ratio to target height."""
    if img is None:
        return None
    h, w = img.shape[:2]
    if h == target_height:
        return img
    ratio = target_height / h
    new_width = int(w * ratio)
    return cv2.resize(img, (new_width, target_height))

def create_combined_images(images_by_timestamp: dict, output_dir: Path) -> None:
    """Create combined debug images from camera, rasmap debug, and road mask."""
    combined_count = 0
    
    # Separate timestamps with data
    camera_timestamps = [ts for ts, imgs in images_by_timestamp.items() if imgs.get('camera') is not None]
    rasmap_timestamps = [ts for ts, imgs in images_by_timestamp.items() if imgs.get('debug') is not None and imgs.get('road') is not None]
    
    print(f"Found {len(camera_timestamps)} camera timestamps and {len(rasmap_timestamps)} rasmap timestamps")
    
    # Match camera with closest rasmap timestamp (within 0.5 seconds)
    for cam_ts in camera_timestamps:
        cam_time = float(cam_ts.split('_')[0]) + float(cam_ts.split('_')[1]) / 1e9
        
        closest_rasmap_ts = None
        min_time_diff = float('inf')
        
        for rasmap_ts in rasmap_timestamps:
            rasmap_time = float(rasmap_ts.split('_')[0]) + float(rasmap_ts.split('_')[1]) / 1e9
            time_diff = abs(cam_time - rasmap_time)
            
            if time_diff < 0.5 and time_diff < min_time_diff:  # Within 0.5 seconds
                min_time_diff = time_diff
                closest_rasmap_ts = rasmap_ts
        
        if closest_rasmap_ts is not None:
            camera = images_by_timestamp[cam_ts]['camera']
            debug = images_by_timestamp[closest_rasmap_ts]['debug']
            road = images_by_timestamp[closest_rasmap_ts]['road']
            
            # Resize all images to the same height (use camera height as reference)
            target_height = camera.shape[0]
            camera_resized = camera
            debug_resized = resize_to_height(debug, target_height)
            road_resized = resize_to_height(road, target_height)
            
            # Combine horizontally: camera | debug | road
            combined = np.hstack([camera_resized, debug_resized, road_resized])
            
            # Save combined image
            output_path = output_dir / "combined" / f"combined_{cam_ts}_matched_{closest_rasmap_ts}.png"
            cv2.imwrite(str(output_path), combined)
            combined_count += 1
            print(f"Created combined debug image: camera {cam_ts} + rasmap {closest_rasmap_ts} (diff: {min_time_diff:.3f}s)")
    
    print(f"Total combined images created: {combined_count}")

# ---------------------------------------------------------------------------
# Main
def main() -> None:
    args = parse_args()
    
    # Validate bag directory
    if not args.bag_dir.is_dir():
        raise SystemExit(f"Error: {args.bag_dir} is not a directory")
    
    bag_files = list(args.bag_dir.glob("*.bag"))
    if not bag_files:
        raise SystemExit(f"Error: No .bag files found in {args.bag_dir}")
    
    print(f"Found {len(bag_files)} bag files in {args.bag_dir}")
    
    ensure_output_dirs(args.out)

    saved_rasmap = saved_cam = 0
    
    # Store the last camera image to use with rasmap data
    last_camera_image = None
    combined_count = 0

    for bag_file in sorted(bag_files):
        print(f"Processing {bag_file} …")
        with Reader(bag_file) as reader:
            target_conns = [c for c in reader.connections if c.topic in (RASMAP_TOPIC, CAM_TOPIC)]
            
            for conn, stamp, raw in reader.messages(target_conns):
                topic = conn.topic
                
                # Extract and clean proto bytes
                try:
                    payload = ros_string_payload(raw)
                    proto_bytes = strip_header(payload)
                except Exception as exc:
                    print(f"Header strip failed for {topic}: {exc}")
                    continue

                # stamp is an int nanoseconds, convert to secs/nsecs
                secs = stamp // 1_000_000_000
                nsecs = stamp % 1_000_000_000
                ts = f"{secs}_{nsecs:09d}"
                
                # Process camera messages - update last camera image
                if topic == CAM_TOPIC:
                    try:
                        img_msg = CompressedImage()
                        img_msg.ParseFromString(proto_bytes)
                        img = proto_compressed_to_cv_mat(img_msg)
                        
                        if img is None:
                            print(f"Failed to decode camera image at timestamp {ts}")
                            continue
                        elif img.size == 0:
                            print(f"Empty camera image at timestamp {ts}")
                            continue
                        else:
                            # Update with decoded camera image
                            last_camera_image = img
                            saved_cam += 1
                            print(f"Updated camera image from timestamp {ts} (shape: {img.shape})")
                            
                    except Exception as exc:
                        print(f"Camera decode error: {exc}")
                elif topic == GNSS_TOPIC:
                    try:
                        ins_msg = Ins()
                        ins_msg.ParseFromString(proto_bytes)
                        print(f"GNSS message at timestamp {ts}")
                    except Exception as exc:
                        print(f"GNSS decode error: {exc}")
                        continue
                # Process rasmap messages - combine with last camera image
                elif topic == RASMAP_TOPIC:
                    try:
                        frame = NnFrame()
                        frame.ParseFromString(proto_bytes)
                        debug_img, road_img, _ = process_rasmap_nn(frame)
                        
                        if debug_img is None or road_img is None:
                            print(f"Empty rasmap images at timestamp {ts}")
                            continue
                            
                        # Create combined image with last camera image
                        if last_camera_image is not None:
                            # Resize all images to the same height (use camera height as reference)
                            target_height = last_camera_image.shape[0]
                            camera_resized = last_camera_image
                            debug_resized = resize_to_height(debug_img, target_height)
                            road_resized = resize_to_height(road_img, target_height)
                            
                            # Combine horizontally: camera | debug | road
                            combined = np.hstack([camera_resized, debug_resized, road_resized])
                            
                            # Save combined image
                            output_path = args.out / "combined" / f"combined_{ts}.png"
                            cv2.imwrite(str(output_path), combined)
                            combined_count += 1
                            print(f"Created combined debug image for rasmap timestamp {ts}")
                        else:
                            print(f"No valid camera image available yet for rasmap timestamp {ts} - skipping")
                            
                        saved_rasmap += 1
                        
                    except Exception as exc:
                        print(f"Rasmap decode error: {exc}")

    print(f"\nDone! Extracted {saved_rasmap:,} rasmap images and {saved_cam:,} camera images")
    print(f"Combined images saved: {combined_count:,}")
    print(f"Processed {len(bag_files)} bag files from {args.bag_dir}")
    print(f"Output location: {args.out}")

    # Generate video if requested
    if args.generate_video and combined_count > 0:
        print(f"\nGenerating video...")
        combined_dir = args.out / "combined"
        video_path = args.out / args.video_name

        success = create_video_from_images(combined_dir, video_path, args.video_fps)
        if success:
            print(f"Video saved to: {video_path}")
        else:
            print("Video generation failed!")
    elif args.generate_video and combined_count == 0:
        print("\nNo combined images available for video generation!")


def extract_timestamp_from_filename(filename: str) -> int:
    """Extract timestamp from filename like 'combined_1749699903_058615886.png'"""
    match = re.search(r'combined_(\d+)_(\d+)\.png', filename)
    if match:
        # Combine seconds and nanoseconds for sorting
        seconds = int(match.group(1))
        nanoseconds = int(match.group(2))
        return seconds * 1000000000 + nanoseconds
    return 0


def get_sorted_combined_images(image_dir: Path) -> List[Path]:
    """Get all combined images sorted by timestamp."""
    image_files = []

    for img_file in image_dir.glob("combined_*.png"):
        timestamp = extract_timestamp_from_filename(img_file.name)
        if timestamp > 0:
            image_files.append((timestamp, img_file))

    # Sort by timestamp
    image_files.sort(key=lambda x: x[0])
    return [img_path for _, img_path in image_files]


def get_video_dimensions_from_images(image_files: List[Path]) -> Tuple[int, int]:
    """Get video dimensions from the first image."""
    if not image_files:
        return 1920, 1080  # Default resolution

    first_image = cv2.imread(str(image_files[0]))
    if first_image is None:
        return 1920, 1080

    height, width = first_image.shape[:2]
    return width, height


def create_video_from_images(image_dir: Path, output_path: Path, fps: float = 10.0) -> bool:
    """
    Create MP4 video from combined images.

    Args:
        image_dir: Directory containing combined images
        output_path: Output video file path
        fps: Frames per second for the video

    Returns:
        True if successful, False otherwise
    """
    print(f"Creating video from images in {image_dir}")

    # Get sorted image files
    image_files = get_sorted_combined_images(image_dir)
    if not image_files:
        print("No combined images found for video generation!")
        return False

    print(f"Found {len(image_files)} images for video")

    # Get video dimensions
    width, height = get_video_dimensions_from_images(image_files)
    print(f"Video resolution: {width}x{height}, FPS: {fps}")

    # Set up video codec
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')

    # Create video writer
    video_writer = cv2.VideoWriter(
        str(output_path),
        fourcc,
        fps,
        (width, height)
    )

    if not video_writer.isOpened():
        print("Failed to create video writer!")
        return False

    # Process images
    print("Processing images for video...")
    for i, img_path in enumerate(image_files):
        if i % 20 == 0:
            print(f"   Video progress: {i+1}/{len(image_files)} ({(i+1)/len(image_files)*100:.1f}%)")

        # Read image
        img = cv2.imread(str(img_path))
        if img is None:
            print(f"Warning: Could not read {img_path}")
            continue

        # Resize if necessary
        if img.shape[1] != width or img.shape[0] != height:
            img = cv2.resize(img, (width, height))

        # Write frame
        video_writer.write(img)

    # Release video writer
    video_writer.release()

    print(f"Video created successfully: {output_path}")
    print(f"Video duration: {len(image_files)/fps:.1f} seconds ({len(image_files)} frames)")

    return True


if __name__ == "__main__":
    main()